{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\patterns\\\\BinarySearchPattern.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction BinarySearchPattern() {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 3, 5, 7, 9, 11, 13, 15, 17, 19]);\n  const [target, setTarget] = useState(11);\n  const [left, setLeft] = useState(0);\n  const [right, setRight] = useState(9);\n  const [mid, setMid] = useState(4);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n  const [searchHistory, setSearchHistory] = useState([]);\n  const steps = [{\n    title: \"🔍 Understanding Binary Search Pattern\",\n    content: \"Binary Search efficiently finds elements in sorted arrays by repeatedly dividing the search space in half, achieving O(log n) time complexity.\",\n    code: `// Binary Search Template\nfunction binarySearch(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            return mid;  // Found target\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return -1;  // Target not found\n}`,\n    learningPrompt: \"Why does binary search require a sorted array?\",\n    answer: \"Binary search relies on the property that if the middle element is less than the target, all elements to the left are also less than the target, allowing us to eliminate half the search space.\"\n  }, {\n    title: \"📊 Step 1: Identify Binary Search Problems\",\n    content: \"Recognize when to use binary search: sorted arrays, search spaces, finding boundaries, or optimization problems.\",\n    code: `// Problem Types for Binary Search:\n\n// 1. Direct Search in Sorted Array\nfunction searchInsert(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        if (nums[mid] === target) return mid;\n        else if (nums[mid] < target) left = mid + 1;\n        else right = mid - 1;\n    }\n    \n    return left; // Insert position\n}\n\n// 2. Search in Rotated Array\nfunction searchRotated(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] === target) return mid;\n        \n        // Check which half is sorted\n        if (nums[left] <= nums[mid]) {\n            // Left half is sorted\n            if (nums[left] <= target && target < nums[mid]) {\n                right = mid - 1;\n            } else {\n                left = mid + 1;\n            }\n        } else {\n            // Right half is sorted\n            if (nums[mid] < target && target <= nums[right]) {\n                left = mid + 1;\n            } else {\n                right = mid - 1;\n            }\n        }\n    }\n    \n    return -1;\n}\n\n// 3. Find Peak Element\nfunction findPeakElement(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[mid + 1]) {\n            right = mid; // Peak is in left half\n        } else {\n            left = mid + 1; // Peak is in right half\n        }\n    }\n    \n    return left;\n}`,\n    learningPrompt: \"What are the key indicators that a problem can use binary search?\",\n    answer: \"1) Sorted data, 2) Search space that can be divided, 3) Monotonic function (if condition is true at position x, it's true for all positions in one direction), 4) Finding boundaries or optimal values.\"\n  }, {\n    title: \"🎯 Step 2: Set Up Search Boundaries\",\n    content: \"Initialize left and right pointers to define the search space correctly.\",\n    code: `// Step 2: Boundary Setup Patterns\n\n// Pattern 1: Standard Array Search\nlet left = 0;\nlet right = arr.length - 1;\n\n// Pattern 2: Search Insert Position\nlet left = 0;\nlet right = arr.length; // Note: length, not length-1\n\n// Pattern 3: Search in Range [min, max]\nlet left = minValue;\nlet right = maxValue;\n\n// Pattern 4: Search for First/Last Occurrence\nfunction findFirst(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            right = mid - 1; // Continue searching left for first occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}\n\nfunction findLast(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            left = mid + 1; // Continue searching right for last occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}`,\n    learningPrompt: \"Why might we use different boundary setups?\",\n    answer: \"Different problems require different search spaces: exact search uses [0, n-1], insert position uses [0, n], and range search uses [min, max] values.\"\n  }, {\n    title: \"⚡ Step 3: Calculate Mid and Compare\",\n    content: \"Calculate the middle point and make comparisons to decide which half to search next.\",\n    code: `// Step 3: Mid Calculation and Comparison Logic\n\nfunction binarySearchWithSteps(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    let steps = [];\n    \n    while (left <= right) {\n        // Calculate mid (avoid overflow)\n        let mid = Math.floor((left + right) / 2);\n        // Alternative: let mid = left + Math.floor((right - left) / 2);\n        \n        steps.push({\n            left: left,\n            right: right,\n            mid: mid,\n            midValue: arr[mid],\n            comparison: arr[mid] === target ? 'equal' : \n                       arr[mid] < target ? 'less' : 'greater'\n        });\n        \n        if (arr[mid] === target) {\n            return { found: true, index: mid, steps: steps };\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return { found: false, index: -1, steps: steps };\n}\n\n// Avoid integer overflow (important in some languages)\nfunction safeMidCalculation(left, right) {\n    // Instead of: (left + right) / 2\n    // Use: left + (right - left) / 2\n    return left + Math.floor((right - left) / 2);\n}`,\n    learningPrompt: \"Why do we use Math.floor for mid calculation?\",\n    answer: \"Math.floor ensures we get an integer index. For arrays with even length, we consistently choose the left-middle element, preventing infinite loops.\"\n  }, {\n    title: \"🎮 Step 4: Interactive Demo\",\n    content: \"Watch binary search in action as it efficiently narrows down the search space.\",\n    code: `// Live Demo: Binary Search\nconst arr = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];\nconst target = 11;\n\n// Current search space:\n// Left: ${left} (value: ${demoArray[left]})\n// Right: ${right} (value: ${demoArray[right]})\n// Mid: ${mid} (value: ${demoArray[mid]})\n// Comparison: ${demoArray[mid]} vs ${target}`,\n    learningPrompt: \"How many comparisons does binary search need in the worst case?\",\n    answer: \"At most ⌊log₂(n)⌋ + 1 comparisons, where n is the array size. For 1000 elements, that's only about 10 comparisons!\"\n  }, {\n    title: \"🔧 Step 5: Advanced Binary Search Patterns\",\n    content: \"Master advanced patterns like finding boundaries, searching in 2D matrices, and optimization problems.\",\n    code: `// Advanced Pattern 1: Search in 2D Matrix\nfunction searchMatrix(matrix, target) {\n    if (!matrix.length || !matrix[0].length) return false;\n    \n    let rows = matrix.length;\n    let cols = matrix[0].length;\n    let left = 0;\n    let right = rows * cols - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        let midValue = matrix[Math.floor(mid / cols)][mid % cols];\n        \n        if (midValue === target) {\n            return true;\n        } else if (midValue < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return false;\n}\n\n// Advanced Pattern 2: Find Minimum in Rotated Array\nfunction findMin(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[right]) {\n            // Minimum is in right half\n            left = mid + 1;\n        } else {\n            // Minimum is in left half (including mid)\n            right = mid;\n        }\n    }\n    \n    return nums[left];\n}\n\n// Advanced Pattern 3: Binary Search on Answer\nfunction minEatingSpeed(piles, h) {\n    let left = 1;\n    let right = Math.max(...piles);\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        let hours = 0;\n        \n        for (let pile of piles) {\n            hours += Math.ceil(pile / mid);\n        }\n        \n        if (hours <= h) {\n            right = mid; // Can eat slower\n        } else {\n            left = mid + 1; // Need to eat faster\n        }\n    }\n    \n    return left;\n}`,\n    learningPrompt: \"What's the key insight in 'binary search on answer' problems?\",\n    answer: \"Instead of searching in an array, we search in the range of possible answers. If a speed/capacity works, all higher values also work (monotonic property).\"\n  }];\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    setSearchHistory([]);\n    let l = 0;\n    let r = demoArray.length - 1;\n    let history = [];\n    while (l <= r) {\n      let m = Math.floor((l + r) / 2);\n      setLeft(l);\n      setRight(r);\n      setMid(m);\n      const step = {\n        left: l,\n        right: r,\n        mid: m,\n        midValue: demoArray[m],\n        comparison: demoArray[m] === target ? 'equal' : demoArray[m] < target ? 'less' : 'greater'\n      };\n      history.push(step);\n      setSearchHistory([...history]);\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      if (demoArray[m] === target) {\n        setResult(`Found ${target} at index ${m}!`);\n        setIsRunning(false);\n        return;\n      } else if (demoArray[m] < target) {\n        l = m + 1;\n      } else {\n        r = m - 1;\n      }\n    }\n    setResult(`${target} not found in array`);\n    setIsRunning(false);\n  };\n  const resetDemo = () => {\n    setLeft(0);\n    setRight(demoArray.length - 1);\n    setMid(Math.floor((demoArray.length - 1) / 2));\n    setResult(null);\n    setSearchHistory([]);\n    setIsRunning(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    style: {\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#9b59b6',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83D\\uDD0D Binary Search Pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em',\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: \"Master binary search to achieve O(log n) solutions for search and optimization problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#9b59b6',\n            height: '100%',\n            width: `${(currentStep + 1) / steps.length * 100}%`,\n            transition: 'width 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0',\n          color: '#7f8c8d'\n        },\n        children: [\"Step \", currentStep + 1, \" of \", steps.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '10px',\n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      },\n      children: steps.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentStep(index),\n        style: {\n          padding: '8px 16px',\n          border: currentStep === index ? '2px solid #9b59b6' : '1px solid #ddd',\n          borderRadius: '20px',\n          backgroundColor: currentStep === index ? '#9b59b6' : 'white',\n          color: currentStep === index ? 'white' : '#333',\n          cursor: 'pointer',\n          fontSize: '14px'\n        },\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#2c3e50',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          lineHeight: '1.6',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            margin: 0,\n            whiteSpace: 'pre-wrap'\n          },\n          children: steps[currentStep].code\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f0f8ff',\n          border: '2px solid #9b59b6',\n          borderRadius: '12px',\n          padding: '20px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#9b59b6',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83C\\uDFAE Live Demo: Binary Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '3px',\n              justifyContent: 'center',\n              marginBottom: '10px',\n              flexWrap: 'wrap'\n            },\n            children: demoArray.map((num, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '35px',\n                height: '35px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: index === mid ? '#e74c3c' : index === left ? '#27ae60' : index === right ? '#3498db' : index >= left && index <= right ? '#f39c12' : '#ecf0f1',\n                color: index >= left && index <= right ? 'white' : '#333',\n                borderRadius: '6px',\n                fontWeight: 'bold',\n                fontSize: '12px',\n                border: '2px solid',\n                borderColor: index === mid ? '#c0392b' : index === left ? '#229954' : index === right ? '#2980b9' : index >= left && index <= right ? '#e67e22' : '#bdc3c7',\n                position: 'relative'\n              },\n              children: [num, index === left && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-20px',\n                  fontSize: '10px',\n                  color: '#27ae60',\n                  fontWeight: 'bold'\n                },\n                children: \"L\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 23\n              }, this), index === mid && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-20px',\n                  fontSize: '10px',\n                  color: '#e74c3c',\n                  fontWeight: 'bold'\n                },\n                children: \"M\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 23\n              }, this), index === right && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-20px',\n                  fontSize: '10px',\n                  color: '#3498db',\n                  fontWeight: 'bold'\n                },\n                children: \"R\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              fontSize: '14px',\n              color: '#666',\n              marginBottom: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#27ae60'\n              },\n              children: [\"\\uD83D\\uDFE2 Left: \", left]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#e74c3c'\n              },\n              children: [\"\\uD83D\\uDD34 Mid: \", mid, \" (value: \", demoArray[mid], \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#3498db'\n              },\n              children: [\"\\uD83D\\uDD35 Right: \", right]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Target: \", target]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), searchHistory.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#2c3e50',\n                margin: '0 0 10px 0',\n                fontSize: '14px'\n              },\n              children: \"\\uD83D\\uDD0D Search History:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxHeight: '100px',\n                overflowY: 'auto',\n                fontSize: '12px'\n              },\n              children: searchHistory.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '5px',\n                  backgroundColor: '#f8f9fa',\n                  margin: '2px 0',\n                  borderRadius: '4px',\n                  border: '1px solid #dee2e6'\n                },\n                children: [\"Step \", index + 1, \": L=\", step.left, \", M=\", step.mid, \"(\", step.midValue, \"), R=\", step.right, \" \\u2192\", step.comparison === 'equal' ? ' Found!' : step.comparison === 'less' ? ' Search right' : ' Search left']\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            gap: '10px',\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: runDemo,\n            disabled: isRunning,\n            style: {\n              backgroundColor: '#9b59b6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: isRunning ? 'not-allowed' : 'pointer',\n              opacity: isRunning ? 0.6 : 1\n            },\n            children: isRunning ? '🔄 Searching...' : '▶️ Run Demo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetDemo,\n            style: {\n              backgroundColor: '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: \"\\uD83D\\uDD04 Reset\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '10px',\n            backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n            border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n            borderRadius: '6px',\n            textAlign: 'center',\n            fontWeight: 'bold',\n            color: result.includes('Found') ? '#155724' : '#721c24'\n          },\n          children: result\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0'\n          },\n          children: \"\\uD83E\\uDD14 Learning Prompt:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0',\n            fontStyle: 'italic'\n          },\n          children: steps[currentStep].learningPrompt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            style: {\n              color: '#856404',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCA1 Click to see answer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#856404',\n              margin: '10px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: steps[currentStep].answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginTop: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.max(0, currentStep - 1)),\n          disabled: currentStep === 0,\n          style: {\n            backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.min(steps.length - 1, currentStep + 1)),\n          disabled: currentStep === steps.length - 1,\n          style: {\n            backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#9b59b6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #9b59b6'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#9b59b6',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83C\\uDFAF Practice Problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '15px'\n        },\n        children: [{\n          name: \"Binary Search\",\n          difficulty: \"Easy\",\n          time: \"10 min\"\n        }, {\n          name: \"Search Insert Position\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Find First and Last Position\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Search in Rotated Array\",\n          difficulty: \"Medium\",\n          time: \"30 min\"\n        }, {\n          name: \"Find Peak Element\",\n          difficulty: \"Medium\",\n          time: \"20 min\"\n        }, {\n          name: \"Median of Two Sorted Arrays\",\n          difficulty: \"Hard\",\n          time: \"45 min\"\n        }].map((problem, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#2c3e50',\n              margin: '0 0 8px 0'\n            },\n            children: problem.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: problem.difficulty === 'Easy' ? '#27ae60' : problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c'\n              },\n              children: problem.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 17\n            }, this), ' • ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: problem.time\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 356,\n    columnNumber: 5\n  }, this);\n}\n_s(BinarySearchPattern, \"a4mqVzvaNucXiL9yeK11bqwza+o=\");\n_c = BinarySearchPattern;\nexport default BinarySearchPattern;\nvar _c;\n$RefreshReg$(_c, \"BinarySearchPattern\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "BinarySearchPattern", "_s", "currentStep", "setCurrentStep", "demoArray", "setDemoArray", "target", "<PERSON><PERSON><PERSON><PERSON>", "left", "setLeft", "right", "setRight", "mid", "setMid", "isRunning", "setIsRunning", "result", "setResult", "searchHistory", "setSearchHistory", "steps", "title", "content", "code", "learningPrompt", "answer", "runDemo", "l", "r", "length", "history", "m", "Math", "floor", "step", "midValue", "comparison", "push", "Promise", "resolve", "setTimeout", "resetDemo", "className", "style", "fontFamily", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "borderRadius", "height", "overflow", "width", "transition", "display", "justifyContent", "gap", "flexWrap", "map", "_", "index", "onClick", "padding", "border", "cursor", "boxShadow", "lineHeight", "whiteSpace", "num", "alignItems", "fontWeight", "borderColor", "position", "top", "maxHeight", "overflowY", "disabled", "opacity", "marginTop", "includes", "fontStyle", "paddingLeft", "max", "min", "gridTemplateColumns", "name", "difficulty", "time", "problem", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/patterns/BinarySearchPattern.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nfunction BinarySearchPattern() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 3, 5, 7, 9, 11, 13, 15, 17, 19]);\n  const [target, setTarget] = useState(11);\n  const [left, setLeft] = useState(0);\n  const [right, setRight] = useState(9);\n  const [mid, setMid] = useState(4);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n  const [searchHistory, setSearchHistory] = useState([]);\n\n  const steps = [\n    {\n      title: \"🔍 Understanding Binary Search Pattern\",\n      content: \"Binary Search efficiently finds elements in sorted arrays by repeatedly dividing the search space in half, achieving O(log n) time complexity.\",\n      code: `// Binary Search Template\nfunction binarySearch(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            return mid;  // Found target\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return -1;  // Target not found\n}`,\n      learningPrompt: \"Why does binary search require a sorted array?\",\n      answer: \"Binary search relies on the property that if the middle element is less than the target, all elements to the left are also less than the target, allowing us to eliminate half the search space.\"\n    },\n    {\n      title: \"📊 Step 1: Identify Binary Search Problems\",\n      content: \"Recognize when to use binary search: sorted arrays, search spaces, finding boundaries, or optimization problems.\",\n      code: `// Problem Types for Binary Search:\n\n// 1. Direct Search in Sorted Array\nfunction searchInsert(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        if (nums[mid] === target) return mid;\n        else if (nums[mid] < target) left = mid + 1;\n        else right = mid - 1;\n    }\n    \n    return left; // Insert position\n}\n\n// 2. Search in Rotated Array\nfunction searchRotated(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] === target) return mid;\n        \n        // Check which half is sorted\n        if (nums[left] <= nums[mid]) {\n            // Left half is sorted\n            if (nums[left] <= target && target < nums[mid]) {\n                right = mid - 1;\n            } else {\n                left = mid + 1;\n            }\n        } else {\n            // Right half is sorted\n            if (nums[mid] < target && target <= nums[right]) {\n                left = mid + 1;\n            } else {\n                right = mid - 1;\n            }\n        }\n    }\n    \n    return -1;\n}\n\n// 3. Find Peak Element\nfunction findPeakElement(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[mid + 1]) {\n            right = mid; // Peak is in left half\n        } else {\n            left = mid + 1; // Peak is in right half\n        }\n    }\n    \n    return left;\n}`,\n      learningPrompt: \"What are the key indicators that a problem can use binary search?\",\n      answer: \"1) Sorted data, 2) Search space that can be divided, 3) Monotonic function (if condition is true at position x, it's true for all positions in one direction), 4) Finding boundaries or optimal values.\"\n    },\n    {\n      title: \"🎯 Step 2: Set Up Search Boundaries\",\n      content: \"Initialize left and right pointers to define the search space correctly.\",\n      code: `// Step 2: Boundary Setup Patterns\n\n// Pattern 1: Standard Array Search\nlet left = 0;\nlet right = arr.length - 1;\n\n// Pattern 2: Search Insert Position\nlet left = 0;\nlet right = arr.length; // Note: length, not length-1\n\n// Pattern 3: Search in Range [min, max]\nlet left = minValue;\nlet right = maxValue;\n\n// Pattern 4: Search for First/Last Occurrence\nfunction findFirst(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            right = mid - 1; // Continue searching left for first occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}\n\nfunction findLast(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            left = mid + 1; // Continue searching right for last occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}`,\n      learningPrompt: \"Why might we use different boundary setups?\",\n      answer: \"Different problems require different search spaces: exact search uses [0, n-1], insert position uses [0, n], and range search uses [min, max] values.\"\n    },\n    {\n      title: \"⚡ Step 3: Calculate Mid and Compare\",\n      content: \"Calculate the middle point and make comparisons to decide which half to search next.\",\n      code: `// Step 3: Mid Calculation and Comparison Logic\n\nfunction binarySearchWithSteps(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    let steps = [];\n    \n    while (left <= right) {\n        // Calculate mid (avoid overflow)\n        let mid = Math.floor((left + right) / 2);\n        // Alternative: let mid = left + Math.floor((right - left) / 2);\n        \n        steps.push({\n            left: left,\n            right: right,\n            mid: mid,\n            midValue: arr[mid],\n            comparison: arr[mid] === target ? 'equal' : \n                       arr[mid] < target ? 'less' : 'greater'\n        });\n        \n        if (arr[mid] === target) {\n            return { found: true, index: mid, steps: steps };\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return { found: false, index: -1, steps: steps };\n}\n\n// Avoid integer overflow (important in some languages)\nfunction safeMidCalculation(left, right) {\n    // Instead of: (left + right) / 2\n    // Use: left + (right - left) / 2\n    return left + Math.floor((right - left) / 2);\n}`,\n      learningPrompt: \"Why do we use Math.floor for mid calculation?\",\n      answer: \"Math.floor ensures we get an integer index. For arrays with even length, we consistently choose the left-middle element, preventing infinite loops.\"\n    },\n    {\n      title: \"🎮 Step 4: Interactive Demo\",\n      content: \"Watch binary search in action as it efficiently narrows down the search space.\",\n      code: `// Live Demo: Binary Search\nconst arr = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];\nconst target = 11;\n\n// Current search space:\n// Left: ${left} (value: ${demoArray[left]})\n// Right: ${right} (value: ${demoArray[right]})\n// Mid: ${mid} (value: ${demoArray[mid]})\n// Comparison: ${demoArray[mid]} vs ${target}`,\n      learningPrompt: \"How many comparisons does binary search need in the worst case?\",\n      answer: \"At most ⌊log₂(n)⌋ + 1 comparisons, where n is the array size. For 1000 elements, that's only about 10 comparisons!\"\n    },\n    {\n      title: \"🔧 Step 5: Advanced Binary Search Patterns\",\n      content: \"Master advanced patterns like finding boundaries, searching in 2D matrices, and optimization problems.\",\n      code: `// Advanced Pattern 1: Search in 2D Matrix\nfunction searchMatrix(matrix, target) {\n    if (!matrix.length || !matrix[0].length) return false;\n    \n    let rows = matrix.length;\n    let cols = matrix[0].length;\n    let left = 0;\n    let right = rows * cols - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        let midValue = matrix[Math.floor(mid / cols)][mid % cols];\n        \n        if (midValue === target) {\n            return true;\n        } else if (midValue < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return false;\n}\n\n// Advanced Pattern 2: Find Minimum in Rotated Array\nfunction findMin(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[right]) {\n            // Minimum is in right half\n            left = mid + 1;\n        } else {\n            // Minimum is in left half (including mid)\n            right = mid;\n        }\n    }\n    \n    return nums[left];\n}\n\n// Advanced Pattern 3: Binary Search on Answer\nfunction minEatingSpeed(piles, h) {\n    let left = 1;\n    let right = Math.max(...piles);\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        let hours = 0;\n        \n        for (let pile of piles) {\n            hours += Math.ceil(pile / mid);\n        }\n        \n        if (hours <= h) {\n            right = mid; // Can eat slower\n        } else {\n            left = mid + 1; // Need to eat faster\n        }\n    }\n    \n    return left;\n}`,\n      learningPrompt: \"What's the key insight in 'binary search on answer' problems?\",\n      answer: \"Instead of searching in an array, we search in the range of possible answers. If a speed/capacity works, all higher values also work (monotonic property).\"\n    }\n  ];\n\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    setSearchHistory([]);\n    \n    let l = 0;\n    let r = demoArray.length - 1;\n    let history = [];\n    \n    while (l <= r) {\n      let m = Math.floor((l + r) / 2);\n      setLeft(l);\n      setRight(r);\n      setMid(m);\n      \n      const step = {\n        left: l,\n        right: r,\n        mid: m,\n        midValue: demoArray[m],\n        comparison: demoArray[m] === target ? 'equal' : \n                   demoArray[m] < target ? 'less' : 'greater'\n      };\n      \n      history.push(step);\n      setSearchHistory([...history]);\n      \n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      if (demoArray[m] === target) {\n        setResult(`Found ${target} at index ${m}!`);\n        setIsRunning(false);\n        return;\n      } else if (demoArray[m] < target) {\n        l = m + 1;\n      } else {\n        r = m - 1;\n      }\n    }\n    \n    setResult(`${target} not found in array`);\n    setIsRunning(false);\n  };\n\n  const resetDemo = () => {\n    setLeft(0);\n    setRight(demoArray.length - 1);\n    setMid(Math.floor((demoArray.length - 1) / 2));\n    setResult(null);\n    setSearchHistory([]);\n    setIsRunning(false);\n  };\n\n  return (\n    <div className=\"page-content\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Header */}\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#9b59b6', fontSize: '2.5em', marginBottom: '10px' }}>\n          🔍 Binary Search Pattern\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>\n          Master binary search to achieve O(log n) solutions for search and optimization problems\n        </p>\n      </div>\n\n      {/* Progress Bar */}\n      <div style={{ marginBottom: '30px' }}>\n        <div style={{\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        }}>\n          <div style={{\n            backgroundColor: '#9b59b6',\n            height: '100%',\n            width: `${((currentStep + 1) / steps.length) * 100}%`,\n            transition: 'width 0.3s ease'\n          }}></div>\n        </div>\n        <p style={{ textAlign: 'center', margin: '10px 0', color: '#7f8c8d' }}>\n          Step {currentStep + 1} of {steps.length}\n        </p>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        gap: '10px', \n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      }}>\n        {steps.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentStep(index)}\n            style={{\n              padding: '8px 16px',\n              border: currentStep === index ? '2px solid #9b59b6' : '1px solid #ddd',\n              borderRadius: '20px',\n              backgroundColor: currentStep === index ? '#9b59b6' : 'white',\n              color: currentStep === index ? 'white' : '#333',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Current Step Content */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      }}>\n        <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>\n          {steps[currentStep].title}\n        </h2>\n        \n        <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>\n          {steps[currentStep].content}\n        </p>\n\n        {/* Code Block */}\n        <div style={{\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        }}>\n          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>\n            {steps[currentStep].code}\n          </pre>\n        </div>\n\n        {/* Interactive Demo for Step 4 */}\n        {currentStep === 4 && (\n          <div style={{\n            backgroundColor: '#f0f8ff',\n            border: '2px solid #9b59b6',\n            borderRadius: '12px',\n            padding: '20px',\n            marginBottom: '20px'\n          }}>\n            <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>🎮 Live Demo: Binary Search</h3>\n            \n            {/* Array Visualization */}\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', gap: '3px', justifyContent: 'center', marginBottom: '10px', flexWrap: 'wrap' }}>\n                {demoArray.map((num, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '35px',\n                      height: '35px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      backgroundColor: \n                        index === mid ? '#e74c3c' :\n                        index === left ? '#27ae60' :\n                        index === right ? '#3498db' :\n                        index >= left && index <= right ? '#f39c12' : '#ecf0f1',\n                      color: (index >= left && index <= right) ? 'white' : '#333',\n                      borderRadius: '6px',\n                      fontWeight: 'bold',\n                      fontSize: '12px',\n                      border: '2px solid',\n                      borderColor: \n                        index === mid ? '#c0392b' :\n                        index === left ? '#229954' :\n                        index === right ? '#2980b9' :\n                        index >= left && index <= right ? '#e67e22' : '#bdc3c7',\n                      position: 'relative'\n                    }}\n                  >\n                    {num}\n                    {index === left && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-20px',\n                        fontSize: '10px',\n                        color: '#27ae60',\n                        fontWeight: 'bold'\n                      }}>\n                        L\n                      </div>\n                    )}\n                    {index === mid && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-20px',\n                        fontSize: '10px',\n                        color: '#e74c3c',\n                        fontWeight: 'bold'\n                      }}>\n                        M\n                      </div>\n                    )}\n                    {index === right && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-20px',\n                        fontSize: '10px',\n                        color: '#3498db',\n                        fontWeight: 'bold'\n                      }}>\n                        R\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n              \n              <div style={{ textAlign: 'center', fontSize: '14px', color: '#666', marginBottom: '15px' }}>\n                <span style={{ color: '#27ae60' }}>🟢 Left: {left}</span>\n                {' | '}\n                <span style={{ color: '#e74c3c' }}>🔴 Mid: {mid} (value: {demoArray[mid]})</span>\n                {' | '}\n                <span style={{ color: '#3498db' }}>🔵 Right: {right}</span>\n                {' | '}\n                <span>Target: {target}</span>\n              </div>\n\n              {/* Search History */}\n              {searchHistory.length > 0 && (\n                <div style={{ marginBottom: '15px' }}>\n                  <h4 style={{ color: '#2c3e50', margin: '0 0 10px 0', fontSize: '14px' }}>\n                    🔍 Search History:\n                  </h4>\n                  <div style={{ maxHeight: '100px', overflowY: 'auto', fontSize: '12px' }}>\n                    {searchHistory.map((step, index) => (\n                      <div key={index} style={{ \n                        padding: '5px', \n                        backgroundColor: '#f8f9fa', \n                        margin: '2px 0',\n                        borderRadius: '4px',\n                        border: '1px solid #dee2e6'\n                      }}>\n                        Step {index + 1}: L={step.left}, M={step.mid}({step.midValue}), R={step.right} → \n                        {step.comparison === 'equal' ? ' Found!' :\n                         step.comparison === 'less' ? ' Search right' : ' Search left'}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Controls */}\n            <div style={{ textAlign: 'center', gap: '10px', display: 'flex', justifyContent: 'center' }}>\n              <button\n                onClick={runDemo}\n                disabled={isRunning}\n                style={{\n                  backgroundColor: '#9b59b6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: isRunning ? 'not-allowed' : 'pointer',\n                  opacity: isRunning ? 0.6 : 1\n                }}\n              >\n                {isRunning ? '🔄 Searching...' : '▶️ Run Demo'}\n              </button>\n              \n              <button\n                onClick={resetDemo}\n                style={{\n                  backgroundColor: '#95a5a6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: 'pointer'\n                }}\n              >\n                🔄 Reset\n              </button>\n            </div>\n\n            {result && (\n              <div style={{\n                marginTop: '15px',\n                padding: '10px',\n                backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n                border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n                borderRadius: '6px',\n                textAlign: 'center',\n                fontWeight: 'bold',\n                color: result.includes('Found') ? '#155724' : '#721c24'\n              }}>\n                {result}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Learning Prompt */}\n        <div style={{\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        }}>\n          <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>\n            🤔 Learning Prompt:\n          </h4>\n          <p style={{ color: '#856404', margin: '0 0 10px 0', fontStyle: 'italic' }}>\n            {steps[currentStep].learningPrompt}\n          </p>\n          <details>\n            <summary style={{ color: '#856404', cursor: 'pointer', fontWeight: 'bold' }}>\n              💡 Click to see answer\n            </summary>\n            <p style={{ color: '#856404', margin: '10px 0 0 0', paddingLeft: '20px' }}>\n              {steps[currentStep].answer}\n            </p>\n          </details>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '30px' }}>\n          <button\n            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}\n            disabled={currentStep === 0}\n            style={{\n              backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            ← Previous\n          </button>\n          \n          <button\n            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}\n            disabled={currentStep === steps.length - 1}\n            style={{\n              backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#9b59b6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next →\n          </button>\n        </div>\n      </div>\n\n      {/* Practice Problems */}\n      <div style={{\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #9b59b6'\n      }}>\n        <h3 style={{ color: '#9b59b6', marginBottom: '20px' }}>\n          🎯 Practice Problems\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\n          {[\n            { name: \"Binary Search\", difficulty: \"Easy\", time: \"10 min\" },\n            { name: \"Search Insert Position\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Find First and Last Position\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Search in Rotated Array\", difficulty: \"Medium\", time: \"30 min\" },\n            { name: \"Find Peak Element\", difficulty: \"Medium\", time: \"20 min\" },\n            { name: \"Median of Two Sorted Arrays\", difficulty: \"Hard\", time: \"45 min\" }\n          ].map((problem, index) => (\n            <div\n              key={index}\n              style={{\n                backgroundColor: 'white',\n                padding: '15px',\n                borderRadius: '8px',\n                border: '1px solid #dee2e6'\n              }}\n            >\n              <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0' }}>{problem.name}</h4>\n              <div style={{ fontSize: '14px', color: '#666' }}>\n                <span style={{ \n                  color: problem.difficulty === 'Easy' ? '#27ae60' : \n                        problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c' \n                }}>\n                  {problem.difficulty}\n                </span>\n                {' • '}\n                <span>{problem.time}</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default BinarySearchPattern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAC/E,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACe,GAAG,EAAEC,MAAM,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EACjC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAMuB,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,wCAAwC;IAC/CC,OAAO,EAAE,gJAAgJ;IACzJC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,gDAAgD;IAChEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,4CAA4C;IACnDC,OAAO,EAAE,kHAAkH;IAC3HC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,mEAAmE;IACnFC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,0EAA0E;IACnFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,6CAA6C;IAC7DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,sFAAsF;IAC/FC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,gFAAgF;IACzFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA,WAAWf,IAAI,YAAYJ,SAAS,CAACI,IAAI,CAAC;AAC1C,YAAYE,KAAK,YAAYN,SAAS,CAACM,KAAK,CAAC;AAC7C,UAAUE,GAAG,YAAYR,SAAS,CAACQ,GAAG,CAAC;AACvC,iBAAiBR,SAAS,CAACQ,GAAG,CAAC,OAAON,MAAM,EAAE;IACxCkB,cAAc,EAAE,iEAAiE;IACjFC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,4CAA4C;IACnDC,OAAO,EAAE,wGAAwG;IACjHC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+DAA+D;IAC/EC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1BX,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,IAAI,CAAC;IACfE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAIQ,CAAC,GAAG,CAAC;IACT,IAAIC,CAAC,GAAGxB,SAAS,CAACyB,MAAM,GAAG,CAAC;IAC5B,IAAIC,OAAO,GAAG,EAAE;IAEhB,OAAOH,CAAC,IAAIC,CAAC,EAAE;MACb,IAAIG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACN,CAAC,GAAGC,CAAC,IAAI,CAAC,CAAC;MAC/BnB,OAAO,CAACkB,CAAC,CAAC;MACVhB,QAAQ,CAACiB,CAAC,CAAC;MACXf,MAAM,CAACkB,CAAC,CAAC;MAET,MAAMG,IAAI,GAAG;QACX1B,IAAI,EAAEmB,CAAC;QACPjB,KAAK,EAAEkB,CAAC;QACRhB,GAAG,EAAEmB,CAAC;QACNI,QAAQ,EAAE/B,SAAS,CAAC2B,CAAC,CAAC;QACtBK,UAAU,EAAEhC,SAAS,CAAC2B,CAAC,CAAC,KAAKzB,MAAM,GAAG,OAAO,GAClCF,SAAS,CAAC2B,CAAC,CAAC,GAAGzB,MAAM,GAAG,MAAM,GAAG;MAC9C,CAAC;MAEDwB,OAAO,CAACO,IAAI,CAACH,IAAI,CAAC;MAClBf,gBAAgB,CAAC,CAAC,GAAGW,OAAO,CAAC,CAAC;MAE9B,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,IAAInC,SAAS,CAAC2B,CAAC,CAAC,KAAKzB,MAAM,EAAE;QAC3BW,SAAS,CAAC,SAASX,MAAM,aAAayB,CAAC,GAAG,CAAC;QAC3ChB,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIX,SAAS,CAAC2B,CAAC,CAAC,GAAGzB,MAAM,EAAE;QAChCqB,CAAC,GAAGI,CAAC,GAAG,CAAC;MACX,CAAC,MAAM;QACLH,CAAC,GAAGG,CAAC,GAAG,CAAC;MACX;IACF;IAEAd,SAAS,CAAC,GAAGX,MAAM,qBAAqB,CAAC;IACzCS,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM0B,SAAS,GAAGA,CAAA,KAAM;IACtBhC,OAAO,CAAC,CAAC,CAAC;IACVE,QAAQ,CAACP,SAAS,CAACyB,MAAM,GAAG,CAAC,CAAC;IAC9BhB,MAAM,CAACmB,IAAI,CAACC,KAAK,CAAC,CAAC7B,SAAS,CAACyB,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9CZ,SAAS,CAAC,IAAI,CAAC;IACfE,gBAAgB,CAAC,EAAE,CAAC;IACpBJ,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEhB,OAAA;IAAK2C,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAEvE9C,OAAA;MAAK4C,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxD9C,OAAA;QAAI4C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEF,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLtD,OAAA;QAAG4C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEK,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAExF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNtD,OAAA;MAAK4C,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnC9C,OAAA;QAAK4C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACA9C,OAAA;UAAK4C,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,MAAM;YACdE,KAAK,EAAE,GAAI,CAAC1D,WAAW,GAAG,CAAC,IAAIkB,KAAK,CAACS,MAAM,GAAI,GAAG,GAAG;YACrDgC,UAAU,EAAE;UACd;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNtD,OAAA;QAAG4C,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAES,MAAM,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAH,QAAA,GAAC,OAChE,EAAC3C,WAAW,GAAG,CAAC,EAAC,MAAI,EAACkB,KAAK,CAACS,MAAM;MAAA;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNtD,OAAA;MAAK4C,KAAK,EAAE;QACVmB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXjB,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE;MACZ,CAAE;MAAApB,QAAA,EACCzB,KAAK,CAAC8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAClBrE,OAAA;QAEEsE,OAAO,EAAEA,CAAA,KAAMlE,cAAc,CAACiE,KAAK,CAAE;QACrCzB,KAAK,EAAE;UACL2B,OAAO,EAAE,UAAU;UACnBC,MAAM,EAAErE,WAAW,KAAKkE,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;UACtEX,YAAY,EAAE,MAAM;UACpBD,eAAe,EAAEtD,WAAW,KAAKkE,KAAK,GAAG,SAAS,GAAG,OAAO;UAC5DpB,KAAK,EAAE9C,WAAW,KAAKkE,KAAK,GAAG,OAAO,GAAG,MAAM;UAC/CI,MAAM,EAAE,SAAS;UACjBvB,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EAEDuB,KAAK,GAAG;MAAC,GAZLA,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNtD,OAAA;MAAK4C,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACA9C,OAAA;QAAI4C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDzB,KAAK,CAAClB,WAAW,CAAC,CAACmB;MAAK;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAELtD,OAAA;QAAG4C,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAE0B,UAAU,EAAE,KAAK;UAAE3B,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClEzB,KAAK,CAAClB,WAAW,CAAC,CAACoB;MAAO;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAGJtD,OAAA;QAAK4C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE,MAAM;UACpBH,UAAU,EAAE,6BAA6B;UACzCK,QAAQ,EAAE,MAAM;UAChBU,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACA9C,OAAA;UAAK4C,KAAK,EAAE;YAAEY,MAAM,EAAE,CAAC;YAAEoB,UAAU,EAAE;UAAW,CAAE;UAAA9B,QAAA,EAC/CzB,KAAK,CAAClB,WAAW,CAAC,CAACqB;QAAI;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLnD,WAAW,KAAK,CAAC,iBAChBH,OAAA;QAAK4C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,MAAM;UACpBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACA9C,OAAA;UAAI4C,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAA2B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGvFtD,OAAA;UAAK4C,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC9C,OAAA;YAAK4C,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,KAAK;cAAED,cAAc,EAAE,QAAQ;cAAEhB,YAAY,EAAE,MAAM;cAAEkB,QAAQ,EAAE;YAAO,CAAE;YAAApB,QAAA,EAC3GzC,SAAS,CAAC8D,GAAG,CAAC,CAACU,GAAG,EAAER,KAAK,kBACxBrE,OAAA;cAEE4C,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdI,OAAO,EAAE,MAAM;gBACfe,UAAU,EAAE,QAAQ;gBACpBd,cAAc,EAAE,QAAQ;gBACxBP,eAAe,EACbY,KAAK,KAAKxD,GAAG,GAAG,SAAS,GACzBwD,KAAK,KAAK5D,IAAI,GAAG,SAAS,GAC1B4D,KAAK,KAAK1D,KAAK,GAAG,SAAS,GAC3B0D,KAAK,IAAI5D,IAAI,IAAI4D,KAAK,IAAI1D,KAAK,GAAG,SAAS,GAAG,SAAS;gBACzDsC,KAAK,EAAGoB,KAAK,IAAI5D,IAAI,IAAI4D,KAAK,IAAI1D,KAAK,GAAI,OAAO,GAAG,MAAM;gBAC3D+C,YAAY,EAAE,KAAK;gBACnBqB,UAAU,EAAE,MAAM;gBAClB7B,QAAQ,EAAE,MAAM;gBAChBsB,MAAM,EAAE,WAAW;gBACnBQ,WAAW,EACTX,KAAK,KAAKxD,GAAG,GAAG,SAAS,GACzBwD,KAAK,KAAK5D,IAAI,GAAG,SAAS,GAC1B4D,KAAK,KAAK1D,KAAK,GAAG,SAAS,GAC3B0D,KAAK,IAAI5D,IAAI,IAAI4D,KAAK,IAAI1D,KAAK,GAAG,SAAS,GAAG,SAAS;gBACzDsE,QAAQ,EAAE;cACZ,CAAE;cAAAnC,QAAA,GAED+B,GAAG,EACHR,KAAK,KAAK5D,IAAI,iBACbT,OAAA;gBAAK4C,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAe,KAAK,KAAKxD,GAAG,iBACZb,OAAA;gBAAK4C,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAe,KAAK,KAAK1D,KAAK,iBACdX,OAAA;gBAAK4C,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GA1DIe,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2DP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAK4C,KAAK,EAAE;cAAEG,SAAS,EAAE,QAAQ;cAAEG,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE,MAAM;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,gBACzF9C,OAAA;cAAM4C,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,qBAAS,EAACrC,IAAI;YAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,KAAK,eACNtD,OAAA;cAAM4C,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,oBAAQ,EAACjC,GAAG,EAAC,WAAS,EAACR,SAAS,CAACQ,GAAG,CAAC,EAAC,GAAC;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAChF,KAAK,eACNtD,OAAA;cAAM4C,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,sBAAU,EAACnC,KAAK;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1D,KAAK,eACNtD,OAAA;cAAA8C,QAAA,GAAM,UAAQ,EAACvC,MAAM;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,EAGLnC,aAAa,CAACW,MAAM,GAAG,CAAC,iBACvB9B,OAAA;YAAK4C,KAAK,EAAE;cAAEI,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,gBACnC9C,OAAA;cAAI4C,KAAK,EAAE;gBAAEK,KAAK,EAAE,SAAS;gBAAEO,MAAM,EAAE,YAAY;gBAAEN,QAAQ,EAAE;cAAO,CAAE;cAAAJ,QAAA,EAAC;YAEzE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtD,OAAA;cAAK4C,KAAK,EAAE;gBAAEuC,SAAS,EAAE,OAAO;gBAAEC,SAAS,EAAE,MAAM;gBAAElC,QAAQ,EAAE;cAAO,CAAE;cAAAJ,QAAA,EACrE3B,aAAa,CAACgD,GAAG,CAAC,CAAChC,IAAI,EAAEkC,KAAK,kBAC7BrE,OAAA;gBAAiB4C,KAAK,EAAE;kBACtB2B,OAAO,EAAE,KAAK;kBACdd,eAAe,EAAE,SAAS;kBAC1BD,MAAM,EAAE,OAAO;kBACfE,YAAY,EAAE,KAAK;kBACnBc,MAAM,EAAE;gBACV,CAAE;gBAAA1B,QAAA,GAAC,OACI,EAACuB,KAAK,GAAG,CAAC,EAAC,MAAI,EAAClC,IAAI,CAAC1B,IAAI,EAAC,MAAI,EAAC0B,IAAI,CAACtB,GAAG,EAAC,GAAC,EAACsB,IAAI,CAACC,QAAQ,EAAC,OAAK,EAACD,IAAI,CAACxB,KAAK,EAAC,SAC9E,EAACwB,IAAI,CAACE,UAAU,KAAK,OAAO,GAAG,SAAS,GACvCF,IAAI,CAACE,UAAU,KAAK,MAAM,GAAG,eAAe,GAAG,cAAc;cAAA,GATtDgC,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNtD,OAAA;UAAK4C,KAAK,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEkB,GAAG,EAAE,MAAM;YAAEF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBAC1F9C,OAAA;YACEsE,OAAO,EAAE3C,OAAQ;YACjB0D,QAAQ,EAAEtE,SAAU;YACpB6B,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE1D,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CuE,OAAO,EAAEvE,SAAS,GAAG,GAAG,GAAG;YAC7B,CAAE;YAAA+B,QAAA,EAED/B,SAAS,GAAG,iBAAiB,GAAG;UAAa;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAETtD,OAAA;YACEsE,OAAO,EAAE5B,SAAU;YACnBE,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELrC,MAAM,iBACLjB,OAAA;UAAK4C,KAAK,EAAE;YACV2C,SAAS,EAAE,MAAM;YACjBhB,OAAO,EAAE,MAAM;YACfd,eAAe,EAAExC,MAAM,CAACuE,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS;YACjEhB,MAAM,EAAE,aAAavD,MAAM,CAACuE,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE;YACvE9B,YAAY,EAAE,KAAK;YACnBX,SAAS,EAAE,QAAQ;YACnBgC,UAAU,EAAE,MAAM;YAClB9B,KAAK,EAAEhC,MAAM,CAACuE,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG;UAChD,CAAE;UAAA1C,QAAA,EACC7B;QAAM;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGDtD,OAAA;QAAK4C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACA9C,OAAA;UAAI4C,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE;UAAa,CAAE;UAAAV,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtD,OAAA;UAAG4C,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE,YAAY;YAAEiC,SAAS,EAAE;UAAS,CAAE;UAAA3C,QAAA,EACvEzB,KAAK,CAAClB,WAAW,CAAC,CAACsB;QAAc;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJtD,OAAA;UAAA8C,QAAA,gBACE9C,OAAA;YAAS4C,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEwB,MAAM,EAAE,SAAS;cAAEM,UAAU,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAAC;UAE7E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACVtD,OAAA;YAAG4C,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE,YAAY;cAAEkC,WAAW,EAAE;YAAO,CAAE;YAAA5C,QAAA,EACvEzB,KAAK,CAAClB,WAAW,CAAC,CAACuB;UAAM;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGNtD,OAAA;QAAK4C,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEuB,SAAS,EAAE;QAAO,CAAE;QAAAzC,QAAA,gBAClF9C,OAAA;UACEsE,OAAO,EAAEA,CAAA,KAAMlE,cAAc,CAAC6B,IAAI,CAAC0D,GAAG,CAAC,CAAC,EAAExF,WAAW,GAAG,CAAC,CAAC,CAAE;UAC5DkF,QAAQ,EAAElF,WAAW,KAAK,CAAE;UAC5ByC,KAAK,EAAE;YACLa,eAAe,EAAEtD,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC1D8C,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEtE,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;UAC9C,CAAE;UAAA2C,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtD,OAAA;UACEsE,OAAO,EAAEA,CAAA,KAAMlE,cAAc,CAAC6B,IAAI,CAAC2D,GAAG,CAACvE,KAAK,CAACS,MAAM,GAAG,CAAC,EAAE3B,WAAW,GAAG,CAAC,CAAC,CAAE;UAC3EkF,QAAQ,EAAElF,WAAW,KAAKkB,KAAK,CAACS,MAAM,GAAG,CAAE;UAC3Cc,KAAK,EAAE;YACLa,eAAe,EAAEtD,WAAW,KAAKkB,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACzEmB,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEtE,WAAW,KAAKkB,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UAC7D,CAAE;UAAAgB,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAK4C,KAAK,EAAE;QACVa,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACA9C,OAAA;QAAI4C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEvD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELtD,OAAA;QAAK4C,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAE8B,mBAAmB,EAAE,sCAAsC;UAAE5B,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EACvG,CACC;UAAEgD,IAAI,EAAE,eAAe;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC7D;UAAEF,IAAI,EAAE,wBAAwB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACtE;UAAEF,IAAI,EAAE,8BAA8B;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC9E;UAAEF,IAAI,EAAE,yBAAyB;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACzE;UAAEF,IAAI,EAAE,mBAAmB;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACnE;UAAEF,IAAI,EAAE,6BAA6B;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,CAC5E,CAAC7B,GAAG,CAAC,CAAC8B,OAAO,EAAE5B,KAAK,kBACnBrE,OAAA;UAEE4C,KAAK,EAAE;YACLa,eAAe,EAAE,OAAO;YACxBc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBAEF9C,OAAA;YAAI4C,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE;YAAY,CAAE;YAAAV,QAAA,EAAEmD,OAAO,CAACH;UAAI;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzEtD,OAAA;YAAK4C,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBAC9C9C,OAAA;cAAM4C,KAAK,EAAE;gBACXK,KAAK,EAAEgD,OAAO,CAACF,UAAU,KAAK,MAAM,GAAG,SAAS,GAC1CE,OAAO,CAACF,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG;cACtD,CAAE;cAAAjD,QAAA,EACCmD,OAAO,CAACF;YAAU;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACN,KAAK,eACNtD,OAAA;cAAA8C,QAAA,EAAOmD,OAAO,CAACD;YAAI;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA,GAlBDe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpD,EAAA,CAvsBQD,mBAAmB;AAAAiG,EAAA,GAAnBjG,mBAAmB;AAysB5B,eAAeA,mBAAmB;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}