{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\MainPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport questionsData from './mcqs.json';\nimport PatternsSummary from './PatternsSummary';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MainPage() {\n  _s();\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState(null);\n  const [promptInputAnswers, setPromptInputAnswers] = useState({});\n  const [selectedPrompts, setSelectedPrompts] = useState([]);\n  const [report, setReport] = useState('');\n  const [selectedPatterns, setSelectedPatterns] = useState(new Set());\n  const [filteredQuestions, setFilteredQuestions] = useState([]);\n  const [showPatternSelector, setShowPatternSelector] = useState(false);\n\n  // Available patterns for selection\n  const availablePatterns = [\"1. Two Pointers\", \"2. Merge Intervals\", \"3. Sliding Window\", \"4. Binary Search\", \"5. Tree Traversal\", \"6. Dynamic Programming (1D)\", \"7. Fast & Slow Pointers\", \"8. Graph Algorithms\", \"Island Pattern\", \"Tree BFS\", \"Tree DFS\", \"Heap\", \"Modified Binary Search\", \"Subsets\", \"Greedy Algorithm\", \"0/1 Knapsack\"];\n  useEffect(() => {\n    const allQuestions = Object.values(questionsData).flat();\n    setQuestions(allQuestions);\n    setFilteredQuestions(allQuestions);\n    const savedIndex = localStorage.getItem('currentQuestionIndex');\n    if (savedIndex !== null) {\n      setCurrentQuestionIndex(parseInt(savedIndex, 10));\n    }\n  }, []);\n\n  // Filter questions based on selected patterns\n  useEffect(() => {\n    if (selectedPatterns.size === 0) {\n      const allQuestions = Object.values(questionsData).flat();\n      setFilteredQuestions(allQuestions);\n    } else {\n      const filtered = [];\n      selectedPatterns.forEach(pattern => {\n        if (questionsData[pattern]) {\n          filtered.push(...questionsData[pattern]);\n        }\n      });\n      setFilteredQuestions(filtered);\n      setCurrentQuestionIndex(0); // Reset to first question when filtering\n    }\n  }, [selectedPatterns]);\n  useEffect(() => {\n    if (filteredQuestions.length > 0) {\n      localStorage.setItem('currentQuestionIndex', currentQuestionIndex);\n    }\n  }, [currentQuestionIndex, filteredQuestions.length]);\n\n  // Pattern selection handlers\n  const handlePatternToggle = pattern => {\n    const newSelected = new Set(selectedPatterns);\n    if (newSelected.has(pattern)) {\n      newSelected.delete(pattern);\n    } else {\n      newSelected.add(pattern);\n    }\n    setSelectedPatterns(newSelected);\n  };\n  const selectAllPatterns = () => {\n    setSelectedPatterns(new Set(availablePatterns));\n  };\n  const clearAllPatterns = () => {\n    setSelectedPatterns(new Set());\n  };\n  const handleNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n      setSelectedCorrectAnswer(null);\n      setPromptInputAnswers({});\n      setSelectedPrompts([]);\n      setReport('');\n    }\n  };\n  const handlePrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n      setSelectedCorrectAnswer(null);\n      setPromptInputAnswers({});\n      setSelectedPrompts([]);\n      setReport('');\n    }\n  };\n  const handleCorrectAnswerChange = option => {\n    setSelectedCorrectAnswer(option);\n  };\n  const handlePromptInputChange = option => {\n    setPromptInputAnswers({\n      ...promptInputAnswers,\n      [option]: !promptInputAnswers[option]\n    });\n  };\n  const handlePromptChange = (prompt, isChecked) => {\n    if (isChecked) {\n      setSelectedPrompts(prev => [...prev, prompt]);\n    } else {\n      setSelectedPrompts(prev => prev.filter(p => p !== prompt));\n    }\n  };\n  const handleGenerateReport = async () => {\n    const reportData = {\n      question: questions[currentQuestionIndex].question,\n      correctAnswer: selectedCorrectAnswer,\n      promptInputAnswers: Object.keys(promptInputAnswers).filter(answer => promptInputAnswers[answer]),\n      selectedPrompts: selectedPrompts\n    };\n    try {\n      const response = await fetch('http://localhost:3001/generate-report', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(reportData)\n      });\n      const data = await response.json();\n      setReport(data.report);\n    } catch (error) {\n      console.error('Error generating report:', error);\n    }\n  };\n  if (questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 12\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const prompts = ['can you design a solution in c# that will provide an output for the selected answer', 'write a program thats based on the choice', 'what is the gap between selected items', 'what is the consequence if I\\'m correct for the given choice based on the question', 'rephrase the question based on my choice', 'what is wrong with this implementation', 'what if my syntasx is correct'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#e8f5e8',\n        padding: '20px',\n        margin: '20px 0',\n        borderRadius: '8px',\n        border: '2px solid #27ae60',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#27ae60',\n          margin: '0 0 10px 0'\n        },\n        children: \"\\uD83C\\uDFAF NEW: Complete Coding Patterns Guide Available!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 15px 0',\n          fontSize: '16px'\n        },\n        children: \"Master all 16 essential coding patterns for technical interviews with step-by-step implementations and interactive demos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '15px',\n          justifyContent: 'center',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/patterns\",\n          style: {\n            display: 'inline-block',\n            padding: '10px 20px',\n            backgroundColor: '#27ae60',\n            color: 'white',\n            textDecoration: 'none',\n            borderRadius: '5px',\n            fontWeight: 'bold'\n          },\n          children: \"\\uD83C\\uDFAF Coding Patterns Guide \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cqrs\",\n          style: {\n            display: 'inline-block',\n            padding: '10px 20px',\n            backgroundColor: '#3498db',\n            color: 'white',\n            textDecoration: 'none',\n            borderRadius: '5px',\n            fontWeight: 'bold'\n          },\n          children: \"\\uD83C\\uDFD7\\uFE0F CQRS Pattern \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PatternsSummary, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: currentQuestion.question\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-container\",\n        children: currentQuestion.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            name: `question-${currentQuestionIndex}`,\n            checked: selectedCorrectAnswer === option,\n            onChange: () => handleCorrectAnswerChange(option)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: option\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: !!promptInputAnswers[option],\n            onChange: () => handlePromptInputChange(option)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prompts-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Follow-up Prompts:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), prompts.map((prompt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prompt\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: prompt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: selectedPrompts.includes(prompt),\n            onChange: e => handlePromptChange(prompt, e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleGenerateReport,\n        children: \"Generate Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), report && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Consolidated Report:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          children: report\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navigation-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrevious,\n          disabled: currentQuestionIndex === 0,\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNext,\n          disabled: currentQuestionIndex === questions.length - 1,\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n}\n_s(MainPage, \"NX1kOy++GdSEZVjJQhPj5jxic2o=\");\n_c = MainPage;\nexport default MainPage;\nvar _c;\n$RefreshReg$(_c, \"MainPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "questionsData", "Pat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "MainPage", "_s", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedCorrectAnswer", "setSelectedCorrectAnswer", "promptInputAnswers", "setPromptInputAnswers", "selectedPrompts", "setSelectedPrompts", "report", "setReport", "selectedPatterns", "setSelectedPatterns", "Set", "filteredQuestions", "setFilteredQuestions", "showPatternSelector", "setShowPatternSelector", "availablePatterns", "allQuestions", "Object", "values", "flat", "savedIndex", "localStorage", "getItem", "parseInt", "size", "filtered", "for<PERSON>ach", "pattern", "push", "length", "setItem", "handlePatternToggle", "newSelected", "has", "delete", "add", "selectAllPatterns", "clearAllPatterns", "handleNext", "handlePrevious", "handleCorrectAnswerChange", "option", "handlePromptInputChange", "handlePromptChange", "prompt", "isChecked", "prev", "filter", "p", "handleGenerateReport", "reportData", "question", "<PERSON><PERSON><PERSON><PERSON>", "keys", "answer", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentQuestion", "prompts", "className", "style", "backgroundColor", "padding", "margin", "borderRadius", "border", "textAlign", "color", "fontSize", "display", "gap", "justifyContent", "flexWrap", "href", "textDecoration", "fontWeight", "options", "map", "index", "type", "name", "checked", "onChange", "includes", "e", "target", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/MainPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\nimport questionsData from './mcqs.json';\r\nimport PatternsSummary from './PatternsSummary';\r\n\r\nfunction MainPage() {\r\n  const [questions, setQuestions] = useState([]);\r\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\r\n  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState(null);\r\n  const [promptInputAnswers, setPromptInputAnswers] = useState({});\r\n  const [selectedPrompts, setSelectedPrompts] = useState([]);\r\n  const [report, setReport] = useState('');\r\n  const [selectedPatterns, setSelectedPatterns] = useState(new Set());\r\n  const [filteredQuestions, setFilteredQuestions] = useState([]);\r\n  const [showPatternSelector, setShowPatternSelector] = useState(false);\r\n\r\n  // Available patterns for selection\r\n  const availablePatterns = [\r\n    \"1. Two Pointers\",\r\n    \"2. Merge Intervals\",\r\n    \"3. Sliding Window\",\r\n    \"4. Binary Search\",\r\n    \"5. Tree Traversal\",\r\n    \"6. Dynamic Programming (1D)\",\r\n    \"7. Fast & Slow Pointers\",\r\n    \"8. Graph Algorithms\",\r\n    \"Island Pattern\",\r\n    \"Tree BFS\",\r\n    \"Tree DFS\",\r\n    \"Heap\",\r\n    \"Modified Binary Search\",\r\n    \"Subsets\",\r\n    \"Greedy Algorithm\",\r\n    \"0/1 Knapsack\"\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const allQuestions = Object.values(questionsData).flat();\r\n    setQuestions(allQuestions);\r\n    setFilteredQuestions(allQuestions);\r\n\r\n    const savedIndex = localStorage.getItem('currentQuestionIndex');\r\n    if (savedIndex !== null) {\r\n      setCurrentQuestionIndex(parseInt(savedIndex, 10));\r\n    }\r\n  }, []);\r\n\r\n  // Filter questions based on selected patterns\r\n  useEffect(() => {\r\n    if (selectedPatterns.size === 0) {\r\n      const allQuestions = Object.values(questionsData).flat();\r\n      setFilteredQuestions(allQuestions);\r\n    } else {\r\n      const filtered = [];\r\n      selectedPatterns.forEach(pattern => {\r\n        if (questionsData[pattern]) {\r\n          filtered.push(...questionsData[pattern]);\r\n        }\r\n      });\r\n      setFilteredQuestions(filtered);\r\n      setCurrentQuestionIndex(0); // Reset to first question when filtering\r\n    }\r\n  }, [selectedPatterns]);\r\n\r\n  useEffect(() => {\r\n    if (filteredQuestions.length > 0) {\r\n      localStorage.setItem('currentQuestionIndex', currentQuestionIndex);\r\n    }\r\n  }, [currentQuestionIndex, filteredQuestions.length]);\r\n\r\n  // Pattern selection handlers\r\n  const handlePatternToggle = (pattern) => {\r\n    const newSelected = new Set(selectedPatterns);\r\n    if (newSelected.has(pattern)) {\r\n      newSelected.delete(pattern);\r\n    } else {\r\n      newSelected.add(pattern);\r\n    }\r\n    setSelectedPatterns(newSelected);\r\n  };\r\n\r\n  const selectAllPatterns = () => {\r\n    setSelectedPatterns(new Set(availablePatterns));\r\n  };\r\n\r\n  const clearAllPatterns = () => {\r\n    setSelectedPatterns(new Set());\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentQuestionIndex < questions.length - 1) {\r\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\r\n      setSelectedCorrectAnswer(null);\r\n      setPromptInputAnswers({});\r\n      setSelectedPrompts([]);\r\n      setReport('');\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentQuestionIndex > 0) {\r\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\r\n      setSelectedCorrectAnswer(null);\r\n      setPromptInputAnswers({});\r\n      setSelectedPrompts([]);\r\n      setReport('');\r\n    }\r\n  };\r\n\r\n  const handleCorrectAnswerChange = (option) => {\r\n    setSelectedCorrectAnswer(option);\r\n  };\r\n\r\n  const handlePromptInputChange = (option) => {\r\n    setPromptInputAnswers({\r\n      ...promptInputAnswers,\r\n      [option]: !promptInputAnswers[option],\r\n    });\r\n  };\r\n\r\n  const handlePromptChange = (prompt, isChecked) => {\r\n    if (isChecked) {\r\n      setSelectedPrompts(prev => [...prev, prompt]);\r\n    } else {\r\n      setSelectedPrompts(prev => prev.filter(p => p !== prompt));\r\n    }\r\n  };\r\n\r\n  const handleGenerateReport = async () => {\r\n    const reportData = {\r\n      question: questions[currentQuestionIndex].question,\r\n      correctAnswer: selectedCorrectAnswer,\r\n      promptInputAnswers: Object.keys(promptInputAnswers).filter(\r\n        (answer) => promptInputAnswers[answer]\r\n      ),\r\n      selectedPrompts: selectedPrompts,\r\n    };\r\n\r\n    try {\r\n      const response = await fetch('http://localhost:3001/generate-report', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(reportData),\r\n      });\r\n      const data = await response.json();\r\n      setReport(data.report);\r\n    } catch (error) {\r\n      console.error('Error generating report:', error);\r\n    }\r\n  };\r\n\r\n  if (questions.length === 0) {\r\n    return <div>Loading...</div>;\r\n  }\r\n\r\n  const currentQuestion = questions[currentQuestionIndex];\r\n  const prompts = [\r\n    'can you design a solution in c# that will provide an output for the selected answer',\r\n    'write a program thats based on the choice',\r\n    'what is the gap between selected items',\r\n    'what is the consequence if I\\'m correct for the given choice based on the question',\r\n    'rephrase the question based on my choice',\r\n    'what is wrong with this implementation',\r\n    'what if my syntasx is correct',\r\n  ];\r\n\r\n  return (\r\n    <div className=\"page-content\">\r\n      {/* New Features Highlight */}\r\n      <div style={{\r\n        backgroundColor: '#e8f5e8',\r\n        padding: '20px',\r\n        margin: '20px 0',\r\n        borderRadius: '8px',\r\n        border: '2px solid #27ae60',\r\n        textAlign: 'center'\r\n      }}>\r\n        <h2 style={{ color: '#27ae60', margin: '0 0 10px 0' }}>\r\n          🎯 NEW: Complete Coding Patterns Guide Available!\r\n        </h2>\r\n        <p style={{ margin: '0 0 15px 0', fontSize: '16px' }}>\r\n          Master all 16 essential coding patterns for technical interviews with step-by-step implementations and interactive demos\r\n        </p>\r\n        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>\r\n          <a\r\n            href=\"/patterns\"\r\n            style={{\r\n              display: 'inline-block',\r\n              padding: '10px 20px',\r\n              backgroundColor: '#27ae60',\r\n              color: 'white',\r\n              textDecoration: 'none',\r\n              borderRadius: '5px',\r\n              fontWeight: 'bold'\r\n            }}\r\n          >\r\n            🎯 Coding Patterns Guide →\r\n          </a>\r\n          <a\r\n            href=\"/cqrs\"\r\n            style={{\r\n              display: 'inline-block',\r\n              padding: '10px 20px',\r\n              backgroundColor: '#3498db',\r\n              color: 'white',\r\n              textDecoration: 'none',\r\n              borderRadius: '5px',\r\n              fontWeight: 'bold'\r\n            }}\r\n          >\r\n            🏗️ CQRS Pattern →\r\n          </a>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Patterns Summary Section */}\r\n      <PatternsSummary />\r\n\r\n      <div className=\"question-container\">\r\n        <h2>{currentQuestion.question}</h2>\r\n        <div className=\"options-container\">\r\n          {currentQuestion.options.map((option, index) => (\r\n            <div key={index} className=\"option\">\r\n              <input\r\n                type=\"radio\"\r\n                name={`question-${currentQuestionIndex}`}\r\n                checked={selectedCorrectAnswer === option}\r\n                onChange={() => handleCorrectAnswerChange(option)}\r\n              />\r\n              <label>{option}</label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={!!promptInputAnswers[option]}\r\n                onChange={() => handlePromptInputChange(option)}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"prompts-container\">\r\n          <h3>Follow-up Prompts:</h3>\r\n          {prompts.map((prompt, index) => (\r\n            <div key={index} className=\"prompt\">\r\n              <label>{prompt}</label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={selectedPrompts.includes(prompt)}\r\n                onChange={(e) => handlePromptChange(prompt, e.target.checked)}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <button onClick={handleGenerateReport}>Generate Report</button>\r\n        {report && (\r\n          <div className=\"report-container\">\r\n            <h3>Consolidated Report:</h3>\r\n            <pre>{report}</pre>\r\n          </div>\r\n        )}\r\n        <div className=\"navigation-buttons\">\r\n          <button onClick={handlePrevious} disabled={currentQuestionIndex === 0}>\r\n            Previous\r\n          </button>\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentQuestionIndex === questions.length - 1}\r\n          >\r\n            Next\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default MainPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAClB,OAAOC,aAAa,MAAM,aAAa;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACY,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,IAAIsB,GAAG,CAAC,CAAC,CAAC;EACnE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM2B,iBAAiB,GAAG,CACxB,iBAAiB,EACjB,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,6BAA6B,EAC7B,yBAAyB,EACzB,qBAAqB,EACrB,gBAAgB,EAChB,UAAU,EACV,UAAU,EACV,MAAM,EACN,wBAAwB,EACxB,SAAS,EACT,kBAAkB,EAClB,cAAc,CACf;EAED1B,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC5B,aAAa,CAAC,CAAC6B,IAAI,CAAC,CAAC;IACxDtB,YAAY,CAACmB,YAAY,CAAC;IAC1BJ,oBAAoB,CAACI,YAAY,CAAC;IAElC,MAAMI,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IAC/D,IAAIF,UAAU,KAAK,IAAI,EAAE;MACvBrB,uBAAuB,CAACwB,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/B,SAAS,CAAC,MAAM;IACd,IAAImB,gBAAgB,CAACgB,IAAI,KAAK,CAAC,EAAE;MAC/B,MAAMR,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC5B,aAAa,CAAC,CAAC6B,IAAI,CAAC,CAAC;MACxDP,oBAAoB,CAACI,YAAY,CAAC;IACpC,CAAC,MAAM;MACL,MAAMS,QAAQ,GAAG,EAAE;MACnBjB,gBAAgB,CAACkB,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIrC,aAAa,CAACqC,OAAO,CAAC,EAAE;UAC1BF,QAAQ,CAACG,IAAI,CAAC,GAAGtC,aAAa,CAACqC,OAAO,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC;MACFf,oBAAoB,CAACa,QAAQ,CAAC;MAC9B1B,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAACS,gBAAgB,CAAC,CAAC;EAEtBnB,SAAS,CAAC,MAAM;IACd,IAAIsB,iBAAiB,CAACkB,MAAM,GAAG,CAAC,EAAE;MAChCR,YAAY,CAACS,OAAO,CAAC,sBAAsB,EAAEhC,oBAAoB,CAAC;IACpE;EACF,CAAC,EAAE,CAACA,oBAAoB,EAAEa,iBAAiB,CAACkB,MAAM,CAAC,CAAC;;EAEpD;EACA,MAAME,mBAAmB,GAAIJ,OAAO,IAAK;IACvC,MAAMK,WAAW,GAAG,IAAItB,GAAG,CAACF,gBAAgB,CAAC;IAC7C,IAAIwB,WAAW,CAACC,GAAG,CAACN,OAAO,CAAC,EAAE;MAC5BK,WAAW,CAACE,MAAM,CAACP,OAAO,CAAC;IAC7B,CAAC,MAAM;MACLK,WAAW,CAACG,GAAG,CAACR,OAAO,CAAC;IAC1B;IACAlB,mBAAmB,CAACuB,WAAW,CAAC;EAClC,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3B,mBAAmB,CAAC,IAAIC,GAAG,CAACK,iBAAiB,CAAC,CAAC;EACjD,CAAC;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5B,mBAAmB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAM4B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIxC,oBAAoB,GAAGF,SAAS,CAACiC,MAAM,GAAG,CAAC,EAAE;MAC/C9B,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;MACjDG,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMgC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIzC,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;MACjDG,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMiC,yBAAyB,GAAIC,MAAM,IAAK;IAC5CxC,wBAAwB,CAACwC,MAAM,CAAC;EAClC,CAAC;EAED,MAAMC,uBAAuB,GAAID,MAAM,IAAK;IAC1CtC,qBAAqB,CAAC;MACpB,GAAGD,kBAAkB;MACrB,CAACuC,MAAM,GAAG,CAACvC,kBAAkB,CAACuC,MAAM;IACtC,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;IAChD,IAAIA,SAAS,EAAE;MACbxC,kBAAkB,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,MAAM,CAAC,CAAC;IAC/C,CAAC,MAAM;MACLvC,kBAAkB,CAACyC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,MAAM,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAMK,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMC,UAAU,GAAG;MACjBC,QAAQ,EAAEvD,SAAS,CAACE,oBAAoB,CAAC,CAACqD,QAAQ;MAClDC,aAAa,EAAEpD,qBAAqB;MACpCE,kBAAkB,EAAEe,MAAM,CAACoC,IAAI,CAACnD,kBAAkB,CAAC,CAAC6C,MAAM,CACvDO,MAAM,IAAKpD,kBAAkB,CAACoD,MAAM,CACvC,CAAC;MACDlD,eAAe,EAAEA;IACnB,CAAC;IAED,IAAI;MACF,MAAMmD,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACX,UAAU;MACjC,CAAC,CAAC;MACF,MAAMY,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClCxD,SAAS,CAACuD,IAAI,CAACxD,MAAM,CAAC;IACxB,CAAC,CAAC,OAAO0D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,IAAIpE,SAAS,CAACiC,MAAM,KAAK,CAAC,EAAE;IAC1B,oBAAOpC,OAAA;MAAAyE,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9B;EAEA,MAAMC,eAAe,GAAG3E,SAAS,CAACE,oBAAoB,CAAC;EACvD,MAAM0E,OAAO,GAAG,CACd,qFAAqF,EACrF,2CAA2C,EAC3C,wCAAwC,EACxC,oFAAoF,EACpF,0CAA0C,EAC1C,wCAAwC,EACxC,+BAA+B,CAChC;EAED,oBACE/E,OAAA;IAAKgF,SAAS,EAAC,cAAc;IAAAP,QAAA,gBAE3BzE,OAAA;MAAKiF,KAAK,EAAE;QACVC,eAAe,EAAE,SAAS;QAC1BC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,QAAQ;QAChBC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE;MACb,CAAE;MAAAd,QAAA,gBACAzE,OAAA;QAAIiF,KAAK,EAAE;UAAEO,KAAK,EAAE,SAAS;UAAEJ,MAAM,EAAE;QAAa,CAAE;QAAAX,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL7E,OAAA;QAAGiF,KAAK,EAAE;UAAEG,MAAM,EAAE,YAAY;UAAEK,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ7E,OAAA;QAAKiF,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAApB,QAAA,gBACvFzE,OAAA;UACE8F,IAAI,EAAC,WAAW;UAChBb,KAAK,EAAE;YACLS,OAAO,EAAE,cAAc;YACvBP,OAAO,EAAE,WAAW;YACpBD,eAAe,EAAE,SAAS;YAC1BM,KAAK,EAAE,OAAO;YACdO,cAAc,EAAE,MAAM;YACtBV,YAAY,EAAE,KAAK;YACnBW,UAAU,EAAE;UACd,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7E,OAAA;UACE8F,IAAI,EAAC,OAAO;UACZb,KAAK,EAAE;YACLS,OAAO,EAAE,cAAc;YACvBP,OAAO,EAAE,WAAW;YACpBD,eAAe,EAAE,SAAS;YAC1BM,KAAK,EAAE,OAAO;YACdO,cAAc,EAAE,MAAM;YACtBV,YAAY,EAAE,KAAK;YACnBW,UAAU,EAAE;UACd,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA,CAACF,eAAe;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEnB7E,OAAA;MAAKgF,SAAS,EAAC,oBAAoB;MAAAP,QAAA,gBACjCzE,OAAA;QAAAyE,QAAA,EAAKK,eAAe,CAACpB;MAAQ;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnC7E,OAAA;QAAKgF,SAAS,EAAC,mBAAmB;QAAAP,QAAA,EAC/BK,eAAe,CAACmB,OAAO,CAACC,GAAG,CAAC,CAAClD,MAAM,EAAEmD,KAAK,kBACzCnG,OAAA;UAAiBgF,SAAS,EAAC,QAAQ;UAAAP,QAAA,gBACjCzE,OAAA;YACEoG,IAAI,EAAC,OAAO;YACZC,IAAI,EAAE,YAAYhG,oBAAoB,EAAG;YACzCiG,OAAO,EAAE/F,qBAAqB,KAAKyC,MAAO;YAC1CuD,QAAQ,EAAEA,CAAA,KAAMxD,yBAAyB,CAACC,MAAM;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACF7E,OAAA;YAAAyE,QAAA,EAAQzB;UAAM;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvB7E,OAAA;YACEoG,IAAI,EAAC,UAAU;YACfE,OAAO,EAAE,CAAC,CAAC7F,kBAAkB,CAACuC,MAAM,CAAE;YACtCuD,QAAQ,EAAEA,CAAA,KAAMtD,uBAAuB,CAACD,MAAM;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA,GAZMsB,KAAK;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN7E,OAAA;QAAKgF,SAAS,EAAC,mBAAmB;QAAAP,QAAA,gBAChCzE,OAAA;UAAAyE,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1BE,OAAO,CAACmB,GAAG,CAAC,CAAC/C,MAAM,EAAEgD,KAAK,kBACzBnG,OAAA;UAAiBgF,SAAS,EAAC,QAAQ;UAAAP,QAAA,gBACjCzE,OAAA;YAAAyE,QAAA,EAAQtB;UAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvB7E,OAAA;YACEoG,IAAI,EAAC,UAAU;YACfE,OAAO,EAAE3F,eAAe,CAAC6F,QAAQ,CAACrD,MAAM,CAAE;YAC1CoD,QAAQ,EAAGE,CAAC,IAAKvD,kBAAkB,CAACC,MAAM,EAAEsD,CAAC,CAACC,MAAM,CAACJ,OAAO;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA,GANMsB,KAAK;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN7E,OAAA;QAAQ2G,OAAO,EAAEnD,oBAAqB;QAAAiB,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC9DhE,MAAM,iBACLb,OAAA;QAAKgF,SAAS,EAAC,kBAAkB;QAAAP,QAAA,gBAC/BzE,OAAA;UAAAyE,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7B7E,OAAA;UAAAyE,QAAA,EAAM5D;QAAM;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACN,eACD7E,OAAA;QAAKgF,SAAS,EAAC,oBAAoB;QAAAP,QAAA,gBACjCzE,OAAA;UAAQ2G,OAAO,EAAE7D,cAAe;UAAC8D,QAAQ,EAAEvG,oBAAoB,KAAK,CAAE;UAAAoE,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7E,OAAA;UACE2G,OAAO,EAAE9D,UAAW;UACpB+D,QAAQ,EAAEvG,oBAAoB,KAAKF,SAAS,CAACiC,MAAM,GAAG,CAAE;UAAAqC,QAAA,EACzD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC3E,EAAA,CA7QQD,QAAQ;AAAA4G,EAAA,GAAR5G,QAAQ;AA+QjB,eAAeA,QAAQ;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}