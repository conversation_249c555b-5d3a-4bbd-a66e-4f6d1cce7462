import React, { useState } from 'react';

function TwoPointersPattern() {
  const [currentStep, setCurrentStep] = useState(0);
  const [demoArray, setDemoArray] = useState([1, 2, 3, 4, 5, 6, 7, 8, 9]);
  const [target, setTarget] = useState(10);
  const [leftPointer, setLeftPointer] = useState(0);
  const [rightPointer, setRightPointer] = useState(8);
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState(null);

  const steps = [
    {
      title: "🎯 Understanding Two Pointers Pattern",
      content: "The Two Pointers technique uses two pointers to traverse data structures, typically arrays or strings, to solve problems efficiently.",
      code: `// Two Pointers Template
function twoPointers(arr) {
    let left = 0;
    let right = arr.length - 1;
    
    while (left < right) {
        // Process current pair
        if (condition) {
            // Found solution
            return [left, right];
        } else if (needToMoveLeft) {
            left++;
        } else {
            right--;
        }
    }
    return null;
}`,
      learningPrompt: "Why use two pointers instead of nested loops?",
      answer: "Two pointers reduce time complexity from O(n²) to O(n) by eliminating the need for nested iterations."
    },
    {
      title: "📋 Step 1: Problem Analysis",
      content: "Identify if the problem can use two pointers: sorted array, finding pairs, palindrome check, or removing duplicates.",
      code: `// Two Sum in Sorted Array
function twoSum(numbers, target) {
    let left = 0;
    let right = numbers.length - 1;
    
    while (left < right) {
        const sum = numbers[left] + numbers[right];
        
        if (sum === target) {
            return [left, right];
        } else if (sum < target) {
            left++;  // Need larger sum
        } else {
            right--; // Need smaller sum
        }
    }
    return [];
}`,
      learningPrompt: "When should we move the left pointer vs right pointer?",
      answer: "Move left when we need a larger value, move right when we need a smaller value (in sorted arrays)."
    },
    {
      title: "🔧 Step 2: Initialize Pointers",
      content: "Set up pointers at appropriate positions - usually start and end for opposite direction movement.",
      code: `// Initialization patterns
// Pattern 1: Opposite ends
let left = 0, right = arr.length - 1;

// Pattern 2: Same start (fast/slow)
let slow = 0, fast = 0;

// Pattern 3: Sliding window
let left = 0, right = 0;`,
      learningPrompt: "What are the different ways to initialize two pointers?",
      answer: "1) Opposite ends (left=0, right=n-1), 2) Same start (slow=0, fast=0), 3) Sliding window (both start at 0)"
    },
    {
      title: "⚡ Step 3: Movement Logic",
      content: "Define the conditions for moving each pointer based on the problem requirements.",
      code: `// Movement strategies
while (left < right) {
    if (condition_met) {
        return result;
    }
    
    // Strategy 1: Based on comparison
    if (sum < target) left++;
    else right--;
    
    // Strategy 2: Always move one
    if (arr[left] === arr[right]) {
        left++; right--;
    }
    
    // Strategy 3: Conditional movement
    if (isValid(left, right)) {
        process(left, right);
    }
    movePointers();
}`,
      learningPrompt: "How do we decide which pointer to move?",
      answer: "Base the decision on problem logic: comparison results, validity checks, or alternating movement patterns."
    },
    {
      title: "🎮 Step 4: Interactive Demo",
      content: "Watch the two pointers algorithm in action with a live demonstration.",
      code: `// Live Demo: Two Sum
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9];
const target = 10;

// Current state:
// Left pointer: ${leftPointer} (value: ${demoArray[leftPointer]})
// Right pointer: ${rightPointer} (value: ${demoArray[rightPointer]})
// Sum: ${demoArray[leftPointer] + demoArray[rightPointer]}`,
      learningPrompt: "What happens when sum equals target?",
      answer: "We found our answer! Return the indices or values of the two pointers."
    },
    {
      title: "🏆 Step 5: Common Variations",
      content: "Master different two-pointer patterns for various problem types.",
      code: `// Variation 1: Remove Duplicates
function removeDuplicates(arr) {
    let writeIndex = 1;
    for (let readIndex = 1; readIndex < arr.length; readIndex++) {
        if (arr[readIndex] !== arr[readIndex - 1]) {
            arr[writeIndex] = arr[readIndex];
            writeIndex++;
        }
    }
    return writeIndex;
}

// Variation 2: Palindrome Check
function isPalindrome(s) {
    let left = 0, right = s.length - 1;
    while (left < right) {
        if (s[left] !== s[right]) return false;
        left++; right--;
    }
    return true;
}`,
      learningPrompt: "What are the main variations of two pointers?",
      answer: "1) Opposite direction (palindrome), 2) Same direction (remove duplicates), 3) Fast/slow (cycle detection)"
    }
  ];

  const runDemo = async () => {
    setIsRunning(true);
    setResult(null);
    let left = 0;
    let right = demoArray.length - 1;

    while (left < right) {
      setLeftPointer(left);
      setRightPointer(right);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const sum = demoArray[left] + demoArray[right];
      
      if (sum === target) {
        setResult(`Found! ${demoArray[left]} + ${demoArray[right]} = ${target}`);
        setIsRunning(false);
        return;
      } else if (sum < target) {
        left++;
      } else {
        right--;
      }
    }
    
    setResult("No solution found");
    setIsRunning(false);
  };

  const resetDemo = () => {
    setLeftPointer(0);
    setRightPointer(demoArray.length - 1);
    setResult(null);
    setIsRunning(false);
  };

  return (
    <div className="page-content" style={{ fontFamily: 'Arial, sans-serif' }}>
      {/* Header */}
      <div style={{ textAlign: 'center', marginBottom: '30px' }}>
        <h1 style={{ color: '#27ae60', fontSize: '2.5em', marginBottom: '10px' }}>
          🎯 Two Pointers Pattern
        </h1>
        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>
          Master the two pointers technique to solve array and string problems efficiently
        </p>
      </div>

      {/* Progress Bar */}
      <div style={{ marginBottom: '30px' }}>
        <div style={{
          backgroundColor: '#ecf0f1',
          borderRadius: '10px',
          height: '8px',
          overflow: 'hidden'
        }}>
          <div style={{
            backgroundColor: '#27ae60',
            height: '100%',
            width: `${((currentStep + 1) / steps.length) * 100}%`,
            transition: 'width 0.3s ease'
          }}></div>
        </div>
        <p style={{ textAlign: 'center', margin: '10px 0', color: '#7f8c8d' }}>
          Step {currentStep + 1} of {steps.length}
        </p>
      </div>

      {/* Navigation */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        gap: '10px', 
        marginBottom: '30px',
        flexWrap: 'wrap'
      }}>
        {steps.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentStep(index)}
            style={{
              padding: '8px 16px',
              border: currentStep === index ? '2px solid #27ae60' : '1px solid #ddd',
              borderRadius: '20px',
              backgroundColor: currentStep === index ? '#27ae60' : 'white',
              color: currentStep === index ? 'white' : '#333',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            {index + 1}
          </button>
        ))}
      </div>

      {/* Current Step Content */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        padding: '30px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
        marginBottom: '30px'
      }}>
        <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>
          {steps[currentStep].title}
        </h2>
        
        <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>
          {steps[currentStep].content}
        </p>

        {/* Code Block */}
        <div style={{
          backgroundColor: '#f8f9fa',
          border: '1px solid #e9ecef',
          borderRadius: '8px',
          padding: '20px',
          marginBottom: '20px',
          fontFamily: 'Monaco, Consolas, monospace',
          fontSize: '14px',
          overflow: 'auto'
        }}>
          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
            {steps[currentStep].code}
          </pre>
        </div>

        {/* Interactive Demo for Step 4 */}
        {currentStep === 4 && (
          <div style={{
            backgroundColor: '#f0f8ff',
            border: '2px solid #27ae60',
            borderRadius: '12px',
            padding: '20px',
            marginBottom: '20px'
          }}>
            <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>🎮 Live Demo</h3>
            
            {/* Array Visualization */}
            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', marginBottom: '10px' }}>
                {demoArray.map((num, index) => (
                  <div
                    key={index}
                    style={{
                      width: '40px',
                      height: '40px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 
                        index === leftPointer ? '#e74c3c' :
                        index === rightPointer ? '#3498db' : '#ecf0f1',
                      color: (index === leftPointer || index === rightPointer) ? 'white' : '#333',
                      borderRadius: '8px',
                      fontWeight: 'bold',
                      border: '2px solid',
                      borderColor: 
                        index === leftPointer ? '#c0392b' :
                        index === rightPointer ? '#2980b9' : '#bdc3c7'
                    }}
                  >
                    {num}
                  </div>
                ))}
              </div>
              
              <div style={{ textAlign: 'center', fontSize: '14px', color: '#666' }}>
                <span style={{ color: '#e74c3c' }}>🔴 Left: {leftPointer}</span>
                {' | '}
                <span style={{ color: '#3498db' }}>🔵 Right: {rightPointer}</span>
                {' | '}
                <span>Target: {target}</span>
                {' | '}
                <span>Sum: {demoArray[leftPointer] + demoArray[rightPointer]}</span>
              </div>
            </div>

            {/* Controls */}
            <div style={{ textAlign: 'center', gap: '10px', display: 'flex', justifyContent: 'center' }}>
              <button
                onClick={runDemo}
                disabled={isRunning}
                style={{
                  backgroundColor: '#27ae60',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '6px',
                  cursor: isRunning ? 'not-allowed' : 'pointer',
                  opacity: isRunning ? 0.6 : 1
                }}
              >
                {isRunning ? '🔄 Running...' : '▶️ Run Demo'}
              </button>
              
              <button
                onClick={resetDemo}
                style={{
                  backgroundColor: '#95a5a6',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                🔄 Reset
              </button>
            </div>

            {result && (
              <div style={{
                marginTop: '15px',
                padding: '10px',
                backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',
                border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,
                borderRadius: '6px',
                textAlign: 'center',
                fontWeight: 'bold',
                color: result.includes('Found') ? '#155724' : '#721c24'
              }}>
                {result}
              </div>
            )}
          </div>
        )}

        {/* Learning Prompt */}
        <div style={{
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '8px',
          padding: '15px',
          marginBottom: '20px'
        }}>
          <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>
            🤔 Learning Prompt:
          </h4>
          <p style={{ color: '#856404', margin: '0 0 10px 0', fontStyle: 'italic' }}>
            {steps[currentStep].learningPrompt}
          </p>
          <details>
            <summary style={{ color: '#856404', cursor: 'pointer', fontWeight: 'bold' }}>
              💡 Click to see answer
            </summary>
            <p style={{ color: '#856404', margin: '10px 0 0 0', paddingLeft: '20px' }}>
              {steps[currentStep].answer}
            </p>
          </details>
        </div>

        {/* Navigation Buttons */}
        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '30px' }}>
          <button
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
            style={{
              backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '6px',
              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'
            }}
          >
            ← Previous
          </button>
          
          <button
            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}
            disabled={currentStep === steps.length - 1}
            style={{
              backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#27ae60',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '6px',
              cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'
            }}
          >
            Next →
          </button>
        </div>
      </div>

      {/* Practice Problems */}
      <div style={{
        backgroundColor: '#f8f9fa',
        borderRadius: '12px',
        padding: '25px',
        border: '2px solid #27ae60'
      }}>
        <h3 style={{ color: '#27ae60', marginBottom: '20px' }}>
          🎯 Practice Problems
        </h3>
        
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
          {[
            { name: "Two Sum", difficulty: "Easy", time: "15 min" },
            { name: "Three Sum", difficulty: "Medium", time: "25 min" },
            { name: "Remove Duplicates", difficulty: "Easy", time: "10 min" },
            { name: "Container With Most Water", difficulty: "Medium", time: "20 min" },
            { name: "Valid Palindrome", difficulty: "Easy", time: "15 min" },
            { name: "Trapping Rain Water", difficulty: "Hard", time: "35 min" }
          ].map((problem, index) => (
            <div
              key={index}
              style={{
                backgroundColor: 'white',
                padding: '15px',
                borderRadius: '8px',
                border: '1px solid #dee2e6'
              }}
            >
              <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0' }}>{problem.name}</h4>
              <div style={{ fontSize: '14px', color: '#666' }}>
                <span style={{ 
                  color: problem.difficulty === 'Easy' ? '#27ae60' : 
                        problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c' 
                }}>
                  {problem.difficulty}
                </span>
                {' • '}
                <span>{problem.time}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default TwoPointersPattern;
