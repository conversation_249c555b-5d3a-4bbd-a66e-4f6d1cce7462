{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';\nimport MainPage from './MainPage';\nimport History from './History';\nimport CQRSPattern from './CQRSPattern';\nimport CodingPatternsGuide from './CodingPatternsGuide';\nimport TwoPointersPattern from './patterns/TwoPointersPattern';\nimport SlidingWindowPattern from './patterns/SlidingWindowPattern';\nimport BinarySearchPattern from './patterns/BinarySearchPattern';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/history\",\n              children: \"Today's History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/cqrs\",\n              children: \"CQRS Pattern\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/patterns\",\n              children: \"Coding Patterns\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(MainPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/history\",\n          element: /*#__PURE__*/_jsxDEV(History, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cqrs\",\n          element: /*#__PURE__*/_jsxDEV(CQRSPattern, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/patterns\",\n          element: /*#__PURE__*/_jsxDEV(CodingPatternsGuide, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/patterns/two-pointers\",\n          element: /*#__PURE__*/_jsxDEV(TwoPointersPattern, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/patterns/sliding-window\",\n          element: /*#__PURE__*/_jsxDEV(SlidingWindowPattern, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/patterns/binary-search\",\n          element: /*#__PURE__*/_jsxDEV(BinarySearchPattern, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "Link", "MainPage", "History", "CQRSPattern", "CodingPatternsGuide", "TwoPointersPattern", "SlidingWindowPattern", "BinarySearchPattern", "jsxDEV", "_jsxDEV", "App", "children", "className", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';\nimport MainPage from './MainPage';\nimport History from './History';\nimport CQRSPattern from './CQRSPattern';\nimport CodingPatternsGuide from './CodingPatternsGuide';\nimport TwoPointersPattern from './patterns/TwoPointersPattern';\nimport SlidingWindowPattern from './patterns/SlidingWindowPattern';\nimport BinarySearchPattern from './patterns/BinarySearchPattern';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"App\">\n        <nav>\n          <ul>\n            <li>\n              <Link to=\"/\">Home</Link>\n            </li>\n            <li>\n              <Link to=\"/history\">Today's History</Link>\n            </li>\n            <li>\n              <Link to=\"/cqrs\">CQRS Pattern</Link>\n            </li>\n            <li>\n              <Link to=\"/patterns\">Coding Patterns</Link>\n            </li>\n          </ul>\n        </nav>\n        <Routes>\n          <Route path=\"/\" element={<MainPage />} />\n          <Route path=\"/history\" element={<History />} />\n          <Route path=\"/cqrs\" element={<CQRSPattern />} />\n          <Route path=\"/patterns\" element={<CodingPatternsGuide />} />\n          <Route path=\"/patterns/two-pointers\" element={<TwoPointersPattern />} />\n          <Route path=\"/patterns/sliding-window\" element={<SlidingWindowPattern />} />\n          <Route path=\"/patterns/binary-search\" element={<BinarySearchPattern />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACZ,MAAM;IAAAc,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAAAD,QAAA,gBAClBF,OAAA;QAAAE,QAAA,eACEF,OAAA;UAAAE,QAAA,gBACEF,OAAA;YAAAE,QAAA,eACEF,OAAA,CAACT,IAAI;cAACa,EAAE,EAAC,GAAG;cAAAF,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACLR,OAAA;YAAAE,QAAA,eACEF,OAAA,CAACT,IAAI;cAACa,EAAE,EAAC,UAAU;cAAAF,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACLR,OAAA;YAAAE,QAAA,eACEF,OAAA,CAACT,IAAI;cAACa,EAAE,EAAC,OAAO;cAAAF,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACLR,OAAA;YAAAE,QAAA,eACEF,OAAA,CAACT,IAAI;cAACa,EAAE,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNR,OAAA,CAACV,MAAM;QAAAY,QAAA,gBACLF,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEV,OAAA,CAACR,QAAQ;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCR,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEV,OAAA,CAACP,OAAO;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CR,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEV,OAAA,CAACN,WAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDR,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEV,OAAA,CAACL,mBAAmB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DR,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,wBAAwB;UAACC,OAAO,eAAEV,OAAA,CAACJ,kBAAkB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxER,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAEV,OAAA,CAACH,oBAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5ER,OAAA,CAACX,KAAK;UAACoB,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAEV,OAAA,CAACF,mBAAmB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACG,EAAA,GAhCQV,GAAG;AAkCZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}