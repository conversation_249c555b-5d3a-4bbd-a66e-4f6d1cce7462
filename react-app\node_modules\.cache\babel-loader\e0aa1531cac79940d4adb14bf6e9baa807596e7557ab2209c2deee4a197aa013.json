{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\patterns\\\\SlidingWindowPattern.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SlidingWindowPattern() {\n  _s();\n  var _patternMCQs$currentM, _patternMCQs$currentM2;\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([2, 1, 2, 3, 4, 1, 2, 1]);\n  const [windowSize, setWindowSize] = useState(3);\n  const [windowStart, setWindowStart] = useState(0);\n  const [windowEnd, setWindowEnd] = useState(2);\n  const [isRunning, setIsRunning] = useState(false);\n  const [currentSum, setCurrentSum] = useState(5);\n  const [maxSum, setMaxSum] = useState(5);\n  const [currentMCQIndex, setCurrentMCQIndex] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState('');\n  const [showMCQResult, setShowMCQResult] = useState(false);\n  const [mcqScore, setMCQScore] = useState(0);\n  const [completedMCQs, setCompletedMCQs] = useState(new Set());\n  const [followUpAnswers, setFollowUpAnswers] = useState({});\n\n  // Get MCQs for Sliding Window pattern\n  const patternMCQs = questionsData[\"3. Sliding Window\"] || [];\n  const steps = [{\n    title: \"🪟 Understanding Sliding Window Pattern\",\n    content: \"The Sliding Window technique maintains a subset of data (window) that slides through the array to solve subarray/substring problems efficiently.\",\n    code: `// Sliding Window Template\nfunction slidingWindow(arr, k) {\n    let windowSum = 0;\n    let maxSum = 0;\n    \n    // Calculate sum of first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    maxSum = windowSum;\n    \n    // Slide the window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}`,\n    learningPrompt: \"Why is sliding window more efficient than recalculating each subarray?\",\n    answer: \"Instead of recalculating the entire sum (O(k) for each position), we just remove the leftmost element and add the new rightmost element (O(1) per slide).\"\n  }, {\n    title: \"📏 Step 1: Fixed vs Variable Window\",\n    content: \"Identify whether you need a fixed-size window or a variable-size window based on the problem requirements.\",\n    code: `// Fixed Window (size k)\nfunction maxSumSubarray(arr, k) {\n    let windowSum = 0;\n    \n    // Initial window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}\n\n// Variable Window\nfunction longestSubstringKDistinct(s, k) {\n    let left = 0, maxLength = 0;\n    let charCount = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        charCount.set(s[right], (charCount.get(s[right]) || 0) + 1);\n        \n        while (charCount.size > k) {\n            charCount.set(s[left], charCount.get(s[left]) - 1);\n            if (charCount.get(s[left]) === 0) {\n                charCount.delete(s[left]);\n            }\n            left++;\n        }\n        \n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}`,\n    learningPrompt: \"When do we use fixed vs variable window size?\",\n    answer: \"Fixed window: when problem specifies exact size (e.g., 'subarray of size k'). Variable window: when we need to find optimal size based on conditions.\"\n  }, {\n    title: \"🎯 Step 2: Initialize Window\",\n    content: \"Set up the initial window and calculate the initial state (sum, count, etc.).\",\n    code: `// Step 2: Window Initialization\nfunction initializeWindow(arr, k) {\n    let windowSum = 0;\n    let windowStart = 0;\n    let windowEnd = k - 1;\n    \n    // Calculate initial window sum\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    console.log(\\`Initial window: [\\${windowStart}, \\${windowEnd}]\\`);\n    console.log(\\`Initial sum: \\${windowSum}\\`);\n    \n    return { windowSum, windowStart, windowEnd };\n}\n\n// For variable window\nfunction initializeVariableWindow() {\n    let left = 0;\n    let right = 0;\n    let windowState = new Map(); // or other data structure\n    \n    return { left, right, windowState };\n}`,\n    learningPrompt: \"What should we track in the window state?\",\n    answer: \"Depends on problem: sum (for max sum), character counts (for substring), frequency maps, or custom conditions.\"\n  }, {\n    title: \"➡️ Step 3: Slide the Window\",\n    content: \"Move the window by removing the leftmost element and adding the new rightmost element.\",\n    code: `// Step 3: Window Sliding Logic\nfunction slideWindow(arr, k) {\n    let windowSum = 0;\n    \n    // Initialize first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide the window\n    for (let windowEnd = k; windowEnd < arr.length; windowEnd++) {\n        // Add new element to window\n        windowSum += arr[windowEnd];\n        \n        // Remove element going out of window\n        windowSum -= arr[windowEnd - k];\n        \n        // Update result\n        maxSum = Math.max(maxSum, windowSum);\n        \n        console.log(\\`Window: [\\${windowEnd - k + 1}, \\${windowEnd}], Sum: \\${windowSum}\\`);\n    }\n    \n    return maxSum;\n}`,\n    learningPrompt: \"What's the key insight in the sliding operation?\",\n    answer: \"We maintain the window size by simultaneously adding one element and removing another, keeping the operation O(1) per slide.\"\n  }, {\n    title: \"🎮 Step 4: Interactive Demo\",\n    content: \"Watch the sliding window algorithm find the maximum sum subarray of size k.\",\n    code: `// Live Demo: Maximum Sum Subarray\nconst arr = [2, 1, 2, 3, 4, 1, 2, 1];\nconst k = 3;\n\n// Current window: [${windowStart}, ${windowEnd}]\n// Window elements: [${demoArray.slice(windowStart, windowEnd + 1).join(', ')}]\n// Current sum: ${currentSum}\n// Maximum sum so far: ${maxSum}`,\n    learningPrompt: \"How does the window maintain its size while sliding?\",\n    answer: \"By adding the new element at the right and removing the old element at the left simultaneously.\"\n  }, {\n    title: \"🔄 Step 5: Variable Window Patterns\",\n    content: \"Learn how to handle variable-size windows that expand and contract based on conditions.\",\n    code: `// Variable Window: Longest Substring Without Repeating Characters\nfunction lengthOfLongestSubstring(s) {\n    let left = 0;\n    let maxLength = 0;\n    let charSet = new Set();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        while (charSet.has(s[right])) {\n            charSet.delete(s[left]);\n            left++;\n        }\n        \n        charSet.add(s[right]);\n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}\n\n// Variable Window: Minimum Window Substring\nfunction minWindow(s, t) {\n    let left = 0, minLen = Infinity, minStart = 0;\n    let required = new Map();\n    let formed = 0;\n    \n    // Count characters in t\n    for (let char of t) {\n        required.set(char, (required.get(char) || 0) + 1);\n    }\n    \n    let windowCounts = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        let char = s[right];\n        windowCounts.set(char, (windowCounts.get(char) || 0) + 1);\n        \n        if (required.has(char) && windowCounts.get(char) === required.get(char)) {\n            formed++;\n        }\n        \n        // Contract window\n        while (left <= right && formed === required.size) {\n            if (right - left + 1 < minLen) {\n                minLen = right - left + 1;\n                minStart = left;\n            }\n            \n            let leftChar = s[left];\n            windowCounts.set(leftChar, windowCounts.get(leftChar) - 1);\n            if (required.has(leftChar) && windowCounts.get(leftChar) < required.get(leftChar)) {\n                formed--;\n            }\n            left++;\n        }\n    }\n    \n    return minLen === Infinity ? \"\" : s.substring(minStart, minStart + minLen);\n}`,\n    learningPrompt: \"When do we expand vs contract a variable window?\",\n    answer: \"Expand when we haven't met the condition yet. Contract when we've met the condition and want to find the minimum valid window.\"\n  }];\n  const runDemo = async () => {\n    setIsRunning(true);\n    let start = 0;\n    let sum = demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0);\n    let max = sum;\n    setWindowStart(start);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(sum);\n    setMaxSum(max);\n    await new Promise(resolve => setTimeout(resolve, 1500));\n    for (let end = windowSize; end < demoArray.length; end++) {\n      // Slide window\n      sum = sum - demoArray[start] + demoArray[end];\n      start++;\n      max = Math.max(max, sum);\n      setWindowStart(start);\n      setWindowEnd(end);\n      setCurrentSum(sum);\n      setMaxSum(max);\n      await new Promise(resolve => setTimeout(resolve, 1500));\n    }\n    setIsRunning(false);\n  };\n  const resetDemo = () => {\n    setWindowStart(0);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setMaxSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setIsRunning(false);\n  };\n\n  // MCQ handling functions\n  const handleMCQAnswer = answer => {\n    setSelectedAnswer(answer);\n    setShowMCQResult(true);\n    const currentMCQ = patternMCQs[currentMCQIndex];\n    if (answer === currentMCQ.answer) {\n      setMCQScore(prev => prev + 1);\n    }\n    setCompletedMCQs(prev => new Set([...prev, currentMCQIndex]));\n  };\n  const nextMCQ = () => {\n    if (currentMCQIndex < patternMCQs.length - 1) {\n      setCurrentMCQIndex(prev => prev + 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n  const previousMCQ = () => {\n    if (currentMCQIndex > 0) {\n      setCurrentMCQIndex(prev => prev - 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n  const handleFollowUpAnswer = (prompt, answer) => {\n    setFollowUpAnswers(prev => ({\n      ...prev,\n      [prompt]: answer\n    }));\n  };\n\n  // Follow-up prompts specific to Sliding Window\n  const followUpPrompts = [\"Explain the difference between fixed-size and variable-size sliding windows.\", \"How would you identify if a problem requires a sliding window approach?\", \"What are the key optimization benefits of using sliding window over brute force?\", \"Describe a scenario where you would use a sliding window with a HashMap.\", \"How do you handle edge cases in sliding window problems (empty arrays, single elements)?\"];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    style: {\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#3498db',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83E\\uDE9F Sliding Window Pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em',\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: \"Master the sliding window technique to optimize subarray and substring problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#3498db',\n            height: '100%',\n            width: `${(currentStep + 1) / steps.length * 100}%`,\n            transition: 'width 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0',\n          color: '#7f8c8d'\n        },\n        children: [\"Step \", currentStep + 1, \" of \", steps.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '10px',\n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      },\n      children: steps.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentStep(index),\n        style: {\n          padding: '8px 16px',\n          border: currentStep === index ? '2px solid #3498db' : '1px solid #ddd',\n          borderRadius: '20px',\n          backgroundColor: currentStep === index ? '#3498db' : 'white',\n          color: currentStep === index ? 'white' : '#333',\n          cursor: 'pointer',\n          fontSize: '14px'\n        },\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#2c3e50',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          lineHeight: '1.6',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            margin: 0,\n            whiteSpace: 'pre-wrap'\n          },\n          children: steps[currentStep].code\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f0f8ff',\n          border: '2px solid #3498db',\n          borderRadius: '12px',\n          padding: '20px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#3498db',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83C\\uDFAE Live Demo: Maximum Sum Subarray\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '5px',\n              justifyContent: 'center',\n              marginBottom: '10px'\n            },\n            children: demoArray.map((num, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '40px',\n                height: '40px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: index >= windowStart && index <= windowEnd ? '#3498db' : '#ecf0f1',\n                color: index >= windowStart && index <= windowEnd ? 'white' : '#333',\n                borderRadius: '8px',\n                fontWeight: 'bold',\n                border: '2px solid',\n                borderColor: index >= windowStart && index <= windowEnd ? '#2980b9' : '#bdc3c7',\n                position: 'relative'\n              },\n              children: [num, index === windowStart && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-25px',\n                  fontSize: '12px',\n                  color: '#e74c3c',\n                  fontWeight: 'bold'\n                },\n                children: \"Start\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 23\n              }, this), index === windowEnd && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-25px',\n                  fontSize: '12px',\n                  color: '#e74c3c',\n                  fontWeight: 'bold'\n                },\n                children: \"End\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Window Size: \", windowSize]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Current Sum: \", currentSum]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#27ae60',\n                fontWeight: 'bold'\n              },\n              children: [\"Max Sum: \", maxSum]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            gap: '10px',\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: runDemo,\n            disabled: isRunning,\n            style: {\n              backgroundColor: '#3498db',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: isRunning ? 'not-allowed' : 'pointer',\n              opacity: isRunning ? 0.6 : 1\n            },\n            children: isRunning ? '🔄 Sliding...' : '▶️ Run Demo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetDemo,\n            style: {\n              backgroundColor: '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: \"\\uD83D\\uDD04 Reset\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0'\n          },\n          children: \"\\uD83E\\uDD14 Learning Prompt:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0',\n            fontStyle: 'italic'\n          },\n          children: steps[currentStep].learningPrompt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            style: {\n              color: '#856404',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCA1 Click to see answer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#856404',\n              margin: '10px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: steps[currentStep].answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginTop: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.max(0, currentStep - 1)),\n          disabled: currentStep === 0,\n          style: {\n            backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.min(steps.length - 1, currentStep + 1)),\n          disabled: currentStep === steps.length - 1,\n          style: {\n            backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#3498db',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #3498db'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#3498db',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83C\\uDFAF Practice Problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '15px'\n        },\n        children: [{\n          name: \"Maximum Sum Subarray of Size K\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Longest Substring Without Repeating\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Minimum Window Substring\",\n          difficulty: \"Hard\",\n          time: \"40 min\"\n        }, {\n          name: \"Permutation in String\",\n          difficulty: \"Medium\",\n          time: \"30 min\"\n        }, {\n          name: \"Longest Substring with K Distinct\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Sliding Window Maximum\",\n          difficulty: \"Hard\",\n          time: \"35 min\"\n        }].map((problem, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#2c3e50',\n              margin: '0 0 8px 0'\n            },\n            children: problem.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: problem.difficulty === 'Easy' ? '#27ae60' : problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c'\n              },\n              children: problem.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this), ' • ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: problem.time\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this), patternMCQs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px',\n        border: '2px solid #3498db'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#3498db',\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: \"\\uD83D\\uDCDD Test Your Knowledge - Sliding Window MCQs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: [\"Question \", currentMCQIndex + 1, \" of \", patternMCQs.length, \" | Score: \", mcqScore, \"/\", completedMCQs.size, \" | Accuracy: \", completedMCQs.size > 0 ? Math.round(mcqScore / completedMCQs.size * 100) : 0, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          padding: '20px',\n          borderRadius: '8px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#2c3e50',\n            marginBottom: '15px'\n          },\n          children: (_patternMCQs$currentM = patternMCQs[currentMCQIndex]) === null || _patternMCQs$currentM === void 0 ? void 0 : _patternMCQs$currentM.question\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gap: '10px'\n          },\n          children: (_patternMCQs$currentM2 = patternMCQs[currentMCQIndex]) === null || _patternMCQs$currentM2 === void 0 ? void 0 : _patternMCQs$currentM2.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => !showMCQResult && handleMCQAnswer(option),\n            disabled: showMCQResult,\n            style: {\n              padding: '12px 16px',\n              border: '2px solid',\n              borderColor: showMCQResult ? option === patternMCQs[currentMCQIndex].answer ? '#27ae60' : option === selectedAnswer ? '#e74c3c' : '#ddd' : selectedAnswer === option ? '#3498db' : '#ddd',\n              borderRadius: '8px',\n              backgroundColor: showMCQResult ? option === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : option === selectedAnswer ? '#fadbd8' : 'white' : selectedAnswer === option ? '#ebf3fd' : 'white',\n              color: '#2c3e50',\n              cursor: showMCQResult ? 'default' : 'pointer',\n              textAlign: 'left',\n              transition: 'all 0.2s ease'\n            },\n            children: [String.fromCharCode(65 + index), \". \", option, showMCQResult && option === patternMCQs[currentMCQIndex].answer && ' ✅', showMCQResult && option === selectedAnswer && option !== patternMCQs[currentMCQIndex].answer && ' ❌']\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 13\n        }, this), showMCQResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '15px',\n            backgroundColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : '#fadbd8',\n            borderRadius: '8px',\n            border: '1px solid',\n            borderColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            style: {\n              color: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n            },\n            children: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '🎉 Correct!' : '❌ Incorrect'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '5px 0 0 0',\n              color: '#2c3e50'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Correct Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 19\n            }, this), \" \", patternMCQs[currentMCQIndex].answer]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: previousMCQ,\n          disabled: currentMCQIndex === 0,\n          style: {\n            backgroundColor: currentMCQIndex === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '6px',\n            cursor: currentMCQIndex === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous MCQ\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '5px',\n              justifyContent: 'center'\n            },\n            children: patternMCQs.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '12px',\n                height: '12px',\n                borderRadius: '50%',\n                backgroundColor: index === currentMCQIndex ? '#3498db' : completedMCQs.has(index) ? '#27ae60' : '#ddd'\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: nextMCQ,\n          disabled: currentMCQIndex === patternMCQs.length - 1,\n          style: {\n            backgroundColor: currentMCQIndex === patternMCQs.length - 1 ? '#bdc3c7' : '#3498db',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '6px',\n            cursor: currentMCQIndex === patternMCQs.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next MCQ \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 710,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        border: '2px solid #f39c12'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#f39c12',\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: \"\\uD83D\\uDCAD Follow-up Learning Prompts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          textAlign: 'center',\n          marginBottom: '25px'\n        },\n        children: \"Deepen your understanding by answering these thought-provoking questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gap: '20px'\n        },\n        children: followUpPrompts.map((prompt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#fef9e7',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #f1c40f'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#f39c12',\n              marginBottom: '10px'\n            },\n            children: [index + 1, \". \", prompt]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: followUpAnswers[prompt] || '',\n            onChange: e => handleFollowUpAnswer(prompt, e.target.value),\n            placeholder: \"Type your answer here...\",\n            style: {\n              width: '100%',\n              minHeight: '80px',\n              padding: '12px',\n              border: '1px solid #ddd',\n              borderRadius: '6px',\n              fontSize: '14px',\n              fontFamily: 'inherit',\n              resize: 'vertical'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '25px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#e8f5e8',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #27ae60'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            style: {\n              color: '#27ae60'\n            },\n            children: [\"\\uD83D\\uDCCA Progress: \", Object.keys(followUpAnswers).length, \"/\", followUpPrompts.length, \" prompts answered\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '5px 0 0 0',\n              color: '#666',\n              fontSize: '14px'\n            },\n            children: \"Complete all prompts to master the Sliding Window pattern!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 325,\n    columnNumber: 5\n  }, this);\n}\n_s(SlidingWindowPattern, \"Rwa1FdLoGhWZpmUuUXuT8GP2fTA=\");\n_c = SlidingWindowPattern;\nexport default SlidingWindowPattern;\nvar _c;\n$RefreshReg$(_c, \"SlidingWindowPattern\");", "map": {"version": 3, "names": ["React", "useState", "questionsData", "jsxDEV", "_jsxDEV", "SlidingWindowPattern", "_s", "_patternMCQs$currentM", "_patternMCQs$currentM2", "currentStep", "setCurrentStep", "demoArray", "setDemoArray", "windowSize", "setWindowSize", "windowStart", "setWindowStart", "windowEnd", "setWindowEnd", "isRunning", "setIsRunning", "currentSum", "setCurrentSum", "maxSum", "setMaxSum", "currentMCQIndex", "setCurrentMCQIndex", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAnswer", "showMCQResult", "setShowMCQResult", "mcqScore", "setMCQScore", "completedMCQs", "setCompletedMCQs", "Set", "followUpAnswers", "setFollowUpAnswers", "patternMCQs", "steps", "title", "content", "code", "learningPrompt", "answer", "slice", "join", "runDemo", "start", "sum", "reduce", "a", "b", "max", "Promise", "resolve", "setTimeout", "end", "length", "Math", "resetDemo", "handleMCQAnswer", "currentMCQ", "prev", "nextMCQ", "previousMCQ", "handleFollowUpAnswer", "prompt", "followUpPrompts", "className", "style", "fontFamily", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "borderRadius", "height", "overflow", "width", "transition", "display", "justifyContent", "gap", "flexWrap", "map", "_", "index", "onClick", "padding", "border", "cursor", "boxShadow", "lineHeight", "whiteSpace", "num", "alignItems", "fontWeight", "borderColor", "position", "top", "disabled", "opacity", "fontStyle", "paddingLeft", "marginTop", "min", "gridTemplateColumns", "name", "difficulty", "time", "problem", "size", "round", "question", "options", "option", "String", "fromCharCode", "has", "value", "onChange", "e", "target", "placeholder", "minHeight", "resize", "Object", "keys", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/patterns/SlidingWindowPattern.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\n\nfunction SlidingWindowPattern() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([2, 1, 2, 3, 4, 1, 2, 1]);\n  const [windowSize, setWindowSize] = useState(3);\n  const [windowStart, setWindowStart] = useState(0);\n  const [windowEnd, setWindowEnd] = useState(2);\n  const [isRunning, setIsRunning] = useState(false);\n  const [currentSum, setCurrentSum] = useState(5);\n  const [maxSum, setMaxSum] = useState(5);\n  const [currentMCQIndex, setCurrentMCQIndex] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState('');\n  const [showMCQResult, setShowMCQResult] = useState(false);\n  const [mcqScore, setMCQScore] = useState(0);\n  const [completedMCQs, setCompletedMCQs] = useState(new Set());\n  const [followUpAnswers, setFollowUpAnswers] = useState({});\n\n  // Get MCQs for Sliding Window pattern\n  const patternMCQs = questionsData[\"3. Sliding Window\"] || [];\n\n  const steps = [\n    {\n      title: \"🪟 Understanding Sliding Window Pattern\",\n      content: \"The Sliding Window technique maintains a subset of data (window) that slides through the array to solve subarray/substring problems efficiently.\",\n      code: `// Sliding Window Template\nfunction slidingWindow(arr, k) {\n    let windowSum = 0;\n    let maxSum = 0;\n    \n    // Calculate sum of first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    maxSum = windowSum;\n    \n    // Slide the window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}`,\n      learningPrompt: \"Why is sliding window more efficient than recalculating each subarray?\",\n      answer: \"Instead of recalculating the entire sum (O(k) for each position), we just remove the leftmost element and add the new rightmost element (O(1) per slide).\"\n    },\n    {\n      title: \"📏 Step 1: Fixed vs Variable Window\",\n      content: \"Identify whether you need a fixed-size window or a variable-size window based on the problem requirements.\",\n      code: `// Fixed Window (size k)\nfunction maxSumSubarray(arr, k) {\n    let windowSum = 0;\n    \n    // Initial window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}\n\n// Variable Window\nfunction longestSubstringKDistinct(s, k) {\n    let left = 0, maxLength = 0;\n    let charCount = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        charCount.set(s[right], (charCount.get(s[right]) || 0) + 1);\n        \n        while (charCount.size > k) {\n            charCount.set(s[left], charCount.get(s[left]) - 1);\n            if (charCount.get(s[left]) === 0) {\n                charCount.delete(s[left]);\n            }\n            left++;\n        }\n        \n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}`,\n      learningPrompt: \"When do we use fixed vs variable window size?\",\n      answer: \"Fixed window: when problem specifies exact size (e.g., 'subarray of size k'). Variable window: when we need to find optimal size based on conditions.\"\n    },\n    {\n      title: \"🎯 Step 2: Initialize Window\",\n      content: \"Set up the initial window and calculate the initial state (sum, count, etc.).\",\n      code: `// Step 2: Window Initialization\nfunction initializeWindow(arr, k) {\n    let windowSum = 0;\n    let windowStart = 0;\n    let windowEnd = k - 1;\n    \n    // Calculate initial window sum\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    console.log(\\`Initial window: [\\${windowStart}, \\${windowEnd}]\\`);\n    console.log(\\`Initial sum: \\${windowSum}\\`);\n    \n    return { windowSum, windowStart, windowEnd };\n}\n\n// For variable window\nfunction initializeVariableWindow() {\n    let left = 0;\n    let right = 0;\n    let windowState = new Map(); // or other data structure\n    \n    return { left, right, windowState };\n}`,\n      learningPrompt: \"What should we track in the window state?\",\n      answer: \"Depends on problem: sum (for max sum), character counts (for substring), frequency maps, or custom conditions.\"\n    },\n    {\n      title: \"➡️ Step 3: Slide the Window\",\n      content: \"Move the window by removing the leftmost element and adding the new rightmost element.\",\n      code: `// Step 3: Window Sliding Logic\nfunction slideWindow(arr, k) {\n    let windowSum = 0;\n    \n    // Initialize first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide the window\n    for (let windowEnd = k; windowEnd < arr.length; windowEnd++) {\n        // Add new element to window\n        windowSum += arr[windowEnd];\n        \n        // Remove element going out of window\n        windowSum -= arr[windowEnd - k];\n        \n        // Update result\n        maxSum = Math.max(maxSum, windowSum);\n        \n        console.log(\\`Window: [\\${windowEnd - k + 1}, \\${windowEnd}], Sum: \\${windowSum}\\`);\n    }\n    \n    return maxSum;\n}`,\n      learningPrompt: \"What's the key insight in the sliding operation?\",\n      answer: \"We maintain the window size by simultaneously adding one element and removing another, keeping the operation O(1) per slide.\"\n    },\n    {\n      title: \"🎮 Step 4: Interactive Demo\",\n      content: \"Watch the sliding window algorithm find the maximum sum subarray of size k.\",\n      code: `// Live Demo: Maximum Sum Subarray\nconst arr = [2, 1, 2, 3, 4, 1, 2, 1];\nconst k = 3;\n\n// Current window: [${windowStart}, ${windowEnd}]\n// Window elements: [${demoArray.slice(windowStart, windowEnd + 1).join(', ')}]\n// Current sum: ${currentSum}\n// Maximum sum so far: ${maxSum}`,\n      learningPrompt: \"How does the window maintain its size while sliding?\",\n      answer: \"By adding the new element at the right and removing the old element at the left simultaneously.\"\n    },\n    {\n      title: \"🔄 Step 5: Variable Window Patterns\",\n      content: \"Learn how to handle variable-size windows that expand and contract based on conditions.\",\n      code: `// Variable Window: Longest Substring Without Repeating Characters\nfunction lengthOfLongestSubstring(s) {\n    let left = 0;\n    let maxLength = 0;\n    let charSet = new Set();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        while (charSet.has(s[right])) {\n            charSet.delete(s[left]);\n            left++;\n        }\n        \n        charSet.add(s[right]);\n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}\n\n// Variable Window: Minimum Window Substring\nfunction minWindow(s, t) {\n    let left = 0, minLen = Infinity, minStart = 0;\n    let required = new Map();\n    let formed = 0;\n    \n    // Count characters in t\n    for (let char of t) {\n        required.set(char, (required.get(char) || 0) + 1);\n    }\n    \n    let windowCounts = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        let char = s[right];\n        windowCounts.set(char, (windowCounts.get(char) || 0) + 1);\n        \n        if (required.has(char) && windowCounts.get(char) === required.get(char)) {\n            formed++;\n        }\n        \n        // Contract window\n        while (left <= right && formed === required.size) {\n            if (right - left + 1 < minLen) {\n                minLen = right - left + 1;\n                minStart = left;\n            }\n            \n            let leftChar = s[left];\n            windowCounts.set(leftChar, windowCounts.get(leftChar) - 1);\n            if (required.has(leftChar) && windowCounts.get(leftChar) < required.get(leftChar)) {\n                formed--;\n            }\n            left++;\n        }\n    }\n    \n    return minLen === Infinity ? \"\" : s.substring(minStart, minStart + minLen);\n}`,\n      learningPrompt: \"When do we expand vs contract a variable window?\",\n      answer: \"Expand when we haven't met the condition yet. Contract when we've met the condition and want to find the minimum valid window.\"\n    }\n  ];\n\n  const runDemo = async () => {\n    setIsRunning(true);\n    let start = 0;\n    let sum = demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0);\n    let max = sum;\n    \n    setWindowStart(start);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(sum);\n    setMaxSum(max);\n    \n    await new Promise(resolve => setTimeout(resolve, 1500));\n    \n    for (let end = windowSize; end < demoArray.length; end++) {\n      // Slide window\n      sum = sum - demoArray[start] + demoArray[end];\n      start++;\n      max = Math.max(max, sum);\n      \n      setWindowStart(start);\n      setWindowEnd(end);\n      setCurrentSum(sum);\n      setMaxSum(max);\n      \n      await new Promise(resolve => setTimeout(resolve, 1500));\n    }\n    \n    setIsRunning(false);\n  };\n\n  const resetDemo = () => {\n    setWindowStart(0);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setMaxSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setIsRunning(false);\n  };\n\n  // MCQ handling functions\n  const handleMCQAnswer = (answer) => {\n    setSelectedAnswer(answer);\n    setShowMCQResult(true);\n\n    const currentMCQ = patternMCQs[currentMCQIndex];\n    if (answer === currentMCQ.answer) {\n      setMCQScore(prev => prev + 1);\n    }\n    setCompletedMCQs(prev => new Set([...prev, currentMCQIndex]));\n  };\n\n  const nextMCQ = () => {\n    if (currentMCQIndex < patternMCQs.length - 1) {\n      setCurrentMCQIndex(prev => prev + 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n\n  const previousMCQ = () => {\n    if (currentMCQIndex > 0) {\n      setCurrentMCQIndex(prev => prev - 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n\n  const handleFollowUpAnswer = (prompt, answer) => {\n    setFollowUpAnswers(prev => ({\n      ...prev,\n      [prompt]: answer\n    }));\n  };\n\n  // Follow-up prompts specific to Sliding Window\n  const followUpPrompts = [\n    \"Explain the difference between fixed-size and variable-size sliding windows.\",\n    \"How would you identify if a problem requires a sliding window approach?\",\n    \"What are the key optimization benefits of using sliding window over brute force?\",\n    \"Describe a scenario where you would use a sliding window with a HashMap.\",\n    \"How do you handle edge cases in sliding window problems (empty arrays, single elements)?\"\n  ];\n\n  return (\n    <div className=\"page-content\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Header */}\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#3498db', fontSize: '2.5em', marginBottom: '10px' }}>\n          🪟 Sliding Window Pattern\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>\n          Master the sliding window technique to optimize subarray and substring problems\n        </p>\n      </div>\n\n      {/* Progress Bar */}\n      <div style={{ marginBottom: '30px' }}>\n        <div style={{\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        }}>\n          <div style={{\n            backgroundColor: '#3498db',\n            height: '100%',\n            width: `${((currentStep + 1) / steps.length) * 100}%`,\n            transition: 'width 0.3s ease'\n          }}></div>\n        </div>\n        <p style={{ textAlign: 'center', margin: '10px 0', color: '#7f8c8d' }}>\n          Step {currentStep + 1} of {steps.length}\n        </p>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        gap: '10px', \n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      }}>\n        {steps.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentStep(index)}\n            style={{\n              padding: '8px 16px',\n              border: currentStep === index ? '2px solid #3498db' : '1px solid #ddd',\n              borderRadius: '20px',\n              backgroundColor: currentStep === index ? '#3498db' : 'white',\n              color: currentStep === index ? 'white' : '#333',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Current Step Content */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      }}>\n        <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>\n          {steps[currentStep].title}\n        </h2>\n        \n        <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>\n          {steps[currentStep].content}\n        </p>\n\n        {/* Code Block */}\n        <div style={{\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        }}>\n          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>\n            {steps[currentStep].code}\n          </pre>\n        </div>\n\n        {/* Interactive Demo for Step 4 */}\n        {currentStep === 4 && (\n          <div style={{\n            backgroundColor: '#f0f8ff',\n            border: '2px solid #3498db',\n            borderRadius: '12px',\n            padding: '20px',\n            marginBottom: '20px'\n          }}>\n            <h3 style={{ color: '#3498db', marginBottom: '15px' }}>🎮 Live Demo: Maximum Sum Subarray</h3>\n            \n            {/* Array Visualization */}\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', marginBottom: '10px' }}>\n                {demoArray.map((num, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '40px',\n                      height: '40px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      backgroundColor: \n                        index >= windowStart && index <= windowEnd ? '#3498db' : '#ecf0f1',\n                      color: (index >= windowStart && index <= windowEnd) ? 'white' : '#333',\n                      borderRadius: '8px',\n                      fontWeight: 'bold',\n                      border: '2px solid',\n                      borderColor: \n                        index >= windowStart && index <= windowEnd ? '#2980b9' : '#bdc3c7',\n                      position: 'relative'\n                    }}\n                  >\n                    {num}\n                    {index === windowStart && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-25px',\n                        fontSize: '12px',\n                        color: '#e74c3c',\n                        fontWeight: 'bold'\n                      }}>\n                        Start\n                      </div>\n                    )}\n                    {index === windowEnd && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-25px',\n                        fontSize: '12px',\n                        color: '#e74c3c',\n                        fontWeight: 'bold'\n                      }}>\n                        End\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n              \n              <div style={{ textAlign: 'center', fontSize: '14px', color: '#666' }}>\n                <span>Window Size: {windowSize}</span>\n                {' | '}\n                <span>Current Sum: {currentSum}</span>\n                {' | '}\n                <span style={{ color: '#27ae60', fontWeight: 'bold' }}>Max Sum: {maxSum}</span>\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div style={{ textAlign: 'center', gap: '10px', display: 'flex', justifyContent: 'center' }}>\n              <button\n                onClick={runDemo}\n                disabled={isRunning}\n                style={{\n                  backgroundColor: '#3498db',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: isRunning ? 'not-allowed' : 'pointer',\n                  opacity: isRunning ? 0.6 : 1\n                }}\n              >\n                {isRunning ? '🔄 Sliding...' : '▶️ Run Demo'}\n              </button>\n              \n              <button\n                onClick={resetDemo}\n                style={{\n                  backgroundColor: '#95a5a6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: 'pointer'\n                }}\n              >\n                🔄 Reset\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Prompt */}\n        <div style={{\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        }}>\n          <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>\n            🤔 Learning Prompt:\n          </h4>\n          <p style={{ color: '#856404', margin: '0 0 10px 0', fontStyle: 'italic' }}>\n            {steps[currentStep].learningPrompt}\n          </p>\n          <details>\n            <summary style={{ color: '#856404', cursor: 'pointer', fontWeight: 'bold' }}>\n              💡 Click to see answer\n            </summary>\n            <p style={{ color: '#856404', margin: '10px 0 0 0', paddingLeft: '20px' }}>\n              {steps[currentStep].answer}\n            </p>\n          </details>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '30px' }}>\n          <button\n            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}\n            disabled={currentStep === 0}\n            style={{\n              backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            ← Previous\n          </button>\n          \n          <button\n            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}\n            disabled={currentStep === steps.length - 1}\n            style={{\n              backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#3498db',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next →\n          </button>\n        </div>\n      </div>\n\n      {/* Practice Problems */}\n      <div style={{\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #3498db'\n      }}>\n        <h3 style={{ color: '#3498db', marginBottom: '20px' }}>\n          🎯 Practice Problems\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\n          {[\n            { name: \"Maximum Sum Subarray of Size K\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Longest Substring Without Repeating\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Minimum Window Substring\", difficulty: \"Hard\", time: \"40 min\" },\n            { name: \"Permutation in String\", difficulty: \"Medium\", time: \"30 min\" },\n            { name: \"Longest Substring with K Distinct\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Sliding Window Maximum\", difficulty: \"Hard\", time: \"35 min\" }\n          ].map((problem, index) => (\n            <div\n              key={index}\n              style={{\n                backgroundColor: 'white',\n                padding: '15px',\n                borderRadius: '8px',\n                border: '1px solid #dee2e6'\n              }}\n            >\n              <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0' }}>{problem.name}</h4>\n              <div style={{ fontSize: '14px', color: '#666' }}>\n                <span style={{ \n                  color: problem.difficulty === 'Easy' ? '#27ae60' : \n                        problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c' \n                }}>\n                  {problem.difficulty}\n                </span>\n                {' • '}\n                <span>{problem.time}</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* MCQ Section */}\n      {patternMCQs.length > 0 && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          padding: '30px',\n          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n          marginBottom: '30px',\n          border: '2px solid #3498db'\n        }}>\n          <h3 style={{ color: '#3498db', marginBottom: '20px', textAlign: 'center' }}>\n            📝 Test Your Knowledge - Sliding Window MCQs\n          </h3>\n\n          {/* MCQ Progress */}\n          <div style={{ marginBottom: '20px', textAlign: 'center' }}>\n            <span style={{ color: '#666', fontSize: '14px' }}>\n              Question {currentMCQIndex + 1} of {patternMCQs.length} |\n              Score: {mcqScore}/{completedMCQs.size} |\n              Accuracy: {completedMCQs.size > 0 ? Math.round((mcqScore / completedMCQs.size) * 100) : 0}%\n            </span>\n          </div>\n\n          {/* Current MCQ */}\n          <div style={{\n            backgroundColor: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            marginBottom: '20px'\n          }}>\n            <h4 style={{ color: '#2c3e50', marginBottom: '15px' }}>\n              {patternMCQs[currentMCQIndex]?.question}\n            </h4>\n\n            <div style={{ display: 'grid', gap: '10px' }}>\n              {patternMCQs[currentMCQIndex]?.options.map((option, index) => (\n                <button\n                  key={index}\n                  onClick={() => !showMCQResult && handleMCQAnswer(option)}\n                  disabled={showMCQResult}\n                  style={{\n                    padding: '12px 16px',\n                    border: '2px solid',\n                    borderColor: showMCQResult\n                      ? (option === patternMCQs[currentMCQIndex].answer ? '#27ae60'\n                         : option === selectedAnswer ? '#e74c3c' : '#ddd')\n                      : (selectedAnswer === option ? '#3498db' : '#ddd'),\n                    borderRadius: '8px',\n                    backgroundColor: showMCQResult\n                      ? (option === patternMCQs[currentMCQIndex].answer ? '#d5f4e6'\n                         : option === selectedAnswer ? '#fadbd8' : 'white')\n                      : (selectedAnswer === option ? '#ebf3fd' : 'white'),\n                    color: '#2c3e50',\n                    cursor: showMCQResult ? 'default' : 'pointer',\n                    textAlign: 'left',\n                    transition: 'all 0.2s ease'\n                  }}\n                >\n                  {String.fromCharCode(65 + index)}. {option}\n                  {showMCQResult && option === patternMCQs[currentMCQIndex].answer && ' ✅'}\n                  {showMCQResult && option === selectedAnswer && option !== patternMCQs[currentMCQIndex].answer && ' ❌'}\n                </button>\n              ))}\n            </div>\n\n            {showMCQResult && (\n              <div style={{\n                marginTop: '15px',\n                padding: '15px',\n                backgroundColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : '#fadbd8',\n                borderRadius: '8px',\n                border: '1px solid',\n                borderColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n              }}>\n                <strong style={{\n                  color: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n                }}>\n                  {selectedAnswer === patternMCQs[currentMCQIndex].answer ? '🎉 Correct!' : '❌ Incorrect'}\n                </strong>\n                <p style={{ margin: '5px 0 0 0', color: '#2c3e50' }}>\n                  <strong>Correct Answer:</strong> {patternMCQs[currentMCQIndex].answer}\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* MCQ Navigation */}\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <button\n              onClick={previousMCQ}\n              disabled={currentMCQIndex === 0}\n              style={{\n                backgroundColor: currentMCQIndex === 0 ? '#bdc3c7' : '#95a5a6',\n                color: 'white',\n                border: 'none',\n                padding: '10px 20px',\n                borderRadius: '6px',\n                cursor: currentMCQIndex === 0 ? 'not-allowed' : 'pointer'\n              }}\n            >\n              ← Previous MCQ\n            </button>\n\n            <div style={{ textAlign: 'center' }}>\n              <div style={{\n                display: 'flex',\n                gap: '5px',\n                justifyContent: 'center'\n              }}>\n                {patternMCQs.map((_, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '12px',\n                      height: '12px',\n                      borderRadius: '50%',\n                      backgroundColor:\n                        index === currentMCQIndex ? '#3498db' :\n                        completedMCQs.has(index) ? '#27ae60' : '#ddd'\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n\n            <button\n              onClick={nextMCQ}\n              disabled={currentMCQIndex === patternMCQs.length - 1}\n              style={{\n                backgroundColor: currentMCQIndex === patternMCQs.length - 1 ? '#bdc3c7' : '#3498db',\n                color: 'white',\n                border: 'none',\n                padding: '10px 20px',\n                borderRadius: '6px',\n                cursor: currentMCQIndex === patternMCQs.length - 1 ? 'not-allowed' : 'pointer'\n              }}\n            >\n              Next MCQ →\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Follow-up Prompts Section */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        border: '2px solid #f39c12'\n      }}>\n        <h3 style={{ color: '#f39c12', marginBottom: '20px', textAlign: 'center' }}>\n          💭 Follow-up Learning Prompts\n        </h3>\n        <p style={{ color: '#666', textAlign: 'center', marginBottom: '25px' }}>\n          Deepen your understanding by answering these thought-provoking questions\n        </p>\n\n        <div style={{ display: 'grid', gap: '20px' }}>\n          {followUpPrompts.map((prompt, index) => (\n            <div key={index} style={{\n              backgroundColor: '#fef9e7',\n              padding: '20px',\n              borderRadius: '8px',\n              border: '1px solid #f1c40f'\n            }}>\n              <h4 style={{ color: '#f39c12', marginBottom: '10px' }}>\n                {index + 1}. {prompt}\n              </h4>\n              <textarea\n                value={followUpAnswers[prompt] || ''}\n                onChange={(e) => handleFollowUpAnswer(prompt, e.target.value)}\n                placeholder=\"Type your answer here...\"\n                style={{\n                  width: '100%',\n                  minHeight: '80px',\n                  padding: '12px',\n                  border: '1px solid #ddd',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  fontFamily: 'inherit',\n                  resize: 'vertical'\n                }}\n              />\n            </div>\n          ))}\n        </div>\n\n        <div style={{ textAlign: 'center', marginTop: '25px' }}>\n          <div style={{\n            backgroundColor: '#e8f5e8',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #27ae60'\n          }}>\n            <strong style={{ color: '#27ae60' }}>\n              📊 Progress: {Object.keys(followUpAnswers).length}/{followUpPrompts.length} prompts answered\n            </strong>\n            <p style={{ margin: '5px 0 0 0', color: '#666', fontSize: '14px' }}>\n              Complete all prompts to master the Sliding Window pattern!\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default SlidingWindowPattern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,oBAAoBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjC,QAAQ,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAMqC,WAAW,GAAGpC,aAAa,CAAC,mBAAmB,CAAC,IAAI,EAAE;EAE5D,MAAMqC,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,yCAAyC;IAChDC,OAAO,EAAE,kJAAkJ;IAC3JC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,wEAAwE;IACxFC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,4GAA4G;IACrHC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE,+EAA+E;IACxFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,2CAA2C;IAC3DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,wFAAwF;IACjGC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,kDAAkD;IAClEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,6EAA6E;IACtFC,IAAI,EAAE;AACZ;AACA;AACA;AACA,sBAAsB3B,WAAW,KAAKE,SAAS;AAC/C,uBAAuBN,SAAS,CAACkC,KAAK,CAAC9B,WAAW,EAAEE,SAAS,GAAG,CAAC,CAAC,CAAC6B,IAAI,CAAC,IAAI,CAAC;AAC7E,kBAAkBzB,UAAU;AAC5B,yBAAyBE,MAAM,EAAE;IAC3BoB,cAAc,EAAE,sDAAsD;IACtEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,yFAAyF;IAClGC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,kDAAkD;IAClEC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMG,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B3B,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI4B,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAGtC,SAAS,CAACkC,KAAK,CAAC,CAAC,EAAEhC,UAAU,CAAC,CAACqC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;IACnE,IAAIC,GAAG,GAAGJ,GAAG;IAEbjC,cAAc,CAACgC,KAAK,CAAC;IACrB9B,YAAY,CAACL,UAAU,GAAG,CAAC,CAAC;IAC5BS,aAAa,CAAC2B,GAAG,CAAC;IAClBzB,SAAS,CAAC6B,GAAG,CAAC;IAEd,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,KAAK,IAAIE,GAAG,GAAG5C,UAAU,EAAE4C,GAAG,GAAG9C,SAAS,CAAC+C,MAAM,EAAED,GAAG,EAAE,EAAE;MACxD;MACAR,GAAG,GAAGA,GAAG,GAAGtC,SAAS,CAACqC,KAAK,CAAC,GAAGrC,SAAS,CAAC8C,GAAG,CAAC;MAC7CT,KAAK,EAAE;MACPK,GAAG,GAAGM,IAAI,CAACN,GAAG,CAACA,GAAG,EAAEJ,GAAG,CAAC;MAExBjC,cAAc,CAACgC,KAAK,CAAC;MACrB9B,YAAY,CAACuC,GAAG,CAAC;MACjBnC,aAAa,CAAC2B,GAAG,CAAC;MAClBzB,SAAS,CAAC6B,GAAG,CAAC;MAEd,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACzD;IAEAnC,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMwC,SAAS,GAAGA,CAAA,KAAM;IACtB5C,cAAc,CAAC,CAAC,CAAC;IACjBE,YAAY,CAACL,UAAU,GAAG,CAAC,CAAC;IAC5BS,aAAa,CAACX,SAAS,CAACkC,KAAK,CAAC,CAAC,EAAEhC,UAAU,CAAC,CAACqC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxE5B,SAAS,CAACb,SAAS,CAACkC,KAAK,CAAC,CAAC,EAAEhC,UAAU,CAAC,CAACqC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpEhC,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMyC,eAAe,GAAIjB,MAAM,IAAK;IAClChB,iBAAiB,CAACgB,MAAM,CAAC;IACzBd,gBAAgB,CAAC,IAAI,CAAC;IAEtB,MAAMgC,UAAU,GAAGxB,WAAW,CAACb,eAAe,CAAC;IAC/C,IAAImB,MAAM,KAAKkB,UAAU,CAAClB,MAAM,EAAE;MAChCZ,WAAW,CAAC+B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B;IACA7B,gBAAgB,CAAC6B,IAAI,IAAI,IAAI5B,GAAG,CAAC,CAAC,GAAG4B,IAAI,EAAEtC,eAAe,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMuC,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIvC,eAAe,GAAGa,WAAW,CAACoB,MAAM,GAAG,CAAC,EAAE;MAC5ChC,kBAAkB,CAACqC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACpCnC,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIxC,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAACqC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACpCnC,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMoC,oBAAoB,GAAGA,CAACC,MAAM,EAAEvB,MAAM,KAAK;IAC/CP,kBAAkB,CAAC0B,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACI,MAAM,GAAGvB;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMwB,eAAe,GAAG,CACtB,8EAA8E,EAC9E,yEAAyE,EACzE,kFAAkF,EAClF,0EAA0E,EAC1E,0FAA0F,CAC3F;EAED,oBACEhE,OAAA;IAAKiE,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAEvEpE,OAAA;MAAKkE,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxDpE,OAAA;QAAIkE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEF,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL5E,OAAA;QAAGkE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEK,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAExF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN5E,OAAA;MAAKkE,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnCpE,OAAA;QAAKkE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACApE,OAAA;UAAKkE,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,MAAM;YACdE,KAAK,EAAE,GAAI,CAAC9E,WAAW,GAAG,CAAC,IAAI8B,KAAK,CAACmB,MAAM,GAAI,GAAG,GAAG;YACrD8B,UAAU,EAAE;UACd;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN5E,OAAA;QAAGkE,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAES,MAAM,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAH,QAAA,GAAC,OAChE,EAAC/D,WAAW,GAAG,CAAC,EAAC,MAAI,EAAC8B,KAAK,CAACmB,MAAM;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN5E,OAAA;MAAKkE,KAAK,EAAE;QACVmB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXjB,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE;MACZ,CAAE;MAAApB,QAAA,EACCjC,KAAK,CAACsD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAClB3F,OAAA;QAEE4F,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAACqF,KAAK,CAAE;QACrCzB,KAAK,EAAE;UACL2B,OAAO,EAAE,UAAU;UACnBC,MAAM,EAAEzF,WAAW,KAAKsF,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;UACtEX,YAAY,EAAE,MAAM;UACpBD,eAAe,EAAE1E,WAAW,KAAKsF,KAAK,GAAG,SAAS,GAAG,OAAO;UAC5DpB,KAAK,EAAElE,WAAW,KAAKsF,KAAK,GAAG,OAAO,GAAG,MAAM;UAC/CI,MAAM,EAAE,SAAS;UACjBvB,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EAEDuB,KAAK,GAAG;MAAC,GAZLA,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5E,OAAA;MAAKkE,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACApE,OAAA;QAAIkE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDjC,KAAK,CAAC9B,WAAW,CAAC,CAAC+B;MAAK;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAEL5E,OAAA;QAAGkE,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAE0B,UAAU,EAAE,KAAK;UAAE3B,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClEjC,KAAK,CAAC9B,WAAW,CAAC,CAACgC;MAAO;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAGJ5E,OAAA;QAAKkE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE,MAAM;UACpBH,UAAU,EAAE,6BAA6B;UACzCK,QAAQ,EAAE,MAAM;UAChBU,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACApE,OAAA;UAAKkE,KAAK,EAAE;YAAEY,MAAM,EAAE,CAAC;YAAEoB,UAAU,EAAE;UAAW,CAAE;UAAA9B,QAAA,EAC/CjC,KAAK,CAAC9B,WAAW,CAAC,CAACiC;QAAI;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLvE,WAAW,KAAK,CAAC,iBAChBL,OAAA;QAAKkE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,MAAM;UACpBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACApE,OAAA;UAAIkE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAkC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAG9F5E,OAAA;UAAKkE,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCpE,OAAA;YAAKkE,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,KAAK;cAAED,cAAc,EAAE,QAAQ;cAAEhB,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EACzF7D,SAAS,CAACkF,GAAG,CAAC,CAACU,GAAG,EAAER,KAAK,kBACxB3F,OAAA;cAEEkE,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdI,OAAO,EAAE,MAAM;gBACfe,UAAU,EAAE,QAAQ;gBACpBd,cAAc,EAAE,QAAQ;gBACxBP,eAAe,EACbY,KAAK,IAAIhF,WAAW,IAAIgF,KAAK,IAAI9E,SAAS,GAAG,SAAS,GAAG,SAAS;gBACpE0D,KAAK,EAAGoB,KAAK,IAAIhF,WAAW,IAAIgF,KAAK,IAAI9E,SAAS,GAAI,OAAO,GAAG,MAAM;gBACtEmE,YAAY,EAAE,KAAK;gBACnBqB,UAAU,EAAE,MAAM;gBAClBP,MAAM,EAAE,WAAW;gBACnBQ,WAAW,EACTX,KAAK,IAAIhF,WAAW,IAAIgF,KAAK,IAAI9E,SAAS,GAAG,SAAS,GAAG,SAAS;gBACpE0F,QAAQ,EAAE;cACZ,CAAE;cAAAnC,QAAA,GAED+B,GAAG,EACHR,KAAK,KAAKhF,WAAW,iBACpBX,OAAA;gBAAKkE,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAe,KAAK,KAAK9E,SAAS,iBAClBb,OAAA;gBAAKkE,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GAxCIe,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyCP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5E,OAAA;YAAKkE,KAAK,EAAE;cAAEG,SAAS,EAAE,QAAQ;cAAEG,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnEpE,OAAA;cAAAoE,QAAA,GAAM,eAAa,EAAC3D,UAAU;YAAA;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrC,KAAK,eACN5E,OAAA;cAAAoE,QAAA,GAAM,eAAa,EAACnD,UAAU;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrC,KAAK,eACN5E,OAAA;cAAMkE,KAAK,EAAE;gBAAEK,KAAK,EAAE,SAAS;gBAAE8B,UAAU,EAAE;cAAO,CAAE;cAAAjC,QAAA,GAAC,WAAS,EAACjD,MAAM;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5E,OAAA;UAAKkE,KAAK,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEkB,GAAG,EAAE,MAAM;YAAEF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBAC1FpE,OAAA;YACE4F,OAAO,EAAEjD,OAAQ;YACjB8D,QAAQ,EAAE1F,SAAU;YACpBmD,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAEhF,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7C2F,OAAO,EAAE3F,SAAS,GAAG,GAAG,GAAG;YAC7B,CAAE;YAAAqD,QAAA,EAEDrD,SAAS,GAAG,eAAe,GAAG;UAAa;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAET5E,OAAA;YACE4F,OAAO,EAAEpC,SAAU;YACnBU,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD5E,OAAA;QAAKkE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACApE,OAAA;UAAIkE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE;UAAa,CAAE;UAAAV,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5E,OAAA;UAAGkE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE,YAAY;YAAE6B,SAAS,EAAE;UAAS,CAAE;UAAAvC,QAAA,EACvEjC,KAAK,CAAC9B,WAAW,CAAC,CAACkC;QAAc;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJ5E,OAAA;UAAAoE,QAAA,gBACEpE,OAAA;YAASkE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEwB,MAAM,EAAE,SAAS;cAAEM,UAAU,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAAC;UAE7E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACV5E,OAAA;YAAGkE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE,YAAY;cAAE8B,WAAW,EAAE;YAAO,CAAE;YAAAxC,QAAA,EACvEjC,KAAK,CAAC9B,WAAW,CAAC,CAACmC;UAAM;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGN5E,OAAA;QAAKkE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEuB,SAAS,EAAE;QAAO,CAAE;QAAAzC,QAAA,gBAClFpE,OAAA;UACE4F,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAACiD,IAAI,CAACN,GAAG,CAAC,CAAC,EAAE5C,WAAW,GAAG,CAAC,CAAC,CAAE;UAC5DoG,QAAQ,EAAEpG,WAAW,KAAK,CAAE;UAC5B6D,KAAK,EAAE;YACLa,eAAe,EAAE1E,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC1DkE,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE1F,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;UAC9C,CAAE;UAAA+D,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5E,OAAA;UACE4F,OAAO,EAAEA,CAAA,KAAMtF,cAAc,CAACiD,IAAI,CAACuD,GAAG,CAAC3E,KAAK,CAACmB,MAAM,GAAG,CAAC,EAAEjD,WAAW,GAAG,CAAC,CAAC,CAAE;UAC3EoG,QAAQ,EAAEpG,WAAW,KAAK8B,KAAK,CAACmB,MAAM,GAAG,CAAE;UAC3CY,KAAK,EAAE;YACLa,eAAe,EAAE1E,WAAW,KAAK8B,KAAK,CAACmB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACzEiB,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE1F,WAAW,KAAK8B,KAAK,CAACmB,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UAC7D,CAAE;UAAAc,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5E,OAAA;MAAKkE,KAAK,EAAE;QACVa,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACApE,OAAA;QAAIkE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEvD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5E,OAAA;QAAKkE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAE0B,mBAAmB,EAAE,sCAAsC;UAAExB,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EACvG,CACC;UAAE4C,IAAI,EAAE,gCAAgC;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC9E;UAAEF,IAAI,EAAE,qCAAqC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACrF;UAAEF,IAAI,EAAE,0BAA0B;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACxE;UAAEF,IAAI,EAAE,uBAAuB;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACvE;UAAEF,IAAI,EAAE,mCAAmC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACnF;UAAEF,IAAI,EAAE,wBAAwB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,CACvE,CAACzB,GAAG,CAAC,CAAC0B,OAAO,EAAExB,KAAK,kBACnB3F,OAAA;UAEEkE,KAAK,EAAE;YACLa,eAAe,EAAE,OAAO;YACxBc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBAEFpE,OAAA;YAAIkE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE;YAAY,CAAE;YAAAV,QAAA,EAAE+C,OAAO,CAACH;UAAI;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzE5E,OAAA;YAAKkE,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBAC9CpE,OAAA;cAAMkE,KAAK,EAAE;gBACXK,KAAK,EAAE4C,OAAO,CAACF,UAAU,KAAK,MAAM,GAAG,SAAS,GAC1CE,OAAO,CAACF,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG;cACtD,CAAE;cAAA7C,QAAA,EACC+C,OAAO,CAACF;YAAU;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACN,KAAK,eACN5E,OAAA;cAAAoE,QAAA,EAAO+C,OAAO,CAACD;YAAI;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA,GAlBDe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1C,WAAW,CAACoB,MAAM,GAAG,CAAC,iBACrBtD,OAAA;MAAKkE,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE,MAAM;QACpBwB,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACApE,OAAA;QAAIkE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGL5E,OAAA;QAAKkE,KAAK,EAAE;UAAEI,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,eACxDpE,OAAA;UAAMkE,KAAK,EAAE;YAAEK,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAJ,QAAA,GAAC,WACvC,EAAC/C,eAAe,GAAG,CAAC,EAAC,MAAI,EAACa,WAAW,CAACoB,MAAM,EAAC,YAC/C,EAAC3B,QAAQ,EAAC,GAAC,EAACE,aAAa,CAACuF,IAAI,EAAC,eAC5B,EAACvF,aAAa,CAACuF,IAAI,GAAG,CAAC,GAAG7D,IAAI,CAAC8D,KAAK,CAAE1F,QAAQ,GAAGE,aAAa,CAACuF,IAAI,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC5F;QAAA;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN5E,OAAA;QAAKkE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Bc,OAAO,EAAE,MAAM;UACfb,YAAY,EAAE,KAAK;UACnBV,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACApE,OAAA;UAAIkE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,GAAAjE,qBAAA,GACnD+B,WAAW,CAACb,eAAe,CAAC,cAAAlB,qBAAA,uBAA5BA,qBAAA,CAA8BmH;QAAQ;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAEL5E,OAAA;UAAKkE,KAAK,EAAE;YAAEmB,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAnB,QAAA,GAAAhE,sBAAA,GAC1C8B,WAAW,CAACb,eAAe,CAAC,cAAAjB,sBAAA,uBAA5BA,sBAAA,CAA8BmH,OAAO,CAAC9B,GAAG,CAAC,CAAC+B,MAAM,EAAE7B,KAAK,kBACvD3F,OAAA;YAEE4F,OAAO,EAAEA,CAAA,KAAM,CAACnE,aAAa,IAAIgC,eAAe,CAAC+D,MAAM,CAAE;YACzDf,QAAQ,EAAEhF,aAAc;YACxByC,KAAK,EAAE;cACL2B,OAAO,EAAE,WAAW;cACpBC,MAAM,EAAE,WAAW;cACnBQ,WAAW,EAAE7E,aAAa,GACrB+F,MAAM,KAAKtF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GACxDgF,MAAM,KAAKjG,cAAc,GAAG,SAAS,GAAG,MAAM,GAChDA,cAAc,KAAKiG,MAAM,GAAG,SAAS,GAAG,MAAO;cACpDxC,YAAY,EAAE,KAAK;cACnBD,eAAe,EAAEtD,aAAa,GACzB+F,MAAM,KAAKtF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GACxDgF,MAAM,KAAKjG,cAAc,GAAG,SAAS,GAAG,OAAO,GACjDA,cAAc,KAAKiG,MAAM,GAAG,SAAS,GAAG,OAAQ;cACrDjD,KAAK,EAAE,SAAS;cAChBwB,MAAM,EAAEtE,aAAa,GAAG,SAAS,GAAG,SAAS;cAC7C4C,SAAS,EAAE,MAAM;cACjBe,UAAU,EAAE;YACd,CAAE;YAAAhB,QAAA,GAEDqD,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG/B,KAAK,CAAC,EAAC,IAAE,EAAC6B,MAAM,EACzC/F,aAAa,IAAI+F,MAAM,KAAKtF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,IAAI,IAAI,EACvEf,aAAa,IAAI+F,MAAM,KAAKjG,cAAc,IAAIiG,MAAM,KAAKtF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,IAAI,IAAI;UAAA,GAvBhGmD,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELnD,aAAa,iBACZzB,OAAA;UAAKkE,KAAK,EAAE;YACV2C,SAAS,EAAE,MAAM;YACjBhB,OAAO,EAAE,MAAM;YACfd,eAAe,EAAExD,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG,SAAS;YAC/FwC,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE,WAAW;YACnBQ,WAAW,EAAE/E,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG;UACpF,CAAE;UAAA4B,QAAA,gBACApE,OAAA;YAAQkE,KAAK,EAAE;cACbK,KAAK,EAAEhD,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG;YAC9E,CAAE;YAAA4B,QAAA,EACC7C,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,aAAa,GAAG;UAAa;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACT5E,OAAA;YAAGkE,KAAK,EAAE;cAAEY,MAAM,EAAE,WAAW;cAAEP,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,gBAClDpE,OAAA;cAAAoE,QAAA,EAAQ;YAAe;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC1C,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN5E,OAAA;QAAKkE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEc,UAAU,EAAE;QAAS,CAAE;QAAAhC,QAAA,gBACrFpE,OAAA;UACE4F,OAAO,EAAE/B,WAAY;UACrB4C,QAAQ,EAAEpF,eAAe,KAAK,CAAE;UAChC6C,KAAK,EAAE;YACLa,eAAe,EAAE1D,eAAe,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC9DkD,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE1E,eAAe,KAAK,CAAC,GAAG,aAAa,GAAG;UAClD,CAAE;UAAA+C,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5E,OAAA;UAAKkE,KAAK,EAAE;YAAEG,SAAS,EAAE;UAAS,CAAE;UAAAD,QAAA,eAClCpE,OAAA;YAAKkE,KAAK,EAAE;cACVmB,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,KAAK;cACVD,cAAc,EAAE;YAClB,CAAE;YAAAlB,QAAA,EACClC,WAAW,CAACuD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACxB3F,OAAA;cAEEkE,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE,KAAK;gBACnBD,eAAe,EACbY,KAAK,KAAKtE,eAAe,GAAG,SAAS,GACrCQ,aAAa,CAAC8F,GAAG,CAAChC,KAAK,CAAC,GAAG,SAAS,GAAG;cAC3C;YAAE,GARGA,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5E,OAAA;UACE4F,OAAO,EAAEhC,OAAQ;UACjB6C,QAAQ,EAAEpF,eAAe,KAAKa,WAAW,CAACoB,MAAM,GAAG,CAAE;UACrDY,KAAK,EAAE;YACLa,eAAe,EAAE1D,eAAe,KAAKa,WAAW,CAACoB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACnFiB,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE1E,eAAe,KAAKa,WAAW,CAACoB,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UACvE,CAAE;UAAAc,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD5E,OAAA;MAAKkE,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzCF,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACApE,OAAA;QAAIkE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL5E,OAAA;QAAGkE,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAEF,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAExE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ5E,OAAA;QAAKkE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAC1CJ,eAAe,CAACyB,GAAG,CAAC,CAAC1B,MAAM,EAAE4B,KAAK,kBACjC3F,OAAA;UAAiBkE,KAAK,EAAE;YACtBa,eAAe,EAAE,SAAS;YAC1Bc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBACApE,OAAA;YAAIkE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,GACnDuB,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC5B,MAAM;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACL5E,OAAA;YACE4H,KAAK,EAAE5F,eAAe,CAAC+B,MAAM,CAAC,IAAI,EAAG;YACrC8D,QAAQ,EAAGC,CAAC,IAAKhE,oBAAoB,CAACC,MAAM,EAAE+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC9DI,WAAW,EAAC,0BAA0B;YACtC9D,KAAK,EAAE;cACLiB,KAAK,EAAE,MAAM;cACb8C,SAAS,EAAE,MAAM;cACjBpC,OAAO,EAAE,MAAM;cACfC,MAAM,EAAE,gBAAgB;cACxBd,YAAY,EAAE,KAAK;cACnBR,QAAQ,EAAE,MAAM;cAChBL,UAAU,EAAE,SAAS;cACrB+D,MAAM,EAAE;YACV;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAvBMe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5E,OAAA;QAAKkE,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAEwC,SAAS,EAAE;QAAO,CAAE;QAAAzC,QAAA,eACrDpE,OAAA;UAAKkE,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1Bc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBACApE,OAAA;YAAQkE,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,GAAC,yBACtB,EAAC+D,MAAM,CAACC,IAAI,CAACpG,eAAe,CAAC,CAACsB,MAAM,EAAC,GAAC,EAACU,eAAe,CAACV,MAAM,EAAC,mBAC7E;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5E,OAAA;YAAGkE,KAAK,EAAE;cAAEY,MAAM,EAAE,WAAW;cAAEP,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEpE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1E,EAAA,CAzzBQD,oBAAoB;AAAAoI,EAAA,GAApBpI,oBAAoB;AA2zB7B,eAAeA,oBAAoB;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}