{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\patterns\\\\TwoPointersPattern.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TwoPointersPattern() {\n  _s();\n  var _patternMCQs$currentM, _patternMCQs$currentM2;\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 2, 3, 4, 5, 6, 7, 8, 9]);\n  const [target, setTarget] = useState(10);\n  const [leftPointer, setLeftPointer] = useState(0);\n  const [rightPointer, setRightPointer] = useState(8);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n  const [currentMCQIndex, setCurrentMCQIndex] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState('');\n  const [showMCQResult, setShowMCQResult] = useState(false);\n  const [mcqScore, setMCQScore] = useState(0);\n  const [completedMCQs, setCompletedMCQs] = useState(new Set());\n  const [followUpAnswers, setFollowUpAnswers] = useState({});\n\n  // Get MCQs for Two Pointers pattern\n  const patternMCQs = questionsData[\"1. Two Pointers\"] || [];\n  const steps = [{\n    title: \"🎯 Understanding Two Pointers Pattern\",\n    content: \"The Two Pointers technique uses two pointers to traverse data structures, typically arrays or strings, to solve problems efficiently.\",\n    code: `// Two Pointers Template\nfunction twoPointers(arr) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left < right) {\n        // Process current pair\n        if (condition) {\n            // Found solution\n            return [left, right];\n        } else if (needToMoveLeft) {\n            left++;\n        } else {\n            right--;\n        }\n    }\n    return null;\n}`,\n    learningPrompt: \"Why use two pointers instead of nested loops?\",\n    answer: \"Two pointers reduce time complexity from O(n²) to O(n) by eliminating the need for nested iterations.\"\n  }, {\n    title: \"📋 Step 1: Problem Analysis\",\n    content: \"Identify if the problem can use two pointers: sorted array, finding pairs, palindrome check, or removing duplicates.\",\n    code: `// Two Sum in Sorted Array\nfunction twoSum(numbers, target) {\n    let left = 0;\n    let right = numbers.length - 1;\n    \n    while (left < right) {\n        const sum = numbers[left] + numbers[right];\n        \n        if (sum === target) {\n            return [left, right];\n        } else if (sum < target) {\n            left++;  // Need larger sum\n        } else {\n            right--; // Need smaller sum\n        }\n    }\n    return [];\n}`,\n    learningPrompt: \"When should we move the left pointer vs right pointer?\",\n    answer: \"Move left when we need a larger value, move right when we need a smaller value (in sorted arrays).\"\n  }, {\n    title: \"🔧 Step 2: Initialize Pointers\",\n    content: \"Set up pointers at appropriate positions - usually start and end for opposite direction movement.\",\n    code: `// Initialization patterns\n// Pattern 1: Opposite ends\nlet left = 0, right = arr.length - 1;\n\n// Pattern 2: Same start (fast/slow)\nlet slow = 0, fast = 0;\n\n// Pattern 3: Sliding window\nlet left = 0, right = 0;`,\n    learningPrompt: \"What are the different ways to initialize two pointers?\",\n    answer: \"1) Opposite ends (left=0, right=n-1), 2) Same start (slow=0, fast=0), 3) Sliding window (both start at 0)\"\n  }, {\n    title: \"⚡ Step 3: Movement Logic\",\n    content: \"Define the conditions for moving each pointer based on the problem requirements.\",\n    code: `// Movement strategies\nwhile (left < right) {\n    if (condition_met) {\n        return result;\n    }\n    \n    // Strategy 1: Based on comparison\n    if (sum < target) left++;\n    else right--;\n    \n    // Strategy 2: Always move one\n    if (arr[left] === arr[right]) {\n        left++; right--;\n    }\n    \n    // Strategy 3: Conditional movement\n    if (isValid(left, right)) {\n        process(left, right);\n    }\n    movePointers();\n}`,\n    learningPrompt: \"How do we decide which pointer to move?\",\n    answer: \"Base the decision on problem logic: comparison results, validity checks, or alternating movement patterns.\"\n  }, {\n    title: \"🎮 Step 4: Interactive Demo\",\n    content: \"Watch the two pointers algorithm in action with a live demonstration.\",\n    code: `// Live Demo: Two Sum\nconst numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9];\nconst target = 10;\n\n// Current state:\n// Left pointer: ${leftPointer} (value: ${demoArray[leftPointer]})\n// Right pointer: ${rightPointer} (value: ${demoArray[rightPointer]})\n// Sum: ${demoArray[leftPointer] + demoArray[rightPointer]}`,\n    learningPrompt: \"What happens when sum equals target?\",\n    answer: \"We found our answer! Return the indices or values of the two pointers.\"\n  }, {\n    title: \"🏆 Step 5: Common Variations\",\n    content: \"Master different two-pointer patterns for various problem types.\",\n    code: `// Variation 1: Remove Duplicates\nfunction removeDuplicates(arr) {\n    let writeIndex = 1;\n    for (let readIndex = 1; readIndex < arr.length; readIndex++) {\n        if (arr[readIndex] !== arr[readIndex - 1]) {\n            arr[writeIndex] = arr[readIndex];\n            writeIndex++;\n        }\n    }\n    return writeIndex;\n}\n\n// Variation 2: Palindrome Check\nfunction isPalindrome(s) {\n    let left = 0, right = s.length - 1;\n    while (left < right) {\n        if (s[left] !== s[right]) return false;\n        left++; right--;\n    }\n    return true;\n}`,\n    learningPrompt: \"What are the main variations of two pointers?\",\n    answer: \"1) Opposite direction (palindrome), 2) Same direction (remove duplicates), 3) Fast/slow (cycle detection)\"\n  }];\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    let left = 0;\n    let right = demoArray.length - 1;\n    while (left < right) {\n      setLeftPointer(left);\n      setRightPointer(right);\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      const sum = demoArray[left] + demoArray[right];\n      if (sum === target) {\n        setResult(`Found! ${demoArray[left]} + ${demoArray[right]} = ${target}`);\n        setIsRunning(false);\n        return;\n      } else if (sum < target) {\n        left++;\n      } else {\n        right--;\n      }\n    }\n    setResult(\"No solution found\");\n    setIsRunning(false);\n  };\n  const resetDemo = () => {\n    setLeftPointer(0);\n    setRightPointer(demoArray.length - 1);\n    setResult(null);\n    setIsRunning(false);\n  };\n\n  // MCQ handling functions\n  const handleMCQAnswer = answer => {\n    setSelectedAnswer(answer);\n    setShowMCQResult(true);\n    const currentMCQ = patternMCQs[currentMCQIndex];\n    if (answer === currentMCQ.answer) {\n      setMCQScore(prev => prev + 1);\n    }\n    setCompletedMCQs(prev => new Set([...prev, currentMCQIndex]));\n  };\n  const nextMCQ = () => {\n    if (currentMCQIndex < patternMCQs.length - 1) {\n      setCurrentMCQIndex(prev => prev + 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n  const previousMCQ = () => {\n    if (currentMCQIndex > 0) {\n      setCurrentMCQIndex(prev => prev - 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n  const handleFollowUpAnswer = (prompt, answer) => {\n    setFollowUpAnswers(prev => ({\n      ...prev,\n      [prompt]: answer\n    }));\n  };\n\n  // Follow-up prompts specific to Two Pointers\n  const followUpPrompts = [\"Describe a real-world scenario where you would use the two pointers technique.\", \"What are the key indicators that a problem can be solved using two pointers?\", \"How would you modify the two pointers approach for a 3Sum problem?\", \"Explain the difference between fast/slow pointers and opposite-direction pointers.\", \"What are the common pitfalls when implementing two pointers algorithms?\"];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    style: {\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#27ae60',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83C\\uDFAF Two Pointers Pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em',\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: \"Master the two pointers technique to solve array and string problems efficiently\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#27ae60',\n            height: '100%',\n            width: `${(currentStep + 1) / steps.length * 100}%`,\n            transition: 'width 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0',\n          color: '#7f8c8d'\n        },\n        children: [\"Step \", currentStep + 1, \" of \", steps.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '10px',\n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      },\n      children: steps.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentStep(index),\n        style: {\n          padding: '8px 16px',\n          border: currentStep === index ? '2px solid #27ae60' : '1px solid #ddd',\n          borderRadius: '20px',\n          backgroundColor: currentStep === index ? '#27ae60' : 'white',\n          color: currentStep === index ? 'white' : '#333',\n          cursor: 'pointer',\n          fontSize: '14px'\n        },\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#2c3e50',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          lineHeight: '1.6',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '20px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#2c3e50',\n              marginBottom: '10px',\n              fontSize: '16px'\n            },\n            children: \"\\uD83D\\uDCBB Code Implementation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              backgroundColor: '#f8f9fa',\n              border: '1px solid #e9ecef',\n              borderRadius: '8px',\n              padding: '20px',\n              fontFamily: 'Monaco, Consolas, monospace',\n              fontSize: '14px',\n              overflow: 'auto',\n              height: 'fit-content'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                margin: 0,\n                whiteSpace: 'pre-wrap'\n              },\n              children: steps[currentStep].code\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#8e44ad',\n              marginBottom: '10px',\n              fontSize: '16px'\n            },\n            children: \"\\uD83D\\uDCDD Pseudocode Logic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              backgroundColor: '#f4f1f8',\n              border: '1px solid #d1c4e9',\n              borderRadius: '8px',\n              padding: '20px',\n              fontFamily: 'Georgia, serif',\n              fontSize: '14px',\n              overflow: 'auto',\n              height: 'fit-content'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                margin: 0,\n                whiteSpace: 'pre-wrap',\n                lineHeight: '1.6'\n              },\n              children: steps[currentStep].pseudocode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f0f8ff',\n          border: '2px solid #27ae60',\n          borderRadius: '12px',\n          padding: '20px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#27ae60',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83C\\uDFAE Live Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '5px',\n              justifyContent: 'center',\n              marginBottom: '10px'\n            },\n            children: demoArray.map((num, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '40px',\n                height: '40px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: index === leftPointer ? '#e74c3c' : index === rightPointer ? '#3498db' : '#ecf0f1',\n                color: index === leftPointer || index === rightPointer ? 'white' : '#333',\n                borderRadius: '8px',\n                fontWeight: 'bold',\n                border: '2px solid',\n                borderColor: index === leftPointer ? '#c0392b' : index === rightPointer ? '#2980b9' : '#bdc3c7'\n              },\n              children: num\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#e74c3c'\n              },\n              children: [\"\\uD83D\\uDD34 Left: \", leftPointer]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#3498db'\n              },\n              children: [\"\\uD83D\\uDD35 Right: \", rightPointer]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Target: \", target]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Sum: \", demoArray[leftPointer] + demoArray[rightPointer]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            gap: '10px',\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: runDemo,\n            disabled: isRunning,\n            style: {\n              backgroundColor: '#27ae60',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: isRunning ? 'not-allowed' : 'pointer',\n              opacity: isRunning ? 0.6 : 1\n            },\n            children: isRunning ? '🔄 Running...' : '▶️ Run Demo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetDemo,\n            style: {\n              backgroundColor: '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: \"\\uD83D\\uDD04 Reset\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '10px',\n            backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n            border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n            borderRadius: '6px',\n            textAlign: 'center',\n            fontWeight: 'bold',\n            color: result.includes('Found') ? '#155724' : '#721c24'\n          },\n          children: result\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0'\n          },\n          children: \"\\uD83E\\uDD14 Learning Prompt:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0',\n            fontStyle: 'italic'\n          },\n          children: steps[currentStep].learningPrompt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            style: {\n              color: '#856404',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCA1 Click to see answer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#856404',\n              margin: '10px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: steps[currentStep].answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginTop: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.max(0, currentStep - 1)),\n          disabled: currentStep === 0,\n          style: {\n            backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.min(steps.length - 1, currentStep + 1)),\n          disabled: currentStep === steps.length - 1,\n          style: {\n            backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#27ae60',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #27ae60'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#27ae60',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83C\\uDFAF Practice Problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '15px'\n        },\n        children: [{\n          name: \"Two Sum\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Three Sum\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Remove Duplicates\",\n          difficulty: \"Easy\",\n          time: \"10 min\"\n        }, {\n          name: \"Container With Most Water\",\n          difficulty: \"Medium\",\n          time: \"20 min\"\n        }, {\n          name: \"Valid Palindrome\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Trapping Rain Water\",\n          difficulty: \"Hard\",\n          time: \"35 min\"\n        }].map((problem, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#2c3e50',\n              margin: '0 0 8px 0'\n            },\n            children: problem.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: problem.difficulty === 'Easy' ? '#27ae60' : problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c'\n              },\n              children: problem.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 17\n            }, this), ' • ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: problem.time\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), patternMCQs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px',\n        border: '2px solid #3498db'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#3498db',\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: \"\\uD83D\\uDCDD Test Your Knowledge - Two Pointers MCQs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: [\"Question \", currentMCQIndex + 1, \" of \", patternMCQs.length, \" | Score: \", mcqScore, \"/\", completedMCQs.size, \" | Accuracy: \", completedMCQs.size > 0 ? Math.round(mcqScore / completedMCQs.size * 100) : 0, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          padding: '20px',\n          borderRadius: '8px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#2c3e50',\n            marginBottom: '15px'\n          },\n          children: (_patternMCQs$currentM = patternMCQs[currentMCQIndex]) === null || _patternMCQs$currentM === void 0 ? void 0 : _patternMCQs$currentM.question\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gap: '10px'\n          },\n          children: (_patternMCQs$currentM2 = patternMCQs[currentMCQIndex]) === null || _patternMCQs$currentM2 === void 0 ? void 0 : _patternMCQs$currentM2.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => !showMCQResult && handleMCQAnswer(option),\n            disabled: showMCQResult,\n            style: {\n              padding: '12px 16px',\n              border: '2px solid',\n              borderColor: showMCQResult ? option === patternMCQs[currentMCQIndex].answer ? '#27ae60' : option === selectedAnswer ? '#e74c3c' : '#ddd' : selectedAnswer === option ? '#3498db' : '#ddd',\n              borderRadius: '8px',\n              backgroundColor: showMCQResult ? option === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : option === selectedAnswer ? '#fadbd8' : 'white' : selectedAnswer === option ? '#ebf3fd' : 'white',\n              color: '#2c3e50',\n              cursor: showMCQResult ? 'default' : 'pointer',\n              textAlign: 'left',\n              transition: 'all 0.2s ease'\n            },\n            children: [String.fromCharCode(65 + index), \". \", option, showMCQResult && option === patternMCQs[currentMCQIndex].answer && ' ✅', showMCQResult && option === selectedAnswer && option !== patternMCQs[currentMCQIndex].answer && ' ❌']\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 13\n        }, this), showMCQResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '15px',\n            backgroundColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : '#fadbd8',\n            borderRadius: '8px',\n            border: '1px solid',\n            borderColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            style: {\n              color: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n            },\n            children: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '🎉 Correct!' : '❌ Incorrect'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '5px 0 0 0',\n              color: '#2c3e50'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Correct Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 19\n            }, this), \" \", patternMCQs[currentMCQIndex].answer]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: previousMCQ,\n          disabled: currentMCQIndex === 0,\n          style: {\n            backgroundColor: currentMCQIndex === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '6px',\n            cursor: currentMCQIndex === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous MCQ\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '5px',\n              justifyContent: 'center'\n            },\n            children: patternMCQs.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '12px',\n                height: '12px',\n                borderRadius: '50%',\n                backgroundColor: index === currentMCQIndex ? '#3498db' : completedMCQs.has(index) ? '#27ae60' : '#ddd'\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: nextMCQ,\n          disabled: currentMCQIndex === patternMCQs.length - 1,\n          style: {\n            backgroundColor: currentMCQIndex === patternMCQs.length - 1 ? '#bdc3c7' : '#3498db',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '6px',\n            cursor: currentMCQIndex === patternMCQs.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next MCQ \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        border: '2px solid #f39c12'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#f39c12',\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: \"\\uD83D\\uDCAD Follow-up Learning Prompts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          textAlign: 'center',\n          marginBottom: '25px'\n        },\n        children: \"Deepen your understanding by answering these thought-provoking questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 710,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gap: '20px'\n        },\n        children: followUpPrompts.map((prompt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#fef9e7',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #f1c40f'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#f39c12',\n              marginBottom: '10px'\n            },\n            children: [index + 1, \". \", prompt]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: followUpAnswers[prompt] || '',\n            onChange: e => handleFollowUpAnswer(prompt, e.target.value),\n            placeholder: \"Type your answer here...\",\n            style: {\n              width: '100%',\n              minHeight: '80px',\n              padding: '12px',\n              border: '1px solid #ddd',\n              borderRadius: '6px',\n              fontSize: '14px',\n              fontFamily: 'inherit',\n              resize: 'vertical'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '25px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#e8f5e8',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #27ae60'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            style: {\n              color: '#27ae60'\n            },\n            children: [\"\\uD83D\\uDCCA Progress: \", Object.keys(followUpAnswers).length, \"/\", followUpPrompts.length, \" prompts answered\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '5px 0 0 0',\n              color: '#666',\n              fontSize: '14px'\n            },\n            children: \"Complete all prompts to master the Two Pointers pattern!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n}\n_s(TwoPointersPattern, \"Rioc74+PFFIbPJduRVJlK85CfKE=\");\n_c = TwoPointersPattern;\nexport default TwoPointersPattern;\nvar _c;\n$RefreshReg$(_c, \"TwoPointersPattern\");", "map": {"version": 3, "names": ["React", "useState", "questionsData", "jsxDEV", "_jsxDEV", "TwoPointersPattern", "_s", "_patternMCQs$currentM", "_patternMCQs$currentM2", "currentStep", "setCurrentStep", "demoArray", "setDemoArray", "target", "<PERSON><PERSON><PERSON><PERSON>", "leftPointer", "set<PERSON><PERSON>tPoint<PERSON>", "rightPointer", "setR<PERSON>Pointer", "isRunning", "setIsRunning", "result", "setResult", "currentMCQIndex", "setCurrentMCQIndex", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAnswer", "showMCQResult", "setShowMCQResult", "mcqScore", "setMCQScore", "completedMCQs", "setCompletedMCQs", "Set", "followUpAnswers", "setFollowUpAnswers", "patternMCQs", "steps", "title", "content", "code", "learningPrompt", "answer", "runDemo", "left", "right", "length", "Promise", "resolve", "setTimeout", "sum", "resetDemo", "handleMCQAnswer", "currentMCQ", "prev", "nextMCQ", "previousMCQ", "handleFollowUpAnswer", "prompt", "followUpPrompts", "className", "style", "fontFamily", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "borderRadius", "height", "overflow", "width", "transition", "display", "justifyContent", "gap", "flexWrap", "map", "_", "index", "onClick", "padding", "border", "cursor", "boxShadow", "lineHeight", "gridTemplateColumns", "whiteSpace", "pseudocode", "num", "alignItems", "fontWeight", "borderColor", "disabled", "opacity", "marginTop", "includes", "fontStyle", "paddingLeft", "Math", "max", "min", "name", "difficulty", "time", "problem", "size", "round", "question", "options", "option", "String", "fromCharCode", "has", "value", "onChange", "e", "placeholder", "minHeight", "resize", "Object", "keys", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/patterns/TwoPointersPattern.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\n\nfunction TwoPointersPattern() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 2, 3, 4, 5, 6, 7, 8, 9]);\n  const [target, setTarget] = useState(10);\n  const [leftPointer, setLeftPointer] = useState(0);\n  const [rightPointer, setRightPointer] = useState(8);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n  const [currentMCQIndex, setCurrentMCQIndex] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState('');\n  const [showMCQResult, setShowMCQResult] = useState(false);\n  const [mcqScore, setMCQScore] = useState(0);\n  const [completedMCQs, setCompletedMCQs] = useState(new Set());\n  const [followUpAnswers, setFollowUpAnswers] = useState({});\n\n  // Get MCQs for Two Pointers pattern\n  const patternMCQs = questionsData[\"1. Two Pointers\"] || [];\n\n  const steps = [\n    {\n      title: \"🎯 Understanding Two Pointers Pattern\",\n      content: \"The Two Pointers technique uses two pointers to traverse data structures, typically arrays or strings, to solve problems efficiently.\",\n      code: `// Two Pointers Template\nfunction twoPointers(arr) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left < right) {\n        // Process current pair\n        if (condition) {\n            // Found solution\n            return [left, right];\n        } else if (needToMoveLeft) {\n            left++;\n        } else {\n            right--;\n        }\n    }\n    return null;\n}`,\n      learningPrompt: \"Why use two pointers instead of nested loops?\",\n      answer: \"Two pointers reduce time complexity from O(n²) to O(n) by eliminating the need for nested iterations.\"\n    },\n    {\n      title: \"📋 Step 1: Problem Analysis\",\n      content: \"Identify if the problem can use two pointers: sorted array, finding pairs, palindrome check, or removing duplicates.\",\n      code: `// Two Sum in Sorted Array\nfunction twoSum(numbers, target) {\n    let left = 0;\n    let right = numbers.length - 1;\n    \n    while (left < right) {\n        const sum = numbers[left] + numbers[right];\n        \n        if (sum === target) {\n            return [left, right];\n        } else if (sum < target) {\n            left++;  // Need larger sum\n        } else {\n            right--; // Need smaller sum\n        }\n    }\n    return [];\n}`,\n      learningPrompt: \"When should we move the left pointer vs right pointer?\",\n      answer: \"Move left when we need a larger value, move right when we need a smaller value (in sorted arrays).\"\n    },\n    {\n      title: \"🔧 Step 2: Initialize Pointers\",\n      content: \"Set up pointers at appropriate positions - usually start and end for opposite direction movement.\",\n      code: `// Initialization patterns\n// Pattern 1: Opposite ends\nlet left = 0, right = arr.length - 1;\n\n// Pattern 2: Same start (fast/slow)\nlet slow = 0, fast = 0;\n\n// Pattern 3: Sliding window\nlet left = 0, right = 0;`,\n      learningPrompt: \"What are the different ways to initialize two pointers?\",\n      answer: \"1) Opposite ends (left=0, right=n-1), 2) Same start (slow=0, fast=0), 3) Sliding window (both start at 0)\"\n    },\n    {\n      title: \"⚡ Step 3: Movement Logic\",\n      content: \"Define the conditions for moving each pointer based on the problem requirements.\",\n      code: `// Movement strategies\nwhile (left < right) {\n    if (condition_met) {\n        return result;\n    }\n    \n    // Strategy 1: Based on comparison\n    if (sum < target) left++;\n    else right--;\n    \n    // Strategy 2: Always move one\n    if (arr[left] === arr[right]) {\n        left++; right--;\n    }\n    \n    // Strategy 3: Conditional movement\n    if (isValid(left, right)) {\n        process(left, right);\n    }\n    movePointers();\n}`,\n      learningPrompt: \"How do we decide which pointer to move?\",\n      answer: \"Base the decision on problem logic: comparison results, validity checks, or alternating movement patterns.\"\n    },\n    {\n      title: \"🎮 Step 4: Interactive Demo\",\n      content: \"Watch the two pointers algorithm in action with a live demonstration.\",\n      code: `// Live Demo: Two Sum\nconst numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9];\nconst target = 10;\n\n// Current state:\n// Left pointer: ${leftPointer} (value: ${demoArray[leftPointer]})\n// Right pointer: ${rightPointer} (value: ${demoArray[rightPointer]})\n// Sum: ${demoArray[leftPointer] + demoArray[rightPointer]}`,\n      learningPrompt: \"What happens when sum equals target?\",\n      answer: \"We found our answer! Return the indices or values of the two pointers.\"\n    },\n    {\n      title: \"🏆 Step 5: Common Variations\",\n      content: \"Master different two-pointer patterns for various problem types.\",\n      code: `// Variation 1: Remove Duplicates\nfunction removeDuplicates(arr) {\n    let writeIndex = 1;\n    for (let readIndex = 1; readIndex < arr.length; readIndex++) {\n        if (arr[readIndex] !== arr[readIndex - 1]) {\n            arr[writeIndex] = arr[readIndex];\n            writeIndex++;\n        }\n    }\n    return writeIndex;\n}\n\n// Variation 2: Palindrome Check\nfunction isPalindrome(s) {\n    let left = 0, right = s.length - 1;\n    while (left < right) {\n        if (s[left] !== s[right]) return false;\n        left++; right--;\n    }\n    return true;\n}`,\n      learningPrompt: \"What are the main variations of two pointers?\",\n      answer: \"1) Opposite direction (palindrome), 2) Same direction (remove duplicates), 3) Fast/slow (cycle detection)\"\n    }\n  ];\n\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    let left = 0;\n    let right = demoArray.length - 1;\n\n    while (left < right) {\n      setLeftPointer(left);\n      setRightPointer(right);\n      \n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const sum = demoArray[left] + demoArray[right];\n      \n      if (sum === target) {\n        setResult(`Found! ${demoArray[left]} + ${demoArray[right]} = ${target}`);\n        setIsRunning(false);\n        return;\n      } else if (sum < target) {\n        left++;\n      } else {\n        right--;\n      }\n    }\n    \n    setResult(\"No solution found\");\n    setIsRunning(false);\n  };\n\n  const resetDemo = () => {\n    setLeftPointer(0);\n    setRightPointer(demoArray.length - 1);\n    setResult(null);\n    setIsRunning(false);\n  };\n\n  // MCQ handling functions\n  const handleMCQAnswer = (answer) => {\n    setSelectedAnswer(answer);\n    setShowMCQResult(true);\n\n    const currentMCQ = patternMCQs[currentMCQIndex];\n    if (answer === currentMCQ.answer) {\n      setMCQScore(prev => prev + 1);\n    }\n    setCompletedMCQs(prev => new Set([...prev, currentMCQIndex]));\n  };\n\n  const nextMCQ = () => {\n    if (currentMCQIndex < patternMCQs.length - 1) {\n      setCurrentMCQIndex(prev => prev + 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n\n  const previousMCQ = () => {\n    if (currentMCQIndex > 0) {\n      setCurrentMCQIndex(prev => prev - 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n\n  const handleFollowUpAnswer = (prompt, answer) => {\n    setFollowUpAnswers(prev => ({\n      ...prev,\n      [prompt]: answer\n    }));\n  };\n\n  // Follow-up prompts specific to Two Pointers\n  const followUpPrompts = [\n    \"Describe a real-world scenario where you would use the two pointers technique.\",\n    \"What are the key indicators that a problem can be solved using two pointers?\",\n    \"How would you modify the two pointers approach for a 3Sum problem?\",\n    \"Explain the difference between fast/slow pointers and opposite-direction pointers.\",\n    \"What are the common pitfalls when implementing two pointers algorithms?\"\n  ];\n\n  return (\n    <div className=\"page-content\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Header */}\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#27ae60', fontSize: '2.5em', marginBottom: '10px' }}>\n          🎯 Two Pointers Pattern\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>\n          Master the two pointers technique to solve array and string problems efficiently\n        </p>\n      </div>\n\n      {/* Progress Bar */}\n      <div style={{ marginBottom: '30px' }}>\n        <div style={{\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        }}>\n          <div style={{\n            backgroundColor: '#27ae60',\n            height: '100%',\n            width: `${((currentStep + 1) / steps.length) * 100}%`,\n            transition: 'width 0.3s ease'\n          }}></div>\n        </div>\n        <p style={{ textAlign: 'center', margin: '10px 0', color: '#7f8c8d' }}>\n          Step {currentStep + 1} of {steps.length}\n        </p>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        gap: '10px', \n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      }}>\n        {steps.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentStep(index)}\n            style={{\n              padding: '8px 16px',\n              border: currentStep === index ? '2px solid #27ae60' : '1px solid #ddd',\n              borderRadius: '20px',\n              backgroundColor: currentStep === index ? '#27ae60' : 'white',\n              color: currentStep === index ? 'white' : '#333',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Current Step Content */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      }}>\n        <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>\n          {steps[currentStep].title}\n        </h2>\n        \n        <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>\n          {steps[currentStep].content}\n        </p>\n\n        {/* Code Block with Pseudocode */}\n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>\n          {/* Code Section */}\n          <div>\n            <h4 style={{ color: '#2c3e50', marginBottom: '10px', fontSize: '16px' }}>💻 Code Implementation</h4>\n            <div style={{\n              backgroundColor: '#f8f9fa',\n              border: '1px solid #e9ecef',\n              borderRadius: '8px',\n              padding: '20px',\n              fontFamily: 'Monaco, Consolas, monospace',\n              fontSize: '14px',\n              overflow: 'auto',\n              height: 'fit-content'\n            }}>\n              <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>\n                {steps[currentStep].code}\n              </pre>\n            </div>\n          </div>\n\n          {/* Pseudocode Section */}\n          <div>\n            <h4 style={{ color: '#8e44ad', marginBottom: '10px', fontSize: '16px' }}>📝 Pseudocode Logic</h4>\n            <div style={{\n              backgroundColor: '#f4f1f8',\n              border: '1px solid #d1c4e9',\n              borderRadius: '8px',\n              padding: '20px',\n              fontFamily: 'Georgia, serif',\n              fontSize: '14px',\n              overflow: 'auto',\n              height: 'fit-content'\n            }}>\n              <pre style={{ margin: 0, whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>\n                {steps[currentStep].pseudocode}\n              </pre>\n            </div>\n          </div>\n        </div>\n\n        {/* Interactive Demo for Step 4 */}\n        {currentStep === 4 && (\n          <div style={{\n            backgroundColor: '#f0f8ff',\n            border: '2px solid #27ae60',\n            borderRadius: '12px',\n            padding: '20px',\n            marginBottom: '20px'\n          }}>\n            <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>🎮 Live Demo</h3>\n            \n            {/* Array Visualization */}\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', marginBottom: '10px' }}>\n                {demoArray.map((num, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '40px',\n                      height: '40px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      backgroundColor: \n                        index === leftPointer ? '#e74c3c' :\n                        index === rightPointer ? '#3498db' : '#ecf0f1',\n                      color: (index === leftPointer || index === rightPointer) ? 'white' : '#333',\n                      borderRadius: '8px',\n                      fontWeight: 'bold',\n                      border: '2px solid',\n                      borderColor: \n                        index === leftPointer ? '#c0392b' :\n                        index === rightPointer ? '#2980b9' : '#bdc3c7'\n                    }}\n                  >\n                    {num}\n                  </div>\n                ))}\n              </div>\n              \n              <div style={{ textAlign: 'center', fontSize: '14px', color: '#666' }}>\n                <span style={{ color: '#e74c3c' }}>🔴 Left: {leftPointer}</span>\n                {' | '}\n                <span style={{ color: '#3498db' }}>🔵 Right: {rightPointer}</span>\n                {' | '}\n                <span>Target: {target}</span>\n                {' | '}\n                <span>Sum: {demoArray[leftPointer] + demoArray[rightPointer]}</span>\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div style={{ textAlign: 'center', gap: '10px', display: 'flex', justifyContent: 'center' }}>\n              <button\n                onClick={runDemo}\n                disabled={isRunning}\n                style={{\n                  backgroundColor: '#27ae60',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: isRunning ? 'not-allowed' : 'pointer',\n                  opacity: isRunning ? 0.6 : 1\n                }}\n              >\n                {isRunning ? '🔄 Running...' : '▶️ Run Demo'}\n              </button>\n              \n              <button\n                onClick={resetDemo}\n                style={{\n                  backgroundColor: '#95a5a6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: 'pointer'\n                }}\n              >\n                🔄 Reset\n              </button>\n            </div>\n\n            {result && (\n              <div style={{\n                marginTop: '15px',\n                padding: '10px',\n                backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n                border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n                borderRadius: '6px',\n                textAlign: 'center',\n                fontWeight: 'bold',\n                color: result.includes('Found') ? '#155724' : '#721c24'\n              }}>\n                {result}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Learning Prompt */}\n        <div style={{\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        }}>\n          <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>\n            🤔 Learning Prompt:\n          </h4>\n          <p style={{ color: '#856404', margin: '0 0 10px 0', fontStyle: 'italic' }}>\n            {steps[currentStep].learningPrompt}\n          </p>\n          <details>\n            <summary style={{ color: '#856404', cursor: 'pointer', fontWeight: 'bold' }}>\n              💡 Click to see answer\n            </summary>\n            <p style={{ color: '#856404', margin: '10px 0 0 0', paddingLeft: '20px' }}>\n              {steps[currentStep].answer}\n            </p>\n          </details>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '30px' }}>\n          <button\n            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}\n            disabled={currentStep === 0}\n            style={{\n              backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            ← Previous\n          </button>\n          \n          <button\n            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}\n            disabled={currentStep === steps.length - 1}\n            style={{\n              backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#27ae60',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next →\n          </button>\n        </div>\n      </div>\n\n      {/* Practice Problems */}\n      <div style={{\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #27ae60'\n      }}>\n        <h3 style={{ color: '#27ae60', marginBottom: '20px' }}>\n          🎯 Practice Problems\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\n          {[\n            { name: \"Two Sum\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Three Sum\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Remove Duplicates\", difficulty: \"Easy\", time: \"10 min\" },\n            { name: \"Container With Most Water\", difficulty: \"Medium\", time: \"20 min\" },\n            { name: \"Valid Palindrome\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Trapping Rain Water\", difficulty: \"Hard\", time: \"35 min\" }\n          ].map((problem, index) => (\n            <div\n              key={index}\n              style={{\n                backgroundColor: 'white',\n                padding: '15px',\n                borderRadius: '8px',\n                border: '1px solid #dee2e6'\n              }}\n            >\n              <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0' }}>{problem.name}</h4>\n              <div style={{ fontSize: '14px', color: '#666' }}>\n                <span style={{ \n                  color: problem.difficulty === 'Easy' ? '#27ae60' : \n                        problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c' \n                }}>\n                  {problem.difficulty}\n                </span>\n                {' • '}\n                <span>{problem.time}</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* MCQ Section */}\n      {patternMCQs.length > 0 && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          padding: '30px',\n          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n          marginBottom: '30px',\n          border: '2px solid #3498db'\n        }}>\n          <h3 style={{ color: '#3498db', marginBottom: '20px', textAlign: 'center' }}>\n            📝 Test Your Knowledge - Two Pointers MCQs\n          </h3>\n\n          {/* MCQ Progress */}\n          <div style={{ marginBottom: '20px', textAlign: 'center' }}>\n            <span style={{ color: '#666', fontSize: '14px' }}>\n              Question {currentMCQIndex + 1} of {patternMCQs.length} |\n              Score: {mcqScore}/{completedMCQs.size} |\n              Accuracy: {completedMCQs.size > 0 ? Math.round((mcqScore / completedMCQs.size) * 100) : 0}%\n            </span>\n          </div>\n\n          {/* Current MCQ */}\n          <div style={{\n            backgroundColor: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            marginBottom: '20px'\n          }}>\n            <h4 style={{ color: '#2c3e50', marginBottom: '15px' }}>\n              {patternMCQs[currentMCQIndex]?.question}\n            </h4>\n\n            <div style={{ display: 'grid', gap: '10px' }}>\n              {patternMCQs[currentMCQIndex]?.options.map((option, index) => (\n                <button\n                  key={index}\n                  onClick={() => !showMCQResult && handleMCQAnswer(option)}\n                  disabled={showMCQResult}\n                  style={{\n                    padding: '12px 16px',\n                    border: '2px solid',\n                    borderColor: showMCQResult\n                      ? (option === patternMCQs[currentMCQIndex].answer ? '#27ae60'\n                         : option === selectedAnswer ? '#e74c3c' : '#ddd')\n                      : (selectedAnswer === option ? '#3498db' : '#ddd'),\n                    borderRadius: '8px',\n                    backgroundColor: showMCQResult\n                      ? (option === patternMCQs[currentMCQIndex].answer ? '#d5f4e6'\n                         : option === selectedAnswer ? '#fadbd8' : 'white')\n                      : (selectedAnswer === option ? '#ebf3fd' : 'white'),\n                    color: '#2c3e50',\n                    cursor: showMCQResult ? 'default' : 'pointer',\n                    textAlign: 'left',\n                    transition: 'all 0.2s ease'\n                  }}\n                >\n                  {String.fromCharCode(65 + index)}. {option}\n                  {showMCQResult && option === patternMCQs[currentMCQIndex].answer && ' ✅'}\n                  {showMCQResult && option === selectedAnswer && option !== patternMCQs[currentMCQIndex].answer && ' ❌'}\n                </button>\n              ))}\n            </div>\n\n            {showMCQResult && (\n              <div style={{\n                marginTop: '15px',\n                padding: '15px',\n                backgroundColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : '#fadbd8',\n                borderRadius: '8px',\n                border: '1px solid',\n                borderColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n              }}>\n                <strong style={{\n                  color: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n                }}>\n                  {selectedAnswer === patternMCQs[currentMCQIndex].answer ? '🎉 Correct!' : '❌ Incorrect'}\n                </strong>\n                <p style={{ margin: '5px 0 0 0', color: '#2c3e50' }}>\n                  <strong>Correct Answer:</strong> {patternMCQs[currentMCQIndex].answer}\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* MCQ Navigation */}\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <button\n              onClick={previousMCQ}\n              disabled={currentMCQIndex === 0}\n              style={{\n                backgroundColor: currentMCQIndex === 0 ? '#bdc3c7' : '#95a5a6',\n                color: 'white',\n                border: 'none',\n                padding: '10px 20px',\n                borderRadius: '6px',\n                cursor: currentMCQIndex === 0 ? 'not-allowed' : 'pointer'\n              }}\n            >\n              ← Previous MCQ\n            </button>\n\n            <div style={{ textAlign: 'center' }}>\n              <div style={{\n                display: 'flex',\n                gap: '5px',\n                justifyContent: 'center'\n              }}>\n                {patternMCQs.map((_, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '12px',\n                      height: '12px',\n                      borderRadius: '50%',\n                      backgroundColor:\n                        index === currentMCQIndex ? '#3498db' :\n                        completedMCQs.has(index) ? '#27ae60' : '#ddd'\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n\n            <button\n              onClick={nextMCQ}\n              disabled={currentMCQIndex === patternMCQs.length - 1}\n              style={{\n                backgroundColor: currentMCQIndex === patternMCQs.length - 1 ? '#bdc3c7' : '#3498db',\n                color: 'white',\n                border: 'none',\n                padding: '10px 20px',\n                borderRadius: '6px',\n                cursor: currentMCQIndex === patternMCQs.length - 1 ? 'not-allowed' : 'pointer'\n              }}\n            >\n              Next MCQ →\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Follow-up Prompts Section */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        border: '2px solid #f39c12'\n      }}>\n        <h3 style={{ color: '#f39c12', marginBottom: '20px', textAlign: 'center' }}>\n          💭 Follow-up Learning Prompts\n        </h3>\n        <p style={{ color: '#666', textAlign: 'center', marginBottom: '25px' }}>\n          Deepen your understanding by answering these thought-provoking questions\n        </p>\n\n        <div style={{ display: 'grid', gap: '20px' }}>\n          {followUpPrompts.map((prompt, index) => (\n            <div key={index} style={{\n              backgroundColor: '#fef9e7',\n              padding: '20px',\n              borderRadius: '8px',\n              border: '1px solid #f1c40f'\n            }}>\n              <h4 style={{ color: '#f39c12', marginBottom: '10px' }}>\n                {index + 1}. {prompt}\n              </h4>\n              <textarea\n                value={followUpAnswers[prompt] || ''}\n                onChange={(e) => handleFollowUpAnswer(prompt, e.target.value)}\n                placeholder=\"Type your answer here...\"\n                style={{\n                  width: '100%',\n                  minHeight: '80px',\n                  padding: '12px',\n                  border: '1px solid #ddd',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  fontFamily: 'inherit',\n                  resize: 'vertical'\n                }}\n              />\n            </div>\n          ))}\n        </div>\n\n        <div style={{ textAlign: 'center', marginTop: '25px' }}>\n          <div style={{\n            backgroundColor: '#e8f5e8',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #27ae60'\n          }}>\n            <strong style={{ color: '#27ae60' }}>\n              📊 Progress: {Object.keys(followUpAnswers).length}/{followUpPrompts.length} prompts answered\n            </strong>\n            <p style={{ margin: '5px 0 0 0', color: '#666', fontSize: '14px' }}>\n              Complete all prompts to master the Two Pointers pattern!\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default TwoPointersPattern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACvE,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAIgC,GAAG,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAMmC,WAAW,GAAGlC,aAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE;EAE1D,MAAMmC,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,uCAAuC;IAC9CC,OAAO,EAAE,uIAAuI;IAChJC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,sHAAsH;IAC/HC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,wDAAwD;IACxEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,gCAAgC;IACvCC,OAAO,EAAE,mGAAmG;IAC5GC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;IACnBC,cAAc,EAAE,yDAAyD;IACzEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,0BAA0B;IACjCC,OAAO,EAAE,kFAAkF;IAC3FC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,yCAAyC;IACzDC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,uEAAuE;IAChFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA,mBAAmBzB,WAAW,YAAYJ,SAAS,CAACI,WAAW,CAAC;AAChE,oBAAoBE,YAAY,YAAYN,SAAS,CAACM,YAAY,CAAC;AACnE,UAAUN,SAAS,CAACI,WAAW,CAAC,GAAGJ,SAAS,CAACM,YAAY,CAAC,EAAE;IACtDwB,cAAc,EAAE,sCAAsC;IACtDC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE,kEAAkE;IAC3EC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1BvB,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,IAAI,CAAC;IACf,IAAIsB,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAGlC,SAAS,CAACmC,MAAM,GAAG,CAAC;IAEhC,OAAOF,IAAI,GAAGC,KAAK,EAAE;MACnB7B,cAAc,CAAC4B,IAAI,CAAC;MACpB1B,eAAe,CAAC2B,KAAK,CAAC;MAEtB,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,GAAG,GAAGvC,SAAS,CAACiC,IAAI,CAAC,GAAGjC,SAAS,CAACkC,KAAK,CAAC;MAE9C,IAAIK,GAAG,KAAKrC,MAAM,EAAE;QAClBS,SAAS,CAAC,UAAUX,SAAS,CAACiC,IAAI,CAAC,MAAMjC,SAAS,CAACkC,KAAK,CAAC,MAAMhC,MAAM,EAAE,CAAC;QACxEO,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAI8B,GAAG,GAAGrC,MAAM,EAAE;QACvB+B,IAAI,EAAE;MACR,CAAC,MAAM;QACLC,KAAK,EAAE;MACT;IACF;IAEAvB,SAAS,CAAC,mBAAmB,CAAC;IAC9BF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM+B,SAAS,GAAGA,CAAA,KAAM;IACtBnC,cAAc,CAAC,CAAC,CAAC;IACjBE,eAAe,CAACP,SAAS,CAACmC,MAAM,GAAG,CAAC,CAAC;IACrCxB,SAAS,CAAC,IAAI,CAAC;IACfF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgC,eAAe,GAAIV,MAAM,IAAK;IAClChB,iBAAiB,CAACgB,MAAM,CAAC;IACzBd,gBAAgB,CAAC,IAAI,CAAC;IAEtB,MAAMyB,UAAU,GAAGjB,WAAW,CAACb,eAAe,CAAC;IAC/C,IAAImB,MAAM,KAAKW,UAAU,CAACX,MAAM,EAAE;MAChCZ,WAAW,CAACwB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B;IACAtB,gBAAgB,CAACsB,IAAI,IAAI,IAAIrB,GAAG,CAAC,CAAC,GAAGqB,IAAI,EAAE/B,eAAe,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMgC,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIhC,eAAe,GAAGa,WAAW,CAACU,MAAM,GAAG,CAAC,EAAE;MAC5CtB,kBAAkB,CAAC8B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACpC5B,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM4B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIjC,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAAC8B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACpC5B,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM6B,oBAAoB,GAAGA,CAACC,MAAM,EAAEhB,MAAM,KAAK;IAC/CP,kBAAkB,CAACmB,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACI,MAAM,GAAGhB;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMiB,eAAe,GAAG,CACtB,gFAAgF,EAChF,8EAA8E,EAC9E,oEAAoE,EACpE,oFAAoF,EACpF,yEAAyE,CAC1E;EAED,oBACEvD,OAAA;IAAKwD,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAEvE3D,OAAA;MAAKyD,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxD3D,OAAA;QAAIyD,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEF,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLnE,OAAA;QAAGyD,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEK,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAExF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnE,OAAA;MAAKyD,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnC3D,OAAA;QAAKyD,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACA3D,OAAA;UAAKyD,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,MAAM;YACdE,KAAK,EAAE,GAAI,CAACrE,WAAW,GAAG,CAAC,IAAI4B,KAAK,CAACS,MAAM,GAAI,GAAG,GAAG;YACrDiC,UAAU,EAAE;UACd;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnE,OAAA;QAAGyD,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAES,MAAM,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAH,QAAA,GAAC,OAChE,EAACtD,WAAW,GAAG,CAAC,EAAC,MAAI,EAAC4B,KAAK,CAACS,MAAM;MAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnE,OAAA;MAAKyD,KAAK,EAAE;QACVmB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXjB,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE;MACZ,CAAE;MAAApB,QAAA,EACC1B,KAAK,CAAC+C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAClBlF,OAAA;QAEEmF,OAAO,EAAEA,CAAA,KAAM7E,cAAc,CAAC4E,KAAK,CAAE;QACrCzB,KAAK,EAAE;UACL2B,OAAO,EAAE,UAAU;UACnBC,MAAM,EAAEhF,WAAW,KAAK6E,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;UACtEX,YAAY,EAAE,MAAM;UACpBD,eAAe,EAAEjE,WAAW,KAAK6E,KAAK,GAAG,SAAS,GAAG,OAAO;UAC5DpB,KAAK,EAAEzD,WAAW,KAAK6E,KAAK,GAAG,OAAO,GAAG,MAAM;UAC/CI,MAAM,EAAE,SAAS;UACjBvB,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EAEDuB,KAAK,GAAG;MAAC,GAZLA,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNnE,OAAA;MAAKyD,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACA3D,OAAA;QAAIyD,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnD1B,KAAK,CAAC5B,WAAW,CAAC,CAAC6B;MAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAELnE,OAAA;QAAGyD,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAE0B,UAAU,EAAE,KAAK;UAAE3B,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClE1B,KAAK,CAAC5B,WAAW,CAAC,CAAC8B;MAAO;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAGJnE,OAAA;QAAKyD,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEa,mBAAmB,EAAE,SAAS;UAAEX,GAAG,EAAE,MAAM;UAAEjB,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBAEjG3D,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAIyD,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE,MAAM;cAAEE,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpGnE,OAAA;YAAKyD,KAAK,EAAE;cACVa,eAAe,EAAE,SAAS;cAC1Be,MAAM,EAAE,mBAAmB;cAC3Bd,YAAY,EAAE,KAAK;cACnBa,OAAO,EAAE,MAAM;cACf1B,UAAU,EAAE,6BAA6B;cACzCK,QAAQ,EAAE,MAAM;cAChBU,QAAQ,EAAE,MAAM;cAChBD,MAAM,EAAE;YACV,CAAE;YAAAb,QAAA,eACA3D,OAAA;cAAKyD,KAAK,EAAE;gBAAEY,MAAM,EAAE,CAAC;gBAAEqB,UAAU,EAAE;cAAW,CAAE;cAAA/B,QAAA,EAC/C1B,KAAK,CAAC5B,WAAW,CAAC,CAAC+B;YAAI;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnE,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAIyD,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE,MAAM;cAAEE,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAAmB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjGnE,OAAA;YAAKyD,KAAK,EAAE;cACVa,eAAe,EAAE,SAAS;cAC1Be,MAAM,EAAE,mBAAmB;cAC3Bd,YAAY,EAAE,KAAK;cACnBa,OAAO,EAAE,MAAM;cACf1B,UAAU,EAAE,gBAAgB;cAC5BK,QAAQ,EAAE,MAAM;cAChBU,QAAQ,EAAE,MAAM;cAChBD,MAAM,EAAE;YACV,CAAE;YAAAb,QAAA,eACA3D,OAAA;cAAKyD,KAAK,EAAE;gBAAEY,MAAM,EAAE,CAAC;gBAAEqB,UAAU,EAAE,UAAU;gBAAEF,UAAU,EAAE;cAAM,CAAE;cAAA7B,QAAA,EAClE1B,KAAK,CAAC5B,WAAW,CAAC,CAACsF;YAAU;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL9D,WAAW,KAAK,CAAC,iBAChBL,OAAA;QAAKyD,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,MAAM;UACpBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACA3D,OAAA;UAAIyD,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGxEnE,OAAA;UAAKyD,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC3D,OAAA;YAAKyD,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,KAAK;cAAED,cAAc,EAAE,QAAQ;cAAEhB,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EACzFpD,SAAS,CAACyE,GAAG,CAAC,CAACY,GAAG,EAAEV,KAAK,kBACxBlF,OAAA;cAEEyD,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdI,OAAO,EAAE,MAAM;gBACfiB,UAAU,EAAE,QAAQ;gBACpBhB,cAAc,EAAE,QAAQ;gBACxBP,eAAe,EACbY,KAAK,KAAKvE,WAAW,GAAG,SAAS,GACjCuE,KAAK,KAAKrE,YAAY,GAAG,SAAS,GAAG,SAAS;gBAChDiD,KAAK,EAAGoB,KAAK,KAAKvE,WAAW,IAAIuE,KAAK,KAAKrE,YAAY,GAAI,OAAO,GAAG,MAAM;gBAC3E0D,YAAY,EAAE,KAAK;gBACnBuB,UAAU,EAAE,MAAM;gBAClBT,MAAM,EAAE,WAAW;gBACnBU,WAAW,EACTb,KAAK,KAAKvE,WAAW,GAAG,SAAS,GACjCuE,KAAK,KAAKrE,YAAY,GAAG,SAAS,GAAG;cACzC,CAAE;cAAA8C,QAAA,EAEDiC;YAAG,GAnBCV,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnE,OAAA;YAAKyD,KAAK,EAAE;cAAEG,SAAS,EAAE,QAAQ;cAAEG,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnE3D,OAAA;cAAMyD,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,qBAAS,EAAChD,WAAW;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC/D,KAAK,eACNnE,OAAA;cAAMyD,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,sBAAU,EAAC9C,YAAY;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACjE,KAAK,eACNnE,OAAA;cAAA2D,QAAA,GAAM,UAAQ,EAAClD,MAAM;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC5B,KAAK,eACNnE,OAAA;cAAA2D,QAAA,GAAM,OAAK,EAACpD,SAAS,CAACI,WAAW,CAAC,GAAGJ,SAAS,CAACM,YAAY,CAAC;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnE,OAAA;UAAKyD,KAAK,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEkB,GAAG,EAAE,MAAM;YAAEF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBAC1F3D,OAAA;YACEmF,OAAO,EAAE5C,OAAQ;YACjByD,QAAQ,EAAEjF,SAAU;YACpB0C,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAEvE,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CkF,OAAO,EAAElF,SAAS,GAAG,GAAG,GAAG;YAC7B,CAAE;YAAA4C,QAAA,EAED5C,SAAS,GAAG,eAAe,GAAG;UAAa;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAETnE,OAAA;YACEmF,OAAO,EAAEpC,SAAU;YACnBU,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELlD,MAAM,iBACLjB,OAAA;UAAKyD,KAAK,EAAE;YACVyC,SAAS,EAAE,MAAM;YACjBd,OAAO,EAAE,MAAM;YACfd,eAAe,EAAErD,MAAM,CAACkF,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS;YACjEd,MAAM,EAAE,aAAapE,MAAM,CAACkF,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE;YACvE5B,YAAY,EAAE,KAAK;YACnBX,SAAS,EAAE,QAAQ;YACnBkC,UAAU,EAAE,MAAM;YAClBhC,KAAK,EAAE7C,MAAM,CAACkF,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG;UAChD,CAAE;UAAAxC,QAAA,EACC1C;QAAM;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGDnE,OAAA;QAAKyD,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACA3D,OAAA;UAAIyD,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE;UAAa,CAAE;UAAAV,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnE,OAAA;UAAGyD,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE,YAAY;YAAE+B,SAAS,EAAE;UAAS,CAAE;UAAAzC,QAAA,EACvE1B,KAAK,CAAC5B,WAAW,CAAC,CAACgC;QAAc;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJnE,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAASyD,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEwB,MAAM,EAAE,SAAS;cAAEQ,UAAU,EAAE;YAAO,CAAE;YAAAnC,QAAA,EAAC;UAE7E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACVnE,OAAA;YAAGyD,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE,YAAY;cAAEgC,WAAW,EAAE;YAAO,CAAE;YAAA1C,QAAA,EACvE1B,KAAK,CAAC5B,WAAW,CAAC,CAACiC;UAAM;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGNnE,OAAA;QAAKyD,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEqB,SAAS,EAAE;QAAO,CAAE;QAAAvC,QAAA,gBAClF3D,OAAA;UACEmF,OAAO,EAAEA,CAAA,KAAM7E,cAAc,CAACgG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElG,WAAW,GAAG,CAAC,CAAC,CAAE;UAC5D2F,QAAQ,EAAE3F,WAAW,KAAK,CAAE;UAC5BoD,KAAK,EAAE;YACLa,eAAe,EAAEjE,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC1DyD,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEjF,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;UAC9C,CAAE;UAAAsD,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETnE,OAAA;UACEmF,OAAO,EAAEA,CAAA,KAAM7E,cAAc,CAACgG,IAAI,CAACE,GAAG,CAACvE,KAAK,CAACS,MAAM,GAAG,CAAC,EAAErC,WAAW,GAAG,CAAC,CAAC,CAAE;UAC3E2F,QAAQ,EAAE3F,WAAW,KAAK4B,KAAK,CAACS,MAAM,GAAG,CAAE;UAC3Ce,KAAK,EAAE;YACLa,eAAe,EAAEjE,WAAW,KAAK4B,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACzEoB,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEjF,WAAW,KAAK4B,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UAC7D,CAAE;UAAAiB,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA;MAAKyD,KAAK,EAAE;QACVa,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACA3D,OAAA;QAAIyD,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEvD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnE,OAAA;QAAKyD,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEa,mBAAmB,EAAE,sCAAsC;UAAEX,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EACvG,CACC;UAAE8C,IAAI,EAAE,SAAS;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACvD;UAAEF,IAAI,EAAE,WAAW;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC3D;UAAEF,IAAI,EAAE,mBAAmB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACjE;UAAEF,IAAI,EAAE,2BAA2B;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC3E;UAAEF,IAAI,EAAE,kBAAkB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EAChE;UAAEF,IAAI,EAAE,qBAAqB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,CACpE,CAAC3B,GAAG,CAAC,CAAC4B,OAAO,EAAE1B,KAAK,kBACnBlF,OAAA;UAEEyD,KAAK,EAAE;YACLa,eAAe,EAAE,OAAO;YACxBc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBAEF3D,OAAA;YAAIyD,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE;YAAY,CAAE;YAAAV,QAAA,EAAEiD,OAAO,CAACH;UAAI;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzEnE,OAAA;YAAKyD,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBAC9C3D,OAAA;cAAMyD,KAAK,EAAE;gBACXK,KAAK,EAAE8C,OAAO,CAACF,UAAU,KAAK,MAAM,GAAG,SAAS,GAC1CE,OAAO,CAACF,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG;cACtD,CAAE;cAAA/C,QAAA,EACCiD,OAAO,CAACF;YAAU;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACN,KAAK,eACNnE,OAAA;cAAA2D,QAAA,EAAOiD,OAAO,CAACD;YAAI;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA,GAlBDe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLnC,WAAW,CAACU,MAAM,GAAG,CAAC,iBACrB1C,OAAA;MAAKyD,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE,MAAM;QACpBwB,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACA3D,OAAA;QAAIyD,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGLnE,OAAA;QAAKyD,KAAK,EAAE;UAAEI,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,eACxD3D,OAAA;UAAMyD,KAAK,EAAE;YAAEK,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAJ,QAAA,GAAC,WACvC,EAACxC,eAAe,GAAG,CAAC,EAAC,MAAI,EAACa,WAAW,CAACU,MAAM,EAAC,YAC/C,EAACjB,QAAQ,EAAC,GAAC,EAACE,aAAa,CAACkF,IAAI,EAAC,eAC5B,EAAClF,aAAa,CAACkF,IAAI,GAAG,CAAC,GAAGP,IAAI,CAACQ,KAAK,CAAErF,QAAQ,GAAGE,aAAa,CAACkF,IAAI,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC5F;QAAA;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNnE,OAAA;QAAKyD,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Bc,OAAO,EAAE,MAAM;UACfb,YAAY,EAAE,KAAK;UACnBV,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACA3D,OAAA;UAAIyD,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,GAAAxD,qBAAA,GACnD6B,WAAW,CAACb,eAAe,CAAC,cAAAhB,qBAAA,uBAA5BA,qBAAA,CAA8B4G;QAAQ;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAELnE,OAAA;UAAKyD,KAAK,EAAE;YAAEmB,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAnB,QAAA,GAAAvD,sBAAA,GAC1C4B,WAAW,CAACb,eAAe,CAAC,cAAAf,sBAAA,uBAA5BA,sBAAA,CAA8B4G,OAAO,CAAChC,GAAG,CAAC,CAACiC,MAAM,EAAE/B,KAAK,kBACvDlF,OAAA;YAEEmF,OAAO,EAAEA,CAAA,KAAM,CAAC5D,aAAa,IAAIyB,eAAe,CAACiE,MAAM,CAAE;YACzDjB,QAAQ,EAAEzE,aAAc;YACxBkC,KAAK,EAAE;cACL2B,OAAO,EAAE,WAAW;cACpBC,MAAM,EAAE,WAAW;cACnBU,WAAW,EAAExE,aAAa,GACrB0F,MAAM,KAAKjF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GACxD2E,MAAM,KAAK5F,cAAc,GAAG,SAAS,GAAG,MAAM,GAChDA,cAAc,KAAK4F,MAAM,GAAG,SAAS,GAAG,MAAO;cACpD1C,YAAY,EAAE,KAAK;cACnBD,eAAe,EAAE/C,aAAa,GACzB0F,MAAM,KAAKjF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GACxD2E,MAAM,KAAK5F,cAAc,GAAG,SAAS,GAAG,OAAO,GACjDA,cAAc,KAAK4F,MAAM,GAAG,SAAS,GAAG,OAAQ;cACrDnD,KAAK,EAAE,SAAS;cAChBwB,MAAM,EAAE/D,aAAa,GAAG,SAAS,GAAG,SAAS;cAC7CqC,SAAS,EAAE,MAAM;cACjBe,UAAU,EAAE;YACd,CAAE;YAAAhB,QAAA,GAEDuD,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGjC,KAAK,CAAC,EAAC,IAAE,EAAC+B,MAAM,EACzC1F,aAAa,IAAI0F,MAAM,KAAKjF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,IAAI,IAAI,EACvEf,aAAa,IAAI0F,MAAM,KAAK5F,cAAc,IAAI4F,MAAM,KAAKjF,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,IAAI,IAAI;UAAA,GAvBhG4C,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL5C,aAAa,iBACZvB,OAAA;UAAKyD,KAAK,EAAE;YACVyC,SAAS,EAAE,MAAM;YACjBd,OAAO,EAAE,MAAM;YACfd,eAAe,EAAEjD,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG,SAAS;YAC/FiC,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE,WAAW;YACnBU,WAAW,EAAE1E,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG;UACpF,CAAE;UAAAqB,QAAA,gBACA3D,OAAA;YAAQyD,KAAK,EAAE;cACbK,KAAK,EAAEzC,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG;YAC9E,CAAE;YAAAqB,QAAA,EACCtC,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,aAAa,GAAG;UAAa;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACTnE,OAAA;YAAGyD,KAAK,EAAE;cAAEY,MAAM,EAAE,WAAW;cAAEP,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,gBAClD3D,OAAA;cAAA2D,QAAA,EAAQ;YAAe;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACnC,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnE,OAAA;QAAKyD,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEgB,UAAU,EAAE;QAAS,CAAE;QAAAlC,QAAA,gBACrF3D,OAAA;UACEmF,OAAO,EAAE/B,WAAY;UACrB4C,QAAQ,EAAE7E,eAAe,KAAK,CAAE;UAChCsC,KAAK,EAAE;YACLa,eAAe,EAAEnD,eAAe,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC9D2C,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEnE,eAAe,KAAK,CAAC,GAAG,aAAa,GAAG;UAClD,CAAE;UAAAwC,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETnE,OAAA;UAAKyD,KAAK,EAAE;YAAEG,SAAS,EAAE;UAAS,CAAE;UAAAD,QAAA,eAClC3D,OAAA;YAAKyD,KAAK,EAAE;cACVmB,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,KAAK;cACVD,cAAc,EAAE;YAClB,CAAE;YAAAlB,QAAA,EACC3B,WAAW,CAACgD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACxBlF,OAAA;cAEEyD,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE,KAAK;gBACnBD,eAAe,EACbY,KAAK,KAAK/D,eAAe,GAAG,SAAS,GACrCQ,aAAa,CAACyF,GAAG,CAAClC,KAAK,CAAC,GAAG,SAAS,GAAG;cAC3C;YAAE,GARGA,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnE,OAAA;UACEmF,OAAO,EAAEhC,OAAQ;UACjB6C,QAAQ,EAAE7E,eAAe,KAAKa,WAAW,CAACU,MAAM,GAAG,CAAE;UACrDe,KAAK,EAAE;YACLa,eAAe,EAAEnD,eAAe,KAAKa,WAAW,CAACU,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACnFoB,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEnE,eAAe,KAAKa,WAAW,CAACU,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UACvE,CAAE;UAAAiB,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDnE,OAAA;MAAKyD,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzCF,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACA3D,OAAA;QAAIyD,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLnE,OAAA;QAAGyD,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAEF,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAExE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJnE,OAAA;QAAKyD,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAC1CJ,eAAe,CAACyB,GAAG,CAAC,CAAC1B,MAAM,EAAE4B,KAAK,kBACjClF,OAAA;UAAiByD,KAAK,EAAE;YACtBa,eAAe,EAAE,SAAS;YAC1Bc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBACA3D,OAAA;YAAIyD,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,GACnDuB,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC5B,MAAM;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACLnE,OAAA;YACEqH,KAAK,EAAEvF,eAAe,CAACwB,MAAM,CAAC,IAAI,EAAG;YACrCgE,QAAQ,EAAGC,CAAC,IAAKlE,oBAAoB,CAACC,MAAM,EAAEiE,CAAC,CAAC9G,MAAM,CAAC4G,KAAK,CAAE;YAC9DG,WAAW,EAAC,0BAA0B;YACtC/D,KAAK,EAAE;cACLiB,KAAK,EAAE,MAAM;cACb+C,SAAS,EAAE,MAAM;cACjBrC,OAAO,EAAE,MAAM;cACfC,MAAM,EAAE,gBAAgB;cACxBd,YAAY,EAAE,KAAK;cACnBR,QAAQ,EAAE,MAAM;cAChBL,UAAU,EAAE,SAAS;cACrBgE,MAAM,EAAE;YACV;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAvBMe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENnE,OAAA;QAAKyD,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAEsC,SAAS,EAAE;QAAO,CAAE;QAAAvC,QAAA,eACrD3D,OAAA;UAAKyD,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1Bc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBACA3D,OAAA;YAAQyD,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,GAAC,yBACtB,EAACgE,MAAM,CAACC,IAAI,CAAC9F,eAAe,CAAC,CAACY,MAAM,EAAC,GAAC,EAACa,eAAe,CAACb,MAAM,EAAC,mBAC7E;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA;YAAGyD,KAAK,EAAE;cAAEY,MAAM,EAAE,WAAW;cAAEP,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEpE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjE,EAAA,CAtvBQD,kBAAkB;AAAA4H,EAAA,GAAlB5H,kBAAkB;AAwvB3B,eAAeA,kBAAkB;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}