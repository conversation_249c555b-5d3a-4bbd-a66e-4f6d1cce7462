{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\MainPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport questionsData from './mcqs.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MainPage() {\n  _s();\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState(null);\n  const [promptInputAnswers, setPromptInputAnswers] = useState({});\n  const [selectedPrompts, setSelectedPrompts] = useState([]);\n  const [report, setReport] = useState('');\n  useEffect(() => {\n    const allQuestions = Object.values(questionsData).flat();\n    setQuestions(allQuestions);\n    const savedIndex = localStorage.getItem('currentQuestionIndex');\n    if (savedIndex !== null) {\n      setCurrentQuestionIndex(parseInt(savedIndex, 10));\n    }\n  }, []);\n  useEffect(() => {\n    if (questions.length > 0) {\n      localStorage.setItem('currentQuestionIndex', currentQuestionIndex);\n    }\n  }, [currentQuestionIndex, questions.length]);\n  const handleNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n      setSelectedCorrectAnswer(null);\n      setPromptInputAnswers({});\n      setSelectedPrompts([]);\n      setReport('');\n    }\n  };\n  const handlePrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n      setSelectedCorrectAnswer(null);\n      setPromptInputAnswers({});\n      setSelectedPrompts([]);\n      setReport('');\n    }\n  };\n  const handleCorrectAnswerChange = option => {\n    setSelectedCorrectAnswer(option);\n  };\n  const handlePromptInputChange = option => {\n    setPromptInputAnswers({\n      ...promptInputAnswers,\n      [option]: !promptInputAnswers[option]\n    });\n  };\n  const handlePromptChange = (prompt, isChecked) => {\n    if (isChecked) {\n      setSelectedPrompts(prev => [...prev, prompt]);\n    } else {\n      setSelectedPrompts(prev => prev.filter(p => p !== prompt));\n    }\n  };\n  const handleGenerateReport = async () => {\n    const reportData = {\n      question: questions[currentQuestionIndex].question,\n      correctAnswer: selectedCorrectAnswer,\n      promptInputAnswers: Object.keys(promptInputAnswers).filter(answer => promptInputAnswers[answer]),\n      selectedPrompts: selectedPrompts\n    };\n    try {\n      const response = await fetch('http://localhost:3001/generate-report', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(reportData)\n      });\n      const data = await response.json();\n      setReport(data.report);\n    } catch (error) {\n      console.error('Error generating report:', error);\n    }\n  };\n  if (questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 12\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const prompts = ['can you design a solution in c# that will provide an output for the selected answer', 'write a program thats based on the choice', 'what is the gap between selected items', 'what is the consequence if I\\'m correct for the given choice based on the question', 'rephrase the question based on my choice', 'what is wrong with this implementation', 'what if my syntasx is correct'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#e8f5e8',\n        padding: '20px',\n        margin: '20px 0',\n        borderRadius: '8px',\n        border: '2px solid #27ae60',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#27ae60',\n          margin: '0 0 10px 0'\n        },\n        children: \"\\uD83C\\uDFAF NEW: CQRS Pattern Implementation Available!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 15px 0',\n          fontSize: '16px'\n        },\n        children: \"Learn Command Query Responsibility Segregation with step-by-step implementation and interactive prompts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/cqrs\",\n        style: {\n          display: 'inline-block',\n          padding: '10px 20px',\n          backgroundColor: '#27ae60',\n          color: 'white',\n          textDecoration: 'none',\n          borderRadius: '5px',\n          fontWeight: 'bold'\n        },\n        children: \"Explore CQRS Pattern \\u2192\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: currentQuestion.question\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-container\",\n        children: currentQuestion.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            name: `question-${currentQuestionIndex}`,\n            checked: selectedCorrectAnswer === option,\n            onChange: () => handleCorrectAnswerChange(option)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: option\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: !!promptInputAnswers[option],\n            onChange: () => handlePromptInputChange(option)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prompts-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Follow-up Prompts:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), prompts.map((prompt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prompt\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: prompt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: selectedPrompts.includes(prompt),\n            onChange: e => handlePromptChange(prompt, e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleGenerateReport,\n        children: \"Generate Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), report && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Consolidated Report:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          children: report\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navigation-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrevious,\n          disabled: currentQuestionIndex === 0,\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNext,\n          disabled: currentQuestionIndex === questions.length - 1,\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n}\n_s(MainPage, \"k+atQgsdkzxOFdYwHwohS7CeyyE=\");\n_c = MainPage;\nexport default MainPage;\nvar _c;\n$RefreshReg$(_c, \"MainPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "questionsData", "jsxDEV", "_jsxDEV", "MainPage", "_s", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedCorrectAnswer", "setSelectedCorrectAnswer", "promptInputAnswers", "setPromptInputAnswers", "selectedPrompts", "setSelectedPrompts", "report", "setReport", "allQuestions", "Object", "values", "flat", "savedIndex", "localStorage", "getItem", "parseInt", "length", "setItem", "handleNext", "handlePrevious", "handleCorrectAnswerChange", "option", "handlePromptInputChange", "handlePromptChange", "prompt", "isChecked", "prev", "filter", "p", "handleGenerateReport", "reportData", "question", "<PERSON><PERSON><PERSON><PERSON>", "keys", "answer", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentQuestion", "prompts", "className", "style", "backgroundColor", "padding", "margin", "borderRadius", "border", "textAlign", "color", "fontSize", "href", "display", "textDecoration", "fontWeight", "options", "map", "index", "type", "name", "checked", "onChange", "includes", "e", "target", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/MainPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\nimport questionsData from './mcqs.json';\r\n\r\nfunction MainPage() {\r\n  const [questions, setQuestions] = useState([]);\r\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\r\n  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState(null);\r\n  const [promptInputAnswers, setPromptInputAnswers] = useState({});\r\n  const [selectedPrompts, setSelectedPrompts] = useState([]);\r\n  const [report, setReport] = useState('');\r\n\r\n  useEffect(() => {\r\n    const allQuestions = Object.values(questionsData).flat();\r\n    setQuestions(allQuestions);\r\n\r\n    const savedIndex = localStorage.getItem('currentQuestionIndex');\r\n    if (savedIndex !== null) {\r\n      setCurrentQuestionIndex(parseInt(savedIndex, 10));\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (questions.length > 0) {\r\n      localStorage.setItem('currentQuestionIndex', currentQuestionIndex);\r\n    }\r\n  }, [currentQuestionIndex, questions.length]);\r\n\r\n  const handleNext = () => {\r\n    if (currentQuestionIndex < questions.length - 1) {\r\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\r\n      setSelectedCorrectAnswer(null);\r\n      setPromptInputAnswers({});\r\n      setSelectedPrompts([]);\r\n      setReport('');\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentQuestionIndex > 0) {\r\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\r\n      setSelectedCorrectAnswer(null);\r\n      setPromptInputAnswers({});\r\n      setSelectedPrompts([]);\r\n      setReport('');\r\n    }\r\n  };\r\n\r\n  const handleCorrectAnswerChange = (option) => {\r\n    setSelectedCorrectAnswer(option);\r\n  };\r\n\r\n  const handlePromptInputChange = (option) => {\r\n    setPromptInputAnswers({\r\n      ...promptInputAnswers,\r\n      [option]: !promptInputAnswers[option],\r\n    });\r\n  };\r\n\r\n  const handlePromptChange = (prompt, isChecked) => {\r\n    if (isChecked) {\r\n      setSelectedPrompts(prev => [...prev, prompt]);\r\n    } else {\r\n      setSelectedPrompts(prev => prev.filter(p => p !== prompt));\r\n    }\r\n  };\r\n\r\n  const handleGenerateReport = async () => {\r\n    const reportData = {\r\n      question: questions[currentQuestionIndex].question,\r\n      correctAnswer: selectedCorrectAnswer,\r\n      promptInputAnswers: Object.keys(promptInputAnswers).filter(\r\n        (answer) => promptInputAnswers[answer]\r\n      ),\r\n      selectedPrompts: selectedPrompts,\r\n    };\r\n\r\n    try {\r\n      const response = await fetch('http://localhost:3001/generate-report', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(reportData),\r\n      });\r\n      const data = await response.json();\r\n      setReport(data.report);\r\n    } catch (error) {\r\n      console.error('Error generating report:', error);\r\n    }\r\n  };\r\n\r\n  if (questions.length === 0) {\r\n    return <div>Loading...</div>;\r\n  }\r\n\r\n  const currentQuestion = questions[currentQuestionIndex];\r\n  const prompts = [\r\n    'can you design a solution in c# that will provide an output for the selected answer',\r\n    'write a program thats based on the choice',\r\n    'what is the gap between selected items',\r\n    'what is the consequence if I\\'m correct for the given choice based on the question',\r\n    'rephrase the question based on my choice',\r\n    'what is wrong with this implementation',\r\n    'what if my syntasx is correct',\r\n  ];\r\n\r\n  return (\r\n    <div className=\"App\">\r\n      {/* New CQRS Pattern Highlight */}\r\n      <div style={{\r\n        backgroundColor: '#e8f5e8',\r\n        padding: '20px',\r\n        margin: '20px 0',\r\n        borderRadius: '8px',\r\n        border: '2px solid #27ae60',\r\n        textAlign: 'center'\r\n      }}>\r\n        <h2 style={{ color: '#27ae60', margin: '0 0 10px 0' }}>\r\n          🎯 NEW: CQRS Pattern Implementation Available!\r\n        </h2>\r\n        <p style={{ margin: '0 0 15px 0', fontSize: '16px' }}>\r\n          Learn Command Query Responsibility Segregation with step-by-step implementation and interactive prompts\r\n        </p>\r\n        <a\r\n          href=\"/cqrs\"\r\n          style={{\r\n            display: 'inline-block',\r\n            padding: '10px 20px',\r\n            backgroundColor: '#27ae60',\r\n            color: 'white',\r\n            textDecoration: 'none',\r\n            borderRadius: '5px',\r\n            fontWeight: 'bold'\r\n          }}\r\n        >\r\n          Explore CQRS Pattern →\r\n        </a>\r\n      </div>\r\n\r\n      <div className=\"question-container\">\r\n        <h2>{currentQuestion.question}</h2>\r\n        <div className=\"options-container\">\r\n          {currentQuestion.options.map((option, index) => (\r\n            <div key={index} className=\"option\">\r\n              <input\r\n                type=\"radio\"\r\n                name={`question-${currentQuestionIndex}`}\r\n                checked={selectedCorrectAnswer === option}\r\n                onChange={() => handleCorrectAnswerChange(option)}\r\n              />\r\n              <label>{option}</label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={!!promptInputAnswers[option]}\r\n                onChange={() => handlePromptInputChange(option)}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"prompts-container\">\r\n          <h3>Follow-up Prompts:</h3>\r\n          {prompts.map((prompt, index) => (\r\n            <div key={index} className=\"prompt\">\r\n              <label>{prompt}</label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={selectedPrompts.includes(prompt)}\r\n                onChange={(e) => handlePromptChange(prompt, e.target.checked)}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <button onClick={handleGenerateReport}>Generate Report</button>\r\n        {report && (\r\n          <div className=\"report-container\">\r\n            <h3>Consolidated Report:</h3>\r\n            <pre>{report}</pre>\r\n          </div>\r\n        )}\r\n        <div className=\"navigation-buttons\">\r\n          <button onClick={handlePrevious} disabled={currentQuestionIndex === 0}>\r\n            Previous\r\n          </button>\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentQuestionIndex === questions.length - 1}\r\n          >\r\n            Next\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default MainPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAClB,OAAOC,aAAa,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACS,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACW,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACa,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,MAAMkB,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACnB,aAAa,CAAC,CAACoB,IAAI,CAAC,CAAC;IACxDd,YAAY,CAACW,YAAY,CAAC;IAE1B,MAAMI,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IAC/D,IAAIF,UAAU,KAAK,IAAI,EAAE;MACvBb,uBAAuB,CAACgB,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,EAAE,CAAC;EAENtB,SAAS,CAAC,MAAM;IACd,IAAIM,SAAS,CAACoB,MAAM,GAAG,CAAC,EAAE;MACxBH,YAAY,CAACI,OAAO,CAAC,sBAAsB,EAAEnB,oBAAoB,CAAC;IACpE;EACF,CAAC,EAAE,CAACA,oBAAoB,EAAEF,SAAS,CAACoB,MAAM,CAAC,CAAC;EAE5C,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIpB,oBAAoB,GAAGF,SAAS,CAACoB,MAAM,GAAG,CAAC,EAAE;MAC/CjB,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;MACjDG,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMY,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrB,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;MACjDG,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMa,yBAAyB,GAAIC,MAAM,IAAK;IAC5CpB,wBAAwB,CAACoB,MAAM,CAAC;EAClC,CAAC;EAED,MAAMC,uBAAuB,GAAID,MAAM,IAAK;IAC1ClB,qBAAqB,CAAC;MACpB,GAAGD,kBAAkB;MACrB,CAACmB,MAAM,GAAG,CAACnB,kBAAkB,CAACmB,MAAM;IACtC,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;IAChD,IAAIA,SAAS,EAAE;MACbpB,kBAAkB,CAACqB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,MAAM,CAAC,CAAC;IAC/C,CAAC,MAAM;MACLnB,kBAAkB,CAACqB,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,MAAM,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAMK,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMC,UAAU,GAAG;MACjBC,QAAQ,EAAEnC,SAAS,CAACE,oBAAoB,CAAC,CAACiC,QAAQ;MAClDC,aAAa,EAAEhC,qBAAqB;MACpCE,kBAAkB,EAAEO,MAAM,CAACwB,IAAI,CAAC/B,kBAAkB,CAAC,CAACyB,MAAM,CACvDO,MAAM,IAAKhC,kBAAkB,CAACgC,MAAM,CACvC,CAAC;MACD9B,eAAe,EAAEA;IACnB,CAAC;IAED,IAAI;MACF,MAAM+B,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACX,UAAU;MACjC,CAAC,CAAC;MACF,MAAMY,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClCpC,SAAS,CAACmC,IAAI,CAACpC,MAAM,CAAC;IACxB,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,IAAIhD,SAAS,CAACoB,MAAM,KAAK,CAAC,EAAE;IAC1B,oBAAOvB,OAAA;MAAAqD,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9B;EAEA,MAAMC,eAAe,GAAGvD,SAAS,CAACE,oBAAoB,CAAC;EACvD,MAAMsD,OAAO,GAAG,CACd,qFAAqF,EACrF,2CAA2C,EAC3C,wCAAwC,EACxC,oFAAoF,EACpF,0CAA0C,EAC1C,wCAAwC,EACxC,+BAA+B,CAChC;EAED,oBACE3D,OAAA;IAAK4D,SAAS,EAAC,KAAK;IAAAP,QAAA,gBAElBrD,OAAA;MAAK6D,KAAK,EAAE;QACVC,eAAe,EAAE,SAAS;QAC1BC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,QAAQ;QAChBC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE;MACb,CAAE;MAAAd,QAAA,gBACArD,OAAA;QAAI6D,KAAK,EAAE;UAAEO,KAAK,EAAE,SAAS;UAAEJ,MAAM,EAAE;QAAa,CAAE;QAAAX,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzD,OAAA;QAAG6D,KAAK,EAAE;UAAEG,MAAM,EAAE,YAAY;UAAEK,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJzD,OAAA;QACEsE,IAAI,EAAC,OAAO;QACZT,KAAK,EAAE;UACLU,OAAO,EAAE,cAAc;UACvBR,OAAO,EAAE,WAAW;UACpBD,eAAe,EAAE,SAAS;UAC1BM,KAAK,EAAE,OAAO;UACdI,cAAc,EAAE,MAAM;UACtBP,YAAY,EAAE,KAAK;UACnBQ,UAAU,EAAE;QACd,CAAE;QAAApB,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENzD,OAAA;MAAK4D,SAAS,EAAC,oBAAoB;MAAAP,QAAA,gBACjCrD,OAAA;QAAAqD,QAAA,EAAKK,eAAe,CAACpB;MAAQ;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnCzD,OAAA;QAAK4D,SAAS,EAAC,mBAAmB;QAAAP,QAAA,EAC/BK,eAAe,CAACgB,OAAO,CAACC,GAAG,CAAC,CAAC/C,MAAM,EAAEgD,KAAK,kBACzC5E,OAAA;UAAiB4D,SAAS,EAAC,QAAQ;UAAAP,QAAA,gBACjCrD,OAAA;YACE6E,IAAI,EAAC,OAAO;YACZC,IAAI,EAAE,YAAYzE,oBAAoB,EAAG;YACzC0E,OAAO,EAAExE,qBAAqB,KAAKqB,MAAO;YAC1CoD,QAAQ,EAAEA,CAAA,KAAMrD,yBAAyB,CAACC,MAAM;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACFzD,OAAA;YAAAqD,QAAA,EAAQzB;UAAM;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvBzD,OAAA;YACE6E,IAAI,EAAC,UAAU;YACfE,OAAO,EAAE,CAAC,CAACtE,kBAAkB,CAACmB,MAAM,CAAE;YACtCoD,QAAQ,EAAEA,CAAA,KAAMnD,uBAAuB,CAACD,MAAM;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA,GAZMmB,KAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNzD,OAAA;QAAK4D,SAAS,EAAC,mBAAmB;QAAAP,QAAA,gBAChCrD,OAAA;UAAAqD,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1BE,OAAO,CAACgB,GAAG,CAAC,CAAC5C,MAAM,EAAE6C,KAAK,kBACzB5E,OAAA;UAAiB4D,SAAS,EAAC,QAAQ;UAAAP,QAAA,gBACjCrD,OAAA;YAAAqD,QAAA,EAAQtB;UAAM;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvBzD,OAAA;YACE6E,IAAI,EAAC,UAAU;YACfE,OAAO,EAAEpE,eAAe,CAACsE,QAAQ,CAAClD,MAAM,CAAE;YAC1CiD,QAAQ,EAAGE,CAAC,IAAKpD,kBAAkB,CAACC,MAAM,EAAEmD,CAAC,CAACC,MAAM,CAACJ,OAAO;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA,GANMmB,KAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNzD,OAAA;QAAQoF,OAAO,EAAEhD,oBAAqB;QAAAiB,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC9D5C,MAAM,iBACLb,OAAA;QAAK4D,SAAS,EAAC,kBAAkB;QAAAP,QAAA,gBAC/BrD,OAAA;UAAAqD,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BzD,OAAA;UAAAqD,QAAA,EAAMxC;QAAM;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACN,eACDzD,OAAA;QAAK4D,SAAS,EAAC,oBAAoB;QAAAP,QAAA,gBACjCrD,OAAA;UAAQoF,OAAO,EAAE1D,cAAe;UAAC2D,QAAQ,EAAEhF,oBAAoB,KAAK,CAAE;UAAAgD,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzD,OAAA;UACEoF,OAAO,EAAE3D,UAAW;UACpB4D,QAAQ,EAAEhF,oBAAoB,KAAKF,SAAS,CAACoB,MAAM,GAAG,CAAE;UAAA8B,QAAA,EACzD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvD,EAAA,CA9LQD,QAAQ;AAAAqF,EAAA,GAARrF,QAAQ;AAgMjB,eAAeA,QAAQ;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}