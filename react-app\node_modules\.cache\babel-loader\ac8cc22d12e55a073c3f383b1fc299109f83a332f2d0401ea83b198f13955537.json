{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\MainPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport questionsData from './mcqs.json';\nimport PatternsSummary from './PatternsSummary';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction MainPage() {\n  _s();\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState(null);\n  const [promptInputAnswers, setPromptInputAnswers] = useState({});\n  const [selectedPrompts, setSelectedPrompts] = useState([]);\n  const [report, setReport] = useState('');\n  const [selectedPatterns, setSelectedPatterns] = useState(new Set());\n  const [filteredQuestions, setFilteredQuestions] = useState([]);\n  const [showPatternSelector, setShowPatternSelector] = useState(false);\n\n  // Available patterns for selection\n  const availablePatterns = [\"1. Two Pointers\", \"2. Merge Intervals\", \"3. Sliding Window\", \"4. Binary Search\", \"5. Tree Traversal\", \"6. Dynamic Programming (1D)\", \"7. Fast & Slow Pointers\", \"8. Graph Algorithms\", \"Island Pattern\", \"Tree BFS\", \"Tree DFS\", \"Heap\", \"Modified Binary Search\", \"Subsets\", \"Greedy Algorithm\", \"0/1 Knapsack\"];\n  useEffect(() => {\n    const allQuestions = Object.values(questionsData).flat();\n    setQuestions(allQuestions);\n    setFilteredQuestions(allQuestions);\n    const savedIndex = localStorage.getItem('currentQuestionIndex');\n    if (savedIndex !== null) {\n      setCurrentQuestionIndex(parseInt(savedIndex, 10));\n    }\n  }, []);\n\n  // Filter questions based on selected patterns\n  useEffect(() => {\n    if (selectedPatterns.size === 0) {\n      const allQuestions = Object.values(questionsData).flat();\n      setFilteredQuestions(allQuestions);\n    } else {\n      const filtered = [];\n      selectedPatterns.forEach(pattern => {\n        if (questionsData[pattern]) {\n          filtered.push(...questionsData[pattern]);\n        }\n      });\n      setFilteredQuestions(filtered);\n      setCurrentQuestionIndex(0); // Reset to first question when filtering\n    }\n  }, [selectedPatterns]);\n  useEffect(() => {\n    if (filteredQuestions.length > 0) {\n      localStorage.setItem('currentQuestionIndex', currentQuestionIndex);\n    }\n  }, [currentQuestionIndex, filteredQuestions.length]);\n\n  // Pattern selection handlers\n  const handlePatternToggle = pattern => {\n    const newSelected = new Set(selectedPatterns);\n    if (newSelected.has(pattern)) {\n      newSelected.delete(pattern);\n    } else {\n      newSelected.add(pattern);\n    }\n    setSelectedPatterns(newSelected);\n  };\n  const selectAllPatterns = () => {\n    setSelectedPatterns(new Set(availablePatterns));\n  };\n  const clearAllPatterns = () => {\n    setSelectedPatterns(new Set());\n  };\n  const handleNext = () => {\n    if (currentQuestionIndex < filteredQuestions.length - 1) {\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\n      setSelectedCorrectAnswer(null);\n      setPromptInputAnswers({});\n      setSelectedPrompts([]);\n      setReport('');\n    }\n  };\n  const handlePrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\n      setSelectedCorrectAnswer(null);\n      setPromptInputAnswers({});\n      setSelectedPrompts([]);\n      setReport('');\n    }\n  };\n  const handleCorrectAnswerChange = option => {\n    setSelectedCorrectAnswer(option);\n  };\n  const handlePromptInputChange = option => {\n    setPromptInputAnswers({\n      ...promptInputAnswers,\n      [option]: !promptInputAnswers[option]\n    });\n  };\n  const handlePromptChange = (prompt, isChecked) => {\n    if (isChecked) {\n      setSelectedPrompts(prev => [...prev, prompt]);\n    } else {\n      setSelectedPrompts(prev => prev.filter(p => p !== prompt));\n    }\n  };\n  const handleGenerateReport = () => {\n    var _filteredQuestions$cu, _filteredQuestions$cu2, _filteredQuestions$cu3;\n    const selectedPatternsArray = Array.from(selectedPatterns);\n    const totalQuestions = filteredQuestions.length;\n    const currentProgress = currentQuestionIndex + 1;\n    const reportContent = `\n## Coding Patterns Learning Report\n\n**Study Session:** ${new Date().toLocaleString()}\n\n### Pattern Selection\n**Selected Patterns:** ${selectedPatternsArray.length > 0 ? selectedPatternsArray.join(', ') : 'All Patterns'}\n**Total Questions:** ${totalQuestions}\n**Current Progress:** ${currentProgress}/${totalQuestions} (${Math.round(currentProgress / totalQuestions * 100)}%)\n\n### Learning Prompts Completed\n**Selected Learning Prompts:** ${selectedPrompts.join(', ') || 'None selected yet'}\n\n### Detailed Responses\n${Object.entries(promptInputAnswers).map(([prompt, answer]) => `\n**Prompt:** ${prompt}\n**Your Response:** ${answer}\n`).join('\\n')}\n\n### Current Question Analysis\n${filteredQuestions.length > 0 ? `\n**Current Question:** ${((_filteredQuestions$cu = filteredQuestions[currentQuestionIndex]) === null || _filteredQuestions$cu === void 0 ? void 0 : _filteredQuestions$cu.question) || 'N/A'}\n**Your Selected Answer:** ${selectedCorrectAnswer || 'Not answered yet'}\n**Correct Answer:** ${((_filteredQuestions$cu2 = filteredQuestions[currentQuestionIndex]) === null || _filteredQuestions$cu2 === void 0 ? void 0 : _filteredQuestions$cu2.answer) || 'N/A'}\n**Status:** ${selectedCorrectAnswer === ((_filteredQuestions$cu3 = filteredQuestions[currentQuestionIndex]) === null || _filteredQuestions$cu3 === void 0 ? void 0 : _filteredQuestions$cu3.answer) ? '✅ Correct' : '❌ Incorrect or Not Answered'}\n` : 'No questions available for selected patterns'}\n\n### Study Recommendations\n${selectedPatternsArray.length === 0 ? '- Select specific patterns to focus your study session\\n- Use the pattern checkboxes above to filter questions' : `- Continue practicing ${selectedPatternsArray.join(', ')} patterns\\n- Review incorrect answers and understand the concepts\\n- Try implementing the patterns in code`}\n\n### Next Steps\n- Complete remaining questions in selected patterns\n- Review learning prompts and provide detailed responses\n- Practice implementing these patterns in your preferred programming language\n- Visit the Coding Patterns Guide for interactive implementations\n\n---\n*Generated by Coding Patterns Learning System*\n    `.trim();\n    setReport(reportContent);\n  };\n  if (filteredQuestions.length === 0 && selectedPatterns.size > 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"No questions found for selected patterns\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Please select different patterns or clear the selection to see all questions.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: clearAllPatterns,\n        children: \"Show All Questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this);\n  }\n  if (filteredQuestions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 12\n    }, this);\n  }\n  const currentQuestion = filteredQuestions[currentQuestionIndex];\n  const prompts = ['can you design a solution in c# that will provide an output for the selected answer', 'write a program thats based on the choice', 'what is the gap between selected items', 'what is the consequence if I\\'m correct for the given choice based on the question', 'rephrase the question based on my choice', 'what is wrong with this implementation', 'what if my syntasx is correct'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#e8f5e8',\n        padding: '20px',\n        margin: '20px 0',\n        borderRadius: '8px',\n        border: '2px solid #27ae60',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#27ae60',\n          margin: '0 0 10px 0'\n        },\n        children: \"\\uD83C\\uDFAF NEW: Complete Coding Patterns Guide Available!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          margin: '0 0 15px 0',\n          fontSize: '16px'\n        },\n        children: \"Master all 16 essential coding patterns for technical interviews with step-by-step implementations and interactive demos\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '15px',\n          justifyContent: 'center',\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/patterns\",\n          style: {\n            display: 'inline-block',\n            padding: '10px 20px',\n            backgroundColor: '#27ae60',\n            color: 'white',\n            textDecoration: 'none',\n            borderRadius: '5px',\n            fontWeight: 'bold'\n          },\n          children: \"\\uD83C\\uDFAF Coding Patterns Guide \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cqrs\",\n          style: {\n            display: 'inline-block',\n            padding: '10px 20px',\n            backgroundColor: '#3498db',\n            color: 'white',\n            textDecoration: 'none',\n            borderRadius: '5px',\n            fontWeight: 'bold'\n          },\n          children: \"\\uD83C\\uDFD7\\uFE0F CQRS Pattern \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PatternsSummary, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        padding: '20px',\n        margin: '20px 0',\n        borderRadius: '8px',\n        border: '1px solid #dee2e6'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: 0,\n            color: '#495057'\n          },\n          children: \"\\uD83C\\uDFAF Select Coding Patterns to Study\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowPatternSelector(!showPatternSelector),\n          style: {\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            padding: '8px 16px',\n            borderRadius: '4px',\n            cursor: 'pointer'\n          },\n          children: showPatternSelector ? 'Hide Patterns' : 'Show Patterns'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), showPatternSelector && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: selectAllPatterns,\n            style: {\n              backgroundColor: '#28a745',\n              color: 'white',\n              border: 'none',\n              padding: '6px 12px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              marginRight: '10px'\n            },\n            children: \"Select All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: clearAllPatterns,\n            style: {\n              backgroundColor: '#dc3545',\n              color: 'white',\n              border: 'none',\n              padding: '6px 12px',\n              borderRadius: '4px',\n              cursor: 'pointer'\n            },\n            children: \"Clear All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n            gap: '10px',\n            marginBottom: '15px'\n          },\n          children: availablePatterns.map(pattern => /*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              padding: '8px',\n              backgroundColor: selectedPatterns.has(pattern) ? '#e7f3ff' : 'white',\n              border: '1px solid #ddd',\n              borderRadius: '4px',\n              cursor: 'pointer'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: selectedPatterns.has(pattern),\n              onChange: () => handlePatternToggle(pattern),\n              style: {\n                marginRight: '8px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '14px'\n              },\n              children: pattern\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 19\n            }, this)]\n          }, pattern, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '10px',\n            backgroundColor: '#e9ecef',\n            borderRadius: '4px',\n            fontSize: '14px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Selected:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), \" \", selectedPatterns.size, \" patterns |\", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \" Questions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), \" \", filteredQuestions.length, \" |\", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \" Progress:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), \" \", currentQuestionIndex + 1, \"/\", filteredQuestions.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"question-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: currentQuestion.question\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-container\",\n        children: currentQuestion.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"option\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"radio\",\n            name: `question-${currentQuestionIndex}`,\n            checked: selectedCorrectAnswer === option,\n            onChange: () => handleCorrectAnswerChange(option)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            children: option\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: !!promptInputAnswers[option],\n            onChange: () => handlePromptInputChange(option)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"prompts-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Follow-up Prompts:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), prompts.map((prompt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prompt\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: prompt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: selectedPrompts.includes(prompt),\n            onChange: e => handlePromptChange(prompt, e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleGenerateReport,\n        children: \"Generate Report\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), report && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Consolidated Report:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          children: report\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navigation-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePrevious,\n          disabled: currentQuestionIndex === 0,\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleNext,\n          disabled: currentQuestionIndex === filteredQuestions.length - 1,\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n}\n_s(MainPage, \"NX1kOy++GdSEZVjJQhPj5jxic2o=\");\n_c = MainPage;\nexport default MainPage;\nvar _c;\n$RefreshReg$(_c, \"MainPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "questionsData", "Pat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "MainPage", "_s", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "selectedCorrectAnswer", "setSelectedCorrectAnswer", "promptInputAnswers", "setPromptInputAnswers", "selectedPrompts", "setSelectedPrompts", "report", "setReport", "selectedPatterns", "setSelectedPatterns", "Set", "filteredQuestions", "setFilteredQuestions", "showPatternSelector", "setShowPatternSelector", "availablePatterns", "allQuestions", "Object", "values", "flat", "savedIndex", "localStorage", "getItem", "parseInt", "size", "filtered", "for<PERSON>ach", "pattern", "push", "length", "setItem", "handlePatternToggle", "newSelected", "has", "delete", "add", "selectAllPatterns", "clearAllPatterns", "handleNext", "handlePrevious", "handleCorrectAnswerChange", "option", "handlePromptInputChange", "handlePromptChange", "prompt", "isChecked", "prev", "filter", "p", "handleGenerateReport", "_filteredQuestions$cu", "_filteredQuestions$cu2", "_filteredQuestions$cu3", "selectedPatternsArray", "Array", "from", "totalQuestions", "currentProgress", "reportContent", "Date", "toLocaleString", "join", "Math", "round", "entries", "map", "answer", "question", "trim", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "prompts", "style", "backgroundColor", "padding", "margin", "borderRadius", "border", "textAlign", "color", "fontSize", "display", "gap", "justifyContent", "flexWrap", "href", "textDecoration", "fontWeight", "alignItems", "marginBottom", "cursor", "marginRight", "gridTemplateColumns", "type", "checked", "onChange", "options", "index", "name", "includes", "e", "target", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/MainPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './App.css';\r\nimport questionsData from './mcqs.json';\r\nimport PatternsSummary from './PatternsSummary';\r\n\r\nfunction MainPage() {\r\n  const [questions, setQuestions] = useState([]);\r\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\r\n  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState(null);\r\n  const [promptInputAnswers, setPromptInputAnswers] = useState({});\r\n  const [selectedPrompts, setSelectedPrompts] = useState([]);\r\n  const [report, setReport] = useState('');\r\n  const [selectedPatterns, setSelectedPatterns] = useState(new Set());\r\n  const [filteredQuestions, setFilteredQuestions] = useState([]);\r\n  const [showPatternSelector, setShowPatternSelector] = useState(false);\r\n\r\n  // Available patterns for selection\r\n  const availablePatterns = [\r\n    \"1. Two Pointers\",\r\n    \"2. Merge Intervals\",\r\n    \"3. Sliding Window\",\r\n    \"4. Binary Search\",\r\n    \"5. Tree Traversal\",\r\n    \"6. Dynamic Programming (1D)\",\r\n    \"7. Fast & Slow Pointers\",\r\n    \"8. Graph Algorithms\",\r\n    \"Island Pattern\",\r\n    \"Tree BFS\",\r\n    \"Tree DFS\",\r\n    \"Heap\",\r\n    \"Modified Binary Search\",\r\n    \"Subsets\",\r\n    \"Greedy Algorithm\",\r\n    \"0/1 Knapsack\"\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const allQuestions = Object.values(questionsData).flat();\r\n    setQuestions(allQuestions);\r\n    setFilteredQuestions(allQuestions);\r\n\r\n    const savedIndex = localStorage.getItem('currentQuestionIndex');\r\n    if (savedIndex !== null) {\r\n      setCurrentQuestionIndex(parseInt(savedIndex, 10));\r\n    }\r\n  }, []);\r\n\r\n  // Filter questions based on selected patterns\r\n  useEffect(() => {\r\n    if (selectedPatterns.size === 0) {\r\n      const allQuestions = Object.values(questionsData).flat();\r\n      setFilteredQuestions(allQuestions);\r\n    } else {\r\n      const filtered = [];\r\n      selectedPatterns.forEach(pattern => {\r\n        if (questionsData[pattern]) {\r\n          filtered.push(...questionsData[pattern]);\r\n        }\r\n      });\r\n      setFilteredQuestions(filtered);\r\n      setCurrentQuestionIndex(0); // Reset to first question when filtering\r\n    }\r\n  }, [selectedPatterns]);\r\n\r\n  useEffect(() => {\r\n    if (filteredQuestions.length > 0) {\r\n      localStorage.setItem('currentQuestionIndex', currentQuestionIndex);\r\n    }\r\n  }, [currentQuestionIndex, filteredQuestions.length]);\r\n\r\n  // Pattern selection handlers\r\n  const handlePatternToggle = (pattern) => {\r\n    const newSelected = new Set(selectedPatterns);\r\n    if (newSelected.has(pattern)) {\r\n      newSelected.delete(pattern);\r\n    } else {\r\n      newSelected.add(pattern);\r\n    }\r\n    setSelectedPatterns(newSelected);\r\n  };\r\n\r\n  const selectAllPatterns = () => {\r\n    setSelectedPatterns(new Set(availablePatterns));\r\n  };\r\n\r\n  const clearAllPatterns = () => {\r\n    setSelectedPatterns(new Set());\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentQuestionIndex < filteredQuestions.length - 1) {\r\n      setCurrentQuestionIndex(currentQuestionIndex + 1);\r\n      setSelectedCorrectAnswer(null);\r\n      setPromptInputAnswers({});\r\n      setSelectedPrompts([]);\r\n      setReport('');\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentQuestionIndex > 0) {\r\n      setCurrentQuestionIndex(currentQuestionIndex - 1);\r\n      setSelectedCorrectAnswer(null);\r\n      setPromptInputAnswers({});\r\n      setSelectedPrompts([]);\r\n      setReport('');\r\n    }\r\n  };\r\n\r\n  const handleCorrectAnswerChange = (option) => {\r\n    setSelectedCorrectAnswer(option);\r\n  };\r\n\r\n  const handlePromptInputChange = (option) => {\r\n    setPromptInputAnswers({\r\n      ...promptInputAnswers,\r\n      [option]: !promptInputAnswers[option],\r\n    });\r\n  };\r\n\r\n  const handlePromptChange = (prompt, isChecked) => {\r\n    if (isChecked) {\r\n      setSelectedPrompts(prev => [...prev, prompt]);\r\n    } else {\r\n      setSelectedPrompts(prev => prev.filter(p => p !== prompt));\r\n    }\r\n  };\r\n\r\n  const handleGenerateReport = () => {\r\n    const selectedPatternsArray = Array.from(selectedPatterns);\r\n    const totalQuestions = filteredQuestions.length;\r\n    const currentProgress = currentQuestionIndex + 1;\r\n\r\n    const reportContent = `\r\n## Coding Patterns Learning Report\r\n\r\n**Study Session:** ${new Date().toLocaleString()}\r\n\r\n### Pattern Selection\r\n**Selected Patterns:** ${selectedPatternsArray.length > 0 ? selectedPatternsArray.join(', ') : 'All Patterns'}\r\n**Total Questions:** ${totalQuestions}\r\n**Current Progress:** ${currentProgress}/${totalQuestions} (${Math.round((currentProgress/totalQuestions) * 100)}%)\r\n\r\n### Learning Prompts Completed\r\n**Selected Learning Prompts:** ${selectedPrompts.join(', ') || 'None selected yet'}\r\n\r\n### Detailed Responses\r\n${Object.entries(promptInputAnswers).map(([prompt, answer]) => `\r\n**Prompt:** ${prompt}\r\n**Your Response:** ${answer}\r\n`).join('\\n')}\r\n\r\n### Current Question Analysis\r\n${filteredQuestions.length > 0 ? `\r\n**Current Question:** ${filteredQuestions[currentQuestionIndex]?.question || 'N/A'}\r\n**Your Selected Answer:** ${selectedCorrectAnswer || 'Not answered yet'}\r\n**Correct Answer:** ${filteredQuestions[currentQuestionIndex]?.answer || 'N/A'}\r\n**Status:** ${selectedCorrectAnswer === filteredQuestions[currentQuestionIndex]?.answer ? '✅ Correct' : '❌ Incorrect or Not Answered'}\r\n` : 'No questions available for selected patterns'}\r\n\r\n### Study Recommendations\r\n${selectedPatternsArray.length === 0 ?\r\n  '- Select specific patterns to focus your study session\\n- Use the pattern checkboxes above to filter questions' :\r\n  `- Continue practicing ${selectedPatternsArray.join(', ')} patterns\\n- Review incorrect answers and understand the concepts\\n- Try implementing the patterns in code`\r\n}\r\n\r\n### Next Steps\r\n- Complete remaining questions in selected patterns\r\n- Review learning prompts and provide detailed responses\r\n- Practice implementing these patterns in your preferred programming language\r\n- Visit the Coding Patterns Guide for interactive implementations\r\n\r\n---\r\n*Generated by Coding Patterns Learning System*\r\n    `.trim();\r\n\r\n    setReport(reportContent);\r\n  };\r\n\r\n  if (filteredQuestions.length === 0 && selectedPatterns.size > 0) {\r\n    return (\r\n      <div className=\"page-content\">\r\n        <h2>No questions found for selected patterns</h2>\r\n        <p>Please select different patterns or clear the selection to see all questions.</p>\r\n        <button onClick={clearAllPatterns}>Show All Questions</button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (filteredQuestions.length === 0) {\r\n    return <div>Loading...</div>;\r\n  }\r\n\r\n  const currentQuestion = filteredQuestions[currentQuestionIndex];\r\n  const prompts = [\r\n    'can you design a solution in c# that will provide an output for the selected answer',\r\n    'write a program thats based on the choice',\r\n    'what is the gap between selected items',\r\n    'what is the consequence if I\\'m correct for the given choice based on the question',\r\n    'rephrase the question based on my choice',\r\n    'what is wrong with this implementation',\r\n    'what if my syntasx is correct',\r\n  ];\r\n\r\n  return (\r\n    <div className=\"page-content\">\r\n      {/* New Features Highlight */}\r\n      <div style={{\r\n        backgroundColor: '#e8f5e8',\r\n        padding: '20px',\r\n        margin: '20px 0',\r\n        borderRadius: '8px',\r\n        border: '2px solid #27ae60',\r\n        textAlign: 'center'\r\n      }}>\r\n        <h2 style={{ color: '#27ae60', margin: '0 0 10px 0' }}>\r\n          🎯 NEW: Complete Coding Patterns Guide Available!\r\n        </h2>\r\n        <p style={{ margin: '0 0 15px 0', fontSize: '16px' }}>\r\n          Master all 16 essential coding patterns for technical interviews with step-by-step implementations and interactive demos\r\n        </p>\r\n        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>\r\n          <a\r\n            href=\"/patterns\"\r\n            style={{\r\n              display: 'inline-block',\r\n              padding: '10px 20px',\r\n              backgroundColor: '#27ae60',\r\n              color: 'white',\r\n              textDecoration: 'none',\r\n              borderRadius: '5px',\r\n              fontWeight: 'bold'\r\n            }}\r\n          >\r\n            🎯 Coding Patterns Guide →\r\n          </a>\r\n          <a\r\n            href=\"/cqrs\"\r\n            style={{\r\n              display: 'inline-block',\r\n              padding: '10px 20px',\r\n              backgroundColor: '#3498db',\r\n              color: 'white',\r\n              textDecoration: 'none',\r\n              borderRadius: '5px',\r\n              fontWeight: 'bold'\r\n            }}\r\n          >\r\n            🏗️ CQRS Pattern →\r\n          </a>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Patterns Summary Section */}\r\n      <PatternsSummary />\r\n\r\n      {/* Pattern Selection Section */}\r\n      <div style={{\r\n        backgroundColor: '#f8f9fa',\r\n        padding: '20px',\r\n        margin: '20px 0',\r\n        borderRadius: '8px',\r\n        border: '1px solid #dee2e6'\r\n      }}>\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>\r\n          <h3 style={{ margin: 0, color: '#495057' }}>🎯 Select Coding Patterns to Study</h3>\r\n          <button\r\n            onClick={() => setShowPatternSelector(!showPatternSelector)}\r\n            style={{\r\n              backgroundColor: '#007bff',\r\n              color: 'white',\r\n              border: 'none',\r\n              padding: '8px 16px',\r\n              borderRadius: '4px',\r\n              cursor: 'pointer'\r\n            }}\r\n          >\r\n            {showPatternSelector ? 'Hide Patterns' : 'Show Patterns'}\r\n          </button>\r\n        </div>\r\n\r\n        {showPatternSelector && (\r\n          <div>\r\n            <div style={{ marginBottom: '15px' }}>\r\n              <button\r\n                onClick={selectAllPatterns}\r\n                style={{\r\n                  backgroundColor: '#28a745',\r\n                  color: 'white',\r\n                  border: 'none',\r\n                  padding: '6px 12px',\r\n                  borderRadius: '4px',\r\n                  cursor: 'pointer',\r\n                  marginRight: '10px'\r\n                }}\r\n              >\r\n                Select All\r\n              </button>\r\n              <button\r\n                onClick={clearAllPatterns}\r\n                style={{\r\n                  backgroundColor: '#dc3545',\r\n                  color: 'white',\r\n                  border: 'none',\r\n                  padding: '6px 12px',\r\n                  borderRadius: '4px',\r\n                  cursor: 'pointer'\r\n                }}\r\n              >\r\n                Clear All\r\n              </button>\r\n            </div>\r\n\r\n            <div style={{\r\n              display: 'grid',\r\n              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\r\n              gap: '10px',\r\n              marginBottom: '15px'\r\n            }}>\r\n              {availablePatterns.map(pattern => (\r\n                <label key={pattern} style={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  padding: '8px',\r\n                  backgroundColor: selectedPatterns.has(pattern) ? '#e7f3ff' : 'white',\r\n                  border: '1px solid #ddd',\r\n                  borderRadius: '4px',\r\n                  cursor: 'pointer'\r\n                }}>\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={selectedPatterns.has(pattern)}\r\n                    onChange={() => handlePatternToggle(pattern)}\r\n                    style={{ marginRight: '8px' }}\r\n                  />\r\n                  <span style={{ fontSize: '14px' }}>{pattern}</span>\r\n                </label>\r\n              ))}\r\n            </div>\r\n\r\n            <div style={{\r\n              padding: '10px',\r\n              backgroundColor: '#e9ecef',\r\n              borderRadius: '4px',\r\n              fontSize: '14px'\r\n            }}>\r\n              <strong>Selected:</strong> {selectedPatterns.size} patterns |\r\n              <strong> Questions:</strong> {filteredQuestions.length} |\r\n              <strong> Progress:</strong> {currentQuestionIndex + 1}/{filteredQuestions.length}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"question-container\">\r\n        <h2>{currentQuestion.question}</h2>\r\n        <div className=\"options-container\">\r\n          {currentQuestion.options.map((option, index) => (\r\n            <div key={index} className=\"option\">\r\n              <input\r\n                type=\"radio\"\r\n                name={`question-${currentQuestionIndex}`}\r\n                checked={selectedCorrectAnswer === option}\r\n                onChange={() => handleCorrectAnswerChange(option)}\r\n              />\r\n              <label>{option}</label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={!!promptInputAnswers[option]}\r\n                onChange={() => handlePromptInputChange(option)}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div className=\"prompts-container\">\r\n          <h3>Follow-up Prompts:</h3>\r\n          {prompts.map((prompt, index) => (\r\n            <div key={index} className=\"prompt\">\r\n              <label>{prompt}</label>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={selectedPrompts.includes(prompt)}\r\n                onChange={(e) => handlePromptChange(prompt, e.target.checked)}\r\n              />\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <button onClick={handleGenerateReport}>Generate Report</button>\r\n        {report && (\r\n          <div className=\"report-container\">\r\n            <h3>Consolidated Report:</h3>\r\n            <pre>{report}</pre>\r\n          </div>\r\n        )}\r\n        <div className=\"navigation-buttons\">\r\n          <button onClick={handlePrevious} disabled={currentQuestionIndex === 0}>\r\n            Previous\r\n          </button>\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentQuestionIndex === filteredQuestions.length - 1}\r\n          >\r\n            Next\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default MainPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAClB,OAAOC,aAAa,MAAM,aAAa;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACY,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACc,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,IAAIsB,GAAG,CAAC,CAAC,CAAC;EACnE,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACyB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM2B,iBAAiB,GAAG,CACxB,iBAAiB,EACjB,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,mBAAmB,EACnB,6BAA6B,EAC7B,yBAAyB,EACzB,qBAAqB,EACrB,gBAAgB,EAChB,UAAU,EACV,UAAU,EACV,MAAM,EACN,wBAAwB,EACxB,SAAS,EACT,kBAAkB,EAClB,cAAc,CACf;EAED1B,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC5B,aAAa,CAAC,CAAC6B,IAAI,CAAC,CAAC;IACxDtB,YAAY,CAACmB,YAAY,CAAC;IAC1BJ,oBAAoB,CAACI,YAAY,CAAC;IAElC,MAAMI,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;IAC/D,IAAIF,UAAU,KAAK,IAAI,EAAE;MACvBrB,uBAAuB,CAACwB,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC,CAAC;IACnD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/B,SAAS,CAAC,MAAM;IACd,IAAImB,gBAAgB,CAACgB,IAAI,KAAK,CAAC,EAAE;MAC/B,MAAMR,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC5B,aAAa,CAAC,CAAC6B,IAAI,CAAC,CAAC;MACxDP,oBAAoB,CAACI,YAAY,CAAC;IACpC,CAAC,MAAM;MACL,MAAMS,QAAQ,GAAG,EAAE;MACnBjB,gBAAgB,CAACkB,OAAO,CAACC,OAAO,IAAI;QAClC,IAAIrC,aAAa,CAACqC,OAAO,CAAC,EAAE;UAC1BF,QAAQ,CAACG,IAAI,CAAC,GAAGtC,aAAa,CAACqC,OAAO,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC;MACFf,oBAAoB,CAACa,QAAQ,CAAC;MAC9B1B,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAACS,gBAAgB,CAAC,CAAC;EAEtBnB,SAAS,CAAC,MAAM;IACd,IAAIsB,iBAAiB,CAACkB,MAAM,GAAG,CAAC,EAAE;MAChCR,YAAY,CAACS,OAAO,CAAC,sBAAsB,EAAEhC,oBAAoB,CAAC;IACpE;EACF,CAAC,EAAE,CAACA,oBAAoB,EAAEa,iBAAiB,CAACkB,MAAM,CAAC,CAAC;;EAEpD;EACA,MAAME,mBAAmB,GAAIJ,OAAO,IAAK;IACvC,MAAMK,WAAW,GAAG,IAAItB,GAAG,CAACF,gBAAgB,CAAC;IAC7C,IAAIwB,WAAW,CAACC,GAAG,CAACN,OAAO,CAAC,EAAE;MAC5BK,WAAW,CAACE,MAAM,CAACP,OAAO,CAAC;IAC7B,CAAC,MAAM;MACLK,WAAW,CAACG,GAAG,CAACR,OAAO,CAAC;IAC1B;IACAlB,mBAAmB,CAACuB,WAAW,CAAC;EAClC,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3B,mBAAmB,CAAC,IAAIC,GAAG,CAACK,iBAAiB,CAAC,CAAC;EACjD,CAAC;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5B,mBAAmB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAM4B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIxC,oBAAoB,GAAGa,iBAAiB,CAACkB,MAAM,GAAG,CAAC,EAAE;MACvD9B,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;MACjDG,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMgC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIzC,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACD,oBAAoB,GAAG,CAAC,CAAC;MACjDG,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzBE,kBAAkB,CAAC,EAAE,CAAC;MACtBE,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;EAED,MAAMiC,yBAAyB,GAAIC,MAAM,IAAK;IAC5CxC,wBAAwB,CAACwC,MAAM,CAAC;EAClC,CAAC;EAED,MAAMC,uBAAuB,GAAID,MAAM,IAAK;IAC1CtC,qBAAqB,CAAC;MACpB,GAAGD,kBAAkB;MACrB,CAACuC,MAAM,GAAG,CAACvC,kBAAkB,CAACuC,MAAM;IACtC,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;IAChD,IAAIA,SAAS,EAAE;MACbxC,kBAAkB,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,MAAM,CAAC,CAAC;IAC/C,CAAC,MAAM;MACLvC,kBAAkB,CAACyC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKJ,MAAM,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACjC,MAAMC,qBAAqB,GAAGC,KAAK,CAACC,IAAI,CAAC/C,gBAAgB,CAAC;IAC1D,MAAMgD,cAAc,GAAG7C,iBAAiB,CAACkB,MAAM;IAC/C,MAAM4B,eAAe,GAAG3D,oBAAoB,GAAG,CAAC;IAEhD,MAAM4D,aAAa,GAAG;AAC1B;AACA;AACA,qBAAqB,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;AAChD;AACA;AACA,yBAAyBP,qBAAqB,CAACxB,MAAM,GAAG,CAAC,GAAGwB,qBAAqB,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAG,cAAc;AAC7G,uBAAuBL,cAAc;AACrC,wBAAwBC,eAAe,IAAID,cAAc,KAAKM,IAAI,CAACC,KAAK,CAAEN,eAAe,GAACD,cAAc,GAAI,GAAG,CAAC;AAChH;AACA;AACA,iCAAiCpD,eAAe,CAACyD,IAAI,CAAC,IAAI,CAAC,IAAI,mBAAmB;AAClF;AACA;AACA,EAAE5C,MAAM,CAAC+C,OAAO,CAAC9D,kBAAkB,CAAC,CAAC+D,GAAG,CAAC,CAAC,CAACrB,MAAM,EAAEsB,MAAM,CAAC,KAAK;AAC/D,cAActB,MAAM;AACpB,qBAAqBsB,MAAM;AAC3B,CAAC,CAAC,CAACL,IAAI,CAAC,IAAI,CAAC;AACb;AACA;AACA,EAAElD,iBAAiB,CAACkB,MAAM,GAAG,CAAC,GAAG;AACjC,wBAAwB,EAAAqB,qBAAA,GAAAvC,iBAAiB,CAACb,oBAAoB,CAAC,cAAAoD,qBAAA,uBAAvCA,qBAAA,CAAyCiB,QAAQ,KAAI,KAAK;AAClF,4BAA4BnE,qBAAqB,IAAI,kBAAkB;AACvE,sBAAsB,EAAAmD,sBAAA,GAAAxC,iBAAiB,CAACb,oBAAoB,CAAC,cAAAqD,sBAAA,uBAAvCA,sBAAA,CAAyCe,MAAM,KAAI,KAAK;AAC9E,cAAclE,qBAAqB,OAAAoD,sBAAA,GAAKzC,iBAAiB,CAACb,oBAAoB,CAAC,cAAAsD,sBAAA,uBAAvCA,sBAAA,CAAyCc,MAAM,IAAG,WAAW,GAAG,6BAA6B;AACrI,CAAC,GAAG,8CAA8C;AAClD;AACA;AACA,EAAEb,qBAAqB,CAACxB,MAAM,KAAK,CAAC,GAClC,gHAAgH,GAChH,yBAAyBwB,qBAAqB,CAACQ,IAAI,CAAC,IAAI,CAAC,4GAA4G;AACvK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KACK,CAACO,IAAI,CAAC,CAAC;IAER7D,SAAS,CAACmD,aAAa,CAAC;EAC1B,CAAC;EAED,IAAI/C,iBAAiB,CAACkB,MAAM,KAAK,CAAC,IAAIrB,gBAAgB,CAACgB,IAAI,GAAG,CAAC,EAAE;IAC/D,oBACE/B,OAAA;MAAK4E,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B7E,OAAA;QAAA6E,QAAA,EAAI;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjDjF,OAAA;QAAA6E,QAAA,EAAG;MAA6E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpFjF,OAAA;QAAQkF,OAAO,EAAEtC,gBAAiB;QAAAiC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAEV;EAEA,IAAI/D,iBAAiB,CAACkB,MAAM,KAAK,CAAC,EAAE;IAClC,oBAAOpC,OAAA;MAAA6E,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9B;EAEA,MAAME,eAAe,GAAGjE,iBAAiB,CAACb,oBAAoB,CAAC;EAC/D,MAAM+E,OAAO,GAAG,CACd,qFAAqF,EACrF,2CAA2C,EAC3C,wCAAwC,EACxC,oFAAoF,EACpF,0CAA0C,EAC1C,wCAAwC,EACxC,+BAA+B,CAChC;EAED,oBACEpF,OAAA;IAAK4E,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3B7E,OAAA;MAAKqF,KAAK,EAAE;QACVC,eAAe,EAAE,SAAS;QAC1BC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,QAAQ;QAChBC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE;MACb,CAAE;MAAAd,QAAA,gBACA7E,OAAA;QAAIqF,KAAK,EAAE;UAAEO,KAAK,EAAE,SAAS;UAAEJ,MAAM,EAAE;QAAa,CAAE;QAAAX,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLjF,OAAA;QAAGqF,KAAK,EAAE;UAAEG,MAAM,EAAE,YAAY;UAAEK,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJjF,OAAA;QAAKqF,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAApB,QAAA,gBACvF7E,OAAA;UACEkG,IAAI,EAAC,WAAW;UAChBb,KAAK,EAAE;YACLS,OAAO,EAAE,cAAc;YACvBP,OAAO,EAAE,WAAW;YACpBD,eAAe,EAAE,SAAS;YAC1BM,KAAK,EAAE,OAAO;YACdO,cAAc,EAAE,MAAM;YACtBV,YAAY,EAAE,KAAK;YACnBW,UAAU,EAAE;UACd,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJjF,OAAA;UACEkG,IAAI,EAAC,OAAO;UACZb,KAAK,EAAE;YACLS,OAAO,EAAE,cAAc;YACvBP,OAAO,EAAE,WAAW;YACpBD,eAAe,EAAE,SAAS;YAC1BM,KAAK,EAAE,OAAO;YACdO,cAAc,EAAE,MAAM;YACtBV,YAAY,EAAE,KAAK;YACnBW,UAAU,EAAE;UACd,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjF,OAAA,CAACF,eAAe;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnBjF,OAAA;MAAKqF,KAAK,EAAE;QACVC,eAAe,EAAE,SAAS;QAC1BC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,QAAQ;QAChBC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAb,QAAA,gBACA7E,OAAA;QAAKqF,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAEK,UAAU,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAzB,QAAA,gBAC3G7E,OAAA;UAAIqF,KAAK,EAAE;YAAEG,MAAM,EAAE,CAAC;YAAEI,KAAK,EAAE;UAAU,CAAE;UAAAf,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnFjF,OAAA;UACEkF,OAAO,EAAEA,CAAA,KAAM7D,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5DiE,KAAK,EAAE;YACLC,eAAe,EAAE,SAAS;YAC1BM,KAAK,EAAE,OAAO;YACdF,MAAM,EAAE,MAAM;YACdH,OAAO,EAAE,UAAU;YACnBE,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,EAEDzD,mBAAmB,GAAG,eAAe,GAAG;QAAe;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL7D,mBAAmB,iBAClBpB,OAAA;QAAA6E,QAAA,gBACE7E,OAAA;UAAKqF,KAAK,EAAE;YAAEiB,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,gBACnC7E,OAAA;YACEkF,OAAO,EAAEvC,iBAAkB;YAC3B0C,KAAK,EAAE;cACLC,eAAe,EAAE,SAAS;cAC1BM,KAAK,EAAE,OAAO;cACdF,MAAM,EAAE,MAAM;cACdH,OAAO,EAAE,UAAU;cACnBE,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE,SAAS;cACjBC,WAAW,EAAE;YACf,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjF,OAAA;YACEkF,OAAO,EAAEtC,gBAAiB;YAC1ByC,KAAK,EAAE;cACLC,eAAe,EAAE,SAAS;cAC1BM,KAAK,EAAE,OAAO;cACdF,MAAM,EAAE,MAAM;cACdH,OAAO,EAAE,UAAU;cACnBE,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE;YACV,CAAE;YAAA1B,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENjF,OAAA;UAAKqF,KAAK,EAAE;YACVS,OAAO,EAAE,MAAM;YACfW,mBAAmB,EAAE,sCAAsC;YAC3DV,GAAG,EAAE,MAAM;YACXO,YAAY,EAAE;UAChB,CAAE;UAAAzB,QAAA,EACCvD,iBAAiB,CAACkD,GAAG,CAACtC,OAAO,iBAC5BlC,OAAA;YAAqBqF,KAAK,EAAE;cAC1BS,OAAO,EAAE,MAAM;cACfO,UAAU,EAAE,QAAQ;cACpBd,OAAO,EAAE,KAAK;cACdD,eAAe,EAAEvE,gBAAgB,CAACyB,GAAG,CAACN,OAAO,CAAC,GAAG,SAAS,GAAG,OAAO;cACpEwD,MAAM,EAAE,gBAAgB;cACxBD,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE;YACV,CAAE;YAAA1B,QAAA,gBACA7E,OAAA;cACE0G,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE5F,gBAAgB,CAACyB,GAAG,CAACN,OAAO,CAAE;cACvC0E,QAAQ,EAAEA,CAAA,KAAMtE,mBAAmB,CAACJ,OAAO,CAAE;cAC7CmD,KAAK,EAAE;gBAAEmB,WAAW,EAAE;cAAM;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACFjF,OAAA;cAAMqF,KAAK,EAAE;gBAAEQ,QAAQ,EAAE;cAAO,CAAE;cAAAhB,QAAA,EAAE3C;YAAO;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAfzC/C,OAAO;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBZ,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjF,OAAA;UAAKqF,KAAK,EAAE;YACVE,OAAO,EAAE,MAAM;YACfD,eAAe,EAAE,SAAS;YAC1BG,YAAY,EAAE,KAAK;YACnBI,QAAQ,EAAE;UACZ,CAAE;UAAAhB,QAAA,gBACA7E,OAAA;YAAA6E,QAAA,EAAQ;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAClE,gBAAgB,CAACgB,IAAI,EAAC,aAClD,eAAA/B,OAAA;YAAA6E,QAAA,EAAQ;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,iBAAiB,CAACkB,MAAM,EAAC,IACvD,eAAApC,OAAA;YAAA6E,QAAA,EAAQ;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5E,oBAAoB,GAAG,CAAC,EAAC,GAAC,EAACa,iBAAiB,CAACkB,MAAM;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENjF,OAAA;MAAK4E,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC7E,OAAA;QAAA6E,QAAA,EAAKM,eAAe,CAACT;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnCjF,OAAA;QAAK4E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/BM,eAAe,CAAC0B,OAAO,CAACrC,GAAG,CAAC,CAACxB,MAAM,EAAE8D,KAAK,kBACzC9G,OAAA;UAAiB4E,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACjC7E,OAAA;YACE0G,IAAI,EAAC,OAAO;YACZK,IAAI,EAAE,YAAY1G,oBAAoB,EAAG;YACzCsG,OAAO,EAAEpG,qBAAqB,KAAKyC,MAAO;YAC1C4D,QAAQ,EAAEA,CAAA,KAAM7D,yBAAyB,CAACC,MAAM;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACFjF,OAAA;YAAA6E,QAAA,EAAQ7B;UAAM;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvBjF,OAAA;YACE0G,IAAI,EAAC,UAAU;YACfC,OAAO,EAAE,CAAC,CAAClG,kBAAkB,CAACuC,MAAM,CAAE;YACtC4D,QAAQ,EAAEA,CAAA,KAAM3D,uBAAuB,CAACD,MAAM;UAAE;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA,GAZM6B,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNjF,OAAA;QAAK4E,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7E,OAAA;UAAA6E,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC1BG,OAAO,CAACZ,GAAG,CAAC,CAACrB,MAAM,EAAE2D,KAAK,kBACzB9G,OAAA;UAAiB4E,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACjC7E,OAAA;YAAA6E,QAAA,EAAQ1B;UAAM;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvBjF,OAAA;YACE0G,IAAI,EAAC,UAAU;YACfC,OAAO,EAAEhG,eAAe,CAACqG,QAAQ,CAAC7D,MAAM,CAAE;YAC1CyD,QAAQ,EAAGK,CAAC,IAAK/D,kBAAkB,CAACC,MAAM,EAAE8D,CAAC,CAACC,MAAM,CAACP,OAAO;UAAE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA,GANM6B,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNjF,OAAA;QAAQkF,OAAO,EAAE1B,oBAAqB;QAAAqB,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC9DpE,MAAM,iBACLb,OAAA;QAAK4E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B7E,OAAA;UAAA6E,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BjF,OAAA;UAAA6E,QAAA,EAAMhE;QAAM;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACN,eACDjF,OAAA;QAAK4E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC7E,OAAA;UAAQkF,OAAO,EAAEpC,cAAe;UAACqE,QAAQ,EAAE9G,oBAAoB,KAAK,CAAE;UAAAwE,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjF,OAAA;UACEkF,OAAO,EAAErC,UAAW;UACpBsE,QAAQ,EAAE9G,oBAAoB,KAAKa,iBAAiB,CAACkB,MAAM,GAAG,CAAE;UAAAyC,QAAA,EACjE;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC/E,EAAA,CAnZQD,QAAQ;AAAAmH,EAAA,GAARnH,QAAQ;AAqZjB,eAAeA,QAAQ;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}