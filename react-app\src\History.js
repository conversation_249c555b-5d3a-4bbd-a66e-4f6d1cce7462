import React from 'react';

function History() {
  return (
    <div>
      <h1>Today's Activity Summary</h1>
      <div className="summary-section">
        <h2>AI Chat History</h2>
        <pre>
- User: What is the primary goal of the Island (Matrix Traversal) pattern?
- AI: The primary goal is to identify and process contiguous groups of elements.
- User: Explain the island pattern with code using dfs or bfs also to identify the area of the island.
- AI: [Provided a detailed explanation with C# code for DFS and BFS solutions]
        </pre>
      </div>
      <div className="summary-section">
        <h2>Code Window Changes</h2>
        <pre>
- **Created:** `IslandPattern.cs` (with DFS and BFS implementations).
- **Modified:** `CodingPatterns.csproj` (to change the startup object to run the new code).
- **Fixed:** `hashmap.cs`, `hashmap2.cs`, `hashmap3.cs` (to resolve build errors).
- **Modified:** `CodingPatterns.csproj` (to revert startup object).
        </pre>
      </div>
      <div className="summary-section">
        <h2>Console Output</h2>
        <pre>
$ dotnet run
--- Running DFS Example ---
[DFS] Total number of islands found: 6
The maximum area of an island is: 6

--- Running BFS Example ---
[BFS] Total number of islands found: 6
The maximum area of an island is: 6
        </pre>
      </div>
    </div>
  );
}

export default History;