import React, { useState, useEffect } from 'react';
import './App.css';
import questionsData from './mcqs.json';
import PatternsSummary from './PatternsSummary';

function MainPage() {
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedCorrectAnswer, setSelectedCorrectAnswer] = useState(null);
  const [promptInputAnswers, setPromptInputAnswers] = useState({});
  const [selectedPrompts, setSelectedPrompts] = useState([]);
  const [report, setReport] = useState('');

  useEffect(() => {
    const allQuestions = Object.values(questionsData).flat();
    setQuestions(allQuestions);

    const savedIndex = localStorage.getItem('currentQuestionIndex');
    if (savedIndex !== null) {
      setCurrentQuestionIndex(parseInt(savedIndex, 10));
    }
  }, []);

  useEffect(() => {
    if (questions.length > 0) {
      localStorage.setItem('currentQuestionIndex', currentQuestionIndex);
    }
  }, [currentQuestionIndex, questions.length]);

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedCorrectAnswer(null);
      setPromptInputAnswers({});
      setSelectedPrompts([]);
      setReport('');
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setSelectedCorrectAnswer(null);
      setPromptInputAnswers({});
      setSelectedPrompts([]);
      setReport('');
    }
  };

  const handleCorrectAnswerChange = (option) => {
    setSelectedCorrectAnswer(option);
  };

  const handlePromptInputChange = (option) => {
    setPromptInputAnswers({
      ...promptInputAnswers,
      [option]: !promptInputAnswers[option],
    });
  };

  const handlePromptChange = (prompt, isChecked) => {
    if (isChecked) {
      setSelectedPrompts(prev => [...prev, prompt]);
    } else {
      setSelectedPrompts(prev => prev.filter(p => p !== prompt));
    }
  };

  const handleGenerateReport = async () => {
    const reportData = {
      question: questions[currentQuestionIndex].question,
      correctAnswer: selectedCorrectAnswer,
      promptInputAnswers: Object.keys(promptInputAnswers).filter(
        (answer) => promptInputAnswers[answer]
      ),
      selectedPrompts: selectedPrompts,
    };

    try {
      const response = await fetch('http://localhost:3001/generate-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });
      const data = await response.json();
      setReport(data.report);
    } catch (error) {
      console.error('Error generating report:', error);
    }
  };

  if (questions.length === 0) {
    return <div>Loading...</div>;
  }

  const currentQuestion = questions[currentQuestionIndex];
  const prompts = [
    'can you design a solution in c# that will provide an output for the selected answer',
    'write a program thats based on the choice',
    'what is the gap between selected items',
    'what is the consequence if I\'m correct for the given choice based on the question',
    'rephrase the question based on my choice',
    'what is wrong with this implementation',
    'what if my syntasx is correct',
  ];

  return (
    <div className="App">
      {/* New CQRS Pattern Highlight */}
      <div style={{
        backgroundColor: '#e8f5e8',
        padding: '20px',
        margin: '20px 0',
        borderRadius: '8px',
        border: '2px solid #27ae60',
        textAlign: 'center'
      }}>
        <h2 style={{ color: '#27ae60', margin: '0 0 10px 0' }}>
          🎯 NEW: CQRS Pattern Implementation Available!
        </h2>
        <p style={{ margin: '0 0 15px 0', fontSize: '16px' }}>
          Learn Command Query Responsibility Segregation with step-by-step implementation and interactive prompts
        </p>
        <a
          href="/cqrs"
          style={{
            display: 'inline-block',
            padding: '10px 20px',
            backgroundColor: '#27ae60',
            color: 'white',
            textDecoration: 'none',
            borderRadius: '5px',
            fontWeight: 'bold'
          }}
        >
          Explore CQRS Pattern →
        </a>
      </div>

      {/* Patterns Summary Section */}
      <PatternsSummary />

      <div className="question-container">
        <h2>{currentQuestion.question}</h2>
        <div className="options-container">
          {currentQuestion.options.map((option, index) => (
            <div key={index} className="option">
              <input
                type="radio"
                name={`question-${currentQuestionIndex}`}
                checked={selectedCorrectAnswer === option}
                onChange={() => handleCorrectAnswerChange(option)}
              />
              <label>{option}</label>
              <input
                type="checkbox"
                checked={!!promptInputAnswers[option]}
                onChange={() => handlePromptInputChange(option)}
              />
            </div>
          ))}
        </div>
        <div className="prompts-container">
          <h3>Follow-up Prompts:</h3>
          {prompts.map((prompt, index) => (
            <div key={index} className="prompt">
              <label>{prompt}</label>
              <input
                type="checkbox"
                checked={selectedPrompts.includes(prompt)}
                onChange={(e) => handlePromptChange(prompt, e.target.checked)}
              />
            </div>
          ))}
        </div>
        <button onClick={handleGenerateReport}>Generate Report</button>
        {report && (
          <div className="report-container">
            <h3>Consolidated Report:</h3>
            <pre>{report}</pre>
          </div>
        )}
        <div className="navigation-buttons">
          <button onClick={handlePrevious} disabled={currentQuestionIndex === 0}>
            Previous
          </button>
          <button
            onClick={handleNext}
            disabled={currentQuestionIndex === questions.length - 1}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

export default MainPage;