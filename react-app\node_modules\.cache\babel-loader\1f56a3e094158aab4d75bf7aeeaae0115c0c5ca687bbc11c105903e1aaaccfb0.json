{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\patterns\\\\BinarySearchPattern.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction BinarySearchPattern() {\n  _s();\n  var _patternMCQs$currentM, _patternMCQs$currentM2;\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 3, 5, 7, 9, 11, 13, 15, 17, 19]);\n  const [target, setTarget] = useState(11);\n  const [left, setLeft] = useState(0);\n  const [right, setRight] = useState(9);\n  const [mid, setMid] = useState(4);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n  const [searchHistory, setSearchHistory] = useState([]);\n  const [currentMCQIndex, setCurrentMCQIndex] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState('');\n  const [showMCQResult, setShowMCQResult] = useState(false);\n  const [mcqScore, setMCQScore] = useState(0);\n  const [completedMCQs, setCompletedMCQs] = useState(new Set());\n  const [followUpAnswers, setFollowUpAnswers] = useState({});\n\n  // Get MCQs for Binary Search pattern\n  const patternMCQs = questionsData[\"4. Binary Search\"] || [];\n  const steps = [{\n    title: \"🔍 Understanding Binary Search Pattern\",\n    content: \"Binary Search efficiently finds elements in sorted arrays by repeatedly dividing the search space in half, achieving O(log n) time complexity.\",\n    code: `// Binary Search Template\nfunction binarySearch(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            return mid;  // Found target\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return -1;  // Target not found\n}`,\n    learningPrompt: \"Why does binary search require a sorted array?\",\n    answer: \"Binary search relies on the property that if the middle element is less than the target, all elements to the left are also less than the target, allowing us to eliminate half the search space.\"\n  }, {\n    title: \"📊 Step 1: Identify Binary Search Problems\",\n    content: \"Recognize when to use binary search: sorted arrays, search spaces, finding boundaries, or optimization problems.\",\n    code: `// Problem Types for Binary Search:\n\n// 1. Direct Search in Sorted Array\nfunction searchInsert(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        if (nums[mid] === target) return mid;\n        else if (nums[mid] < target) left = mid + 1;\n        else right = mid - 1;\n    }\n    \n    return left; // Insert position\n}\n\n// 2. Search in Rotated Array\nfunction searchRotated(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] === target) return mid;\n        \n        // Check which half is sorted\n        if (nums[left] <= nums[mid]) {\n            // Left half is sorted\n            if (nums[left] <= target && target < nums[mid]) {\n                right = mid - 1;\n            } else {\n                left = mid + 1;\n            }\n        } else {\n            // Right half is sorted\n            if (nums[mid] < target && target <= nums[right]) {\n                left = mid + 1;\n            } else {\n                right = mid - 1;\n            }\n        }\n    }\n    \n    return -1;\n}\n\n// 3. Find Peak Element\nfunction findPeakElement(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[mid + 1]) {\n            right = mid; // Peak is in left half\n        } else {\n            left = mid + 1; // Peak is in right half\n        }\n    }\n    \n    return left;\n}`,\n    learningPrompt: \"What are the key indicators that a problem can use binary search?\",\n    answer: \"1) Sorted data, 2) Search space that can be divided, 3) Monotonic function (if condition is true at position x, it's true for all positions in one direction), 4) Finding boundaries or optimal values.\"\n  }, {\n    title: \"🎯 Step 2: Set Up Search Boundaries\",\n    content: \"Initialize left and right pointers to define the search space correctly.\",\n    code: `// Step 2: Boundary Setup Patterns\n\n// Pattern 1: Standard Array Search\nlet left = 0;\nlet right = arr.length - 1;\n\n// Pattern 2: Search Insert Position\nlet left = 0;\nlet right = arr.length; // Note: length, not length-1\n\n// Pattern 3: Search in Range [min, max]\nlet left = minValue;\nlet right = maxValue;\n\n// Pattern 4: Search for First/Last Occurrence\nfunction findFirst(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            right = mid - 1; // Continue searching left for first occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}\n\nfunction findLast(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            left = mid + 1; // Continue searching right for last occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}`,\n    learningPrompt: \"Why might we use different boundary setups?\",\n    answer: \"Different problems require different search spaces: exact search uses [0, n-1], insert position uses [0, n], and range search uses [min, max] values.\"\n  }, {\n    title: \"⚡ Step 3: Calculate Mid and Compare\",\n    content: \"Calculate the middle point and make comparisons to decide which half to search next.\",\n    code: `// Step 3: Mid Calculation and Comparison Logic\n\nfunction binarySearchWithSteps(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    let steps = [];\n    \n    while (left <= right) {\n        // Calculate mid (avoid overflow)\n        let mid = Math.floor((left + right) / 2);\n        // Alternative: let mid = left + Math.floor((right - left) / 2);\n        \n        steps.push({\n            left: left,\n            right: right,\n            mid: mid,\n            midValue: arr[mid],\n            comparison: arr[mid] === target ? 'equal' : \n                       arr[mid] < target ? 'less' : 'greater'\n        });\n        \n        if (arr[mid] === target) {\n            return { found: true, index: mid, steps: steps };\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return { found: false, index: -1, steps: steps };\n}\n\n// Avoid integer overflow (important in some languages)\nfunction safeMidCalculation(left, right) {\n    // Instead of: (left + right) / 2\n    // Use: left + (right - left) / 2\n    return left + Math.floor((right - left) / 2);\n}`,\n    learningPrompt: \"Why do we use Math.floor for mid calculation?\",\n    answer: \"Math.floor ensures we get an integer index. For arrays with even length, we consistently choose the left-middle element, preventing infinite loops.\"\n  }, {\n    title: \"🎮 Step 4: Interactive Demo\",\n    content: \"Watch binary search in action as it efficiently narrows down the search space.\",\n    code: `// Live Demo: Binary Search\nconst arr = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];\nconst target = 11;\n\n// Current search space:\n// Left: ${left} (value: ${demoArray[left]})\n// Right: ${right} (value: ${demoArray[right]})\n// Mid: ${mid} (value: ${demoArray[mid]})\n// Comparison: ${demoArray[mid]} vs ${target}`,\n    learningPrompt: \"How many comparisons does binary search need in the worst case?\",\n    answer: \"At most ⌊log₂(n)⌋ + 1 comparisons, where n is the array size. For 1000 elements, that's only about 10 comparisons!\"\n  }, {\n    title: \"🔧 Step 5: Advanced Binary Search Patterns\",\n    content: \"Master advanced patterns like finding boundaries, searching in 2D matrices, and optimization problems.\",\n    code: `// Advanced Pattern 1: Search in 2D Matrix\nfunction searchMatrix(matrix, target) {\n    if (!matrix.length || !matrix[0].length) return false;\n    \n    let rows = matrix.length;\n    let cols = matrix[0].length;\n    let left = 0;\n    let right = rows * cols - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        let midValue = matrix[Math.floor(mid / cols)][mid % cols];\n        \n        if (midValue === target) {\n            return true;\n        } else if (midValue < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return false;\n}\n\n// Advanced Pattern 2: Find Minimum in Rotated Array\nfunction findMin(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[right]) {\n            // Minimum is in right half\n            left = mid + 1;\n        } else {\n            // Minimum is in left half (including mid)\n            right = mid;\n        }\n    }\n    \n    return nums[left];\n}\n\n// Advanced Pattern 3: Binary Search on Answer\nfunction minEatingSpeed(piles, h) {\n    let left = 1;\n    let right = Math.max(...piles);\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        let hours = 0;\n        \n        for (let pile of piles) {\n            hours += Math.ceil(pile / mid);\n        }\n        \n        if (hours <= h) {\n            right = mid; // Can eat slower\n        } else {\n            left = mid + 1; // Need to eat faster\n        }\n    }\n    \n    return left;\n}`,\n    learningPrompt: \"What's the key insight in 'binary search on answer' problems?\",\n    answer: \"Instead of searching in an array, we search in the range of possible answers. If a speed/capacity works, all higher values also work (monotonic property).\"\n  }];\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    setSearchHistory([]);\n    let l = 0;\n    let r = demoArray.length - 1;\n    let history = [];\n    while (l <= r) {\n      let m = Math.floor((l + r) / 2);\n      setLeft(l);\n      setRight(r);\n      setMid(m);\n      const step = {\n        left: l,\n        right: r,\n        mid: m,\n        midValue: demoArray[m],\n        comparison: demoArray[m] === target ? 'equal' : demoArray[m] < target ? 'less' : 'greater'\n      };\n      history.push(step);\n      setSearchHistory([...history]);\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      if (demoArray[m] === target) {\n        setResult(`Found ${target} at index ${m}!`);\n        setIsRunning(false);\n        return;\n      } else if (demoArray[m] < target) {\n        l = m + 1;\n      } else {\n        r = m - 1;\n      }\n    }\n    setResult(`${target} not found in array`);\n    setIsRunning(false);\n  };\n  const resetDemo = () => {\n    setLeft(0);\n    setRight(demoArray.length - 1);\n    setMid(Math.floor((demoArray.length - 1) / 2));\n    setResult(null);\n    setSearchHistory([]);\n    setIsRunning(false);\n  };\n\n  // MCQ handling functions\n  const handleMCQAnswer = answer => {\n    setSelectedAnswer(answer);\n    setShowMCQResult(true);\n    const currentMCQ = patternMCQs[currentMCQIndex];\n    if (answer === currentMCQ.answer) {\n      setMCQScore(prev => prev + 1);\n    }\n    setCompletedMCQs(prev => new Set([...prev, currentMCQIndex]));\n  };\n  const nextMCQ = () => {\n    if (currentMCQIndex < patternMCQs.length - 1) {\n      setCurrentMCQIndex(prev => prev + 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n  const previousMCQ = () => {\n    if (currentMCQIndex > 0) {\n      setCurrentMCQIndex(prev => prev - 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n  const handleFollowUpAnswer = (prompt, answer) => {\n    setFollowUpAnswers(prev => ({\n      ...prev,\n      [prompt]: answer\n    }));\n  };\n\n  // Follow-up prompts specific to Binary Search\n  const followUpPrompts = [\"Explain why binary search only works on sorted arrays and what happens if the array is not sorted.\", \"How would you modify binary search to find the first or last occurrence of a duplicate element?\", \"Describe the difference between iterative and recursive binary search implementations.\", \"What are some real-world applications where binary search is commonly used?\", \"How do you handle edge cases in binary search (empty arrays, single elements, target not found)?\"];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    style: {\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#9b59b6',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83D\\uDD0D Binary Search Pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em',\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: \"Master binary search to achieve O(log n) solutions for search and optimization problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#9b59b6',\n            height: '100%',\n            width: `${(currentStep + 1) / steps.length * 100}%`,\n            transition: 'width 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0',\n          color: '#7f8c8d'\n        },\n        children: [\"Step \", currentStep + 1, \" of \", steps.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '10px',\n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      },\n      children: steps.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentStep(index),\n        style: {\n          padding: '8px 16px',\n          border: currentStep === index ? '2px solid #9b59b6' : '1px solid #ddd',\n          borderRadius: '20px',\n          backgroundColor: currentStep === index ? '#9b59b6' : 'white',\n          color: currentStep === index ? 'white' : '#333',\n          cursor: 'pointer',\n          fontSize: '14px'\n        },\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#2c3e50',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          lineHeight: '1.6',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            margin: 0,\n            whiteSpace: 'pre-wrap'\n          },\n          children: steps[currentStep].code\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f0f8ff',\n          border: '2px solid #9b59b6',\n          borderRadius: '12px',\n          padding: '20px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#9b59b6',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83C\\uDFAE Live Demo: Binary Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '3px',\n              justifyContent: 'center',\n              marginBottom: '10px',\n              flexWrap: 'wrap'\n            },\n            children: demoArray.map((num, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '35px',\n                height: '35px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: index === mid ? '#e74c3c' : index === left ? '#27ae60' : index === right ? '#3498db' : index >= left && index <= right ? '#f39c12' : '#ecf0f1',\n                color: index >= left && index <= right ? 'white' : '#333',\n                borderRadius: '6px',\n                fontWeight: 'bold',\n                fontSize: '12px',\n                border: '2px solid',\n                borderColor: index === mid ? '#c0392b' : index === left ? '#229954' : index === right ? '#2980b9' : index >= left && index <= right ? '#e67e22' : '#bdc3c7',\n                position: 'relative'\n              },\n              children: [num, index === left && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-20px',\n                  fontSize: '10px',\n                  color: '#27ae60',\n                  fontWeight: 'bold'\n                },\n                children: \"L\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 23\n              }, this), index === mid && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-20px',\n                  fontSize: '10px',\n                  color: '#e74c3c',\n                  fontWeight: 'bold'\n                },\n                children: \"M\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 23\n              }, this), index === right && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-20px',\n                  fontSize: '10px',\n                  color: '#3498db',\n                  fontWeight: 'bold'\n                },\n                children: \"R\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              fontSize: '14px',\n              color: '#666',\n              marginBottom: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#27ae60'\n              },\n              children: [\"\\uD83D\\uDFE2 Left: \", left]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#e74c3c'\n              },\n              children: [\"\\uD83D\\uDD34 Mid: \", mid, \" (value: \", demoArray[mid], \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#3498db'\n              },\n              children: [\"\\uD83D\\uDD35 Right: \", right]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Target: \", target]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this), searchHistory.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#2c3e50',\n                margin: '0 0 10px 0',\n                fontSize: '14px'\n              },\n              children: \"\\uD83D\\uDD0D Search History:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxHeight: '100px',\n                overflowY: 'auto',\n                fontSize: '12px'\n              },\n              children: searchHistory.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '5px',\n                  backgroundColor: '#f8f9fa',\n                  margin: '2px 0',\n                  borderRadius: '4px',\n                  border: '1px solid #dee2e6'\n                },\n                children: [\"Step \", index + 1, \": L=\", step.left, \", M=\", step.mid, \"(\", step.midValue, \"), R=\", step.right, \" \\u2192\", step.comparison === 'equal' ? ' Found!' : step.comparison === 'less' ? ' Search right' : ' Search left']\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            gap: '10px',\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: runDemo,\n            disabled: isRunning,\n            style: {\n              backgroundColor: '#9b59b6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: isRunning ? 'not-allowed' : 'pointer',\n              opacity: isRunning ? 0.6 : 1\n            },\n            children: isRunning ? '🔄 Searching...' : '▶️ Run Demo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetDemo,\n            style: {\n              backgroundColor: '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: \"\\uD83D\\uDD04 Reset\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 13\n        }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '10px',\n            backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n            border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n            borderRadius: '6px',\n            textAlign: 'center',\n            fontWeight: 'bold',\n            color: result.includes('Found') ? '#155724' : '#721c24'\n          },\n          children: result\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0'\n          },\n          children: \"\\uD83E\\uDD14 Learning Prompt:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0',\n            fontStyle: 'italic'\n          },\n          children: steps[currentStep].learningPrompt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            style: {\n              color: '#856404',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCA1 Click to see answer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#856404',\n              margin: '10px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: steps[currentStep].answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginTop: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.max(0, currentStep - 1)),\n          disabled: currentStep === 0,\n          style: {\n            backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.min(steps.length - 1, currentStep + 1)),\n          disabled: currentStep === steps.length - 1,\n          style: {\n            backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#9b59b6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #9b59b6'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#9b59b6',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83C\\uDFAF Practice Problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 729,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '15px'\n        },\n        children: [{\n          name: \"Binary Search\",\n          difficulty: \"Easy\",\n          time: \"10 min\"\n        }, {\n          name: \"Search Insert Position\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Find First and Last Position\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Search in Rotated Array\",\n          difficulty: \"Medium\",\n          time: \"30 min\"\n        }, {\n          name: \"Find Peak Element\",\n          difficulty: \"Medium\",\n          time: \"20 min\"\n        }, {\n          name: \"Median of Two Sorted Arrays\",\n          difficulty: \"Hard\",\n          time: \"45 min\"\n        }].map((problem, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#2c3e50',\n              margin: '0 0 8px 0'\n            },\n            children: problem.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: problem.difficulty === 'Easy' ? '#27ae60' : problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c'\n              },\n              children: problem.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this), ' • ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: problem.time\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 723,\n      columnNumber: 7\n    }, this), patternMCQs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px',\n        border: '2px solid #3498db'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#3498db',\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: \"\\uD83D\\uDCDD Test Your Knowledge - Binary Search MCQs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#666',\n            fontSize: '14px'\n          },\n          children: [\"Question \", currentMCQIndex + 1, \" of \", patternMCQs.length, \" | Score: \", mcqScore, \"/\", completedMCQs.size, \" | Accuracy: \", completedMCQs.size > 0 ? Math.round(mcqScore / completedMCQs.size * 100) : 0, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 782,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          padding: '20px',\n          borderRadius: '8px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#2c3e50',\n            marginBottom: '15px'\n          },\n          children: (_patternMCQs$currentM = patternMCQs[currentMCQIndex]) === null || _patternMCQs$currentM === void 0 ? void 0 : _patternMCQs$currentM.question\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gap: '10px'\n          },\n          children: (_patternMCQs$currentM2 = patternMCQs[currentMCQIndex]) === null || _patternMCQs$currentM2 === void 0 ? void 0 : _patternMCQs$currentM2.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => !showMCQResult && handleMCQAnswer(option),\n            disabled: showMCQResult,\n            style: {\n              padding: '12px 16px',\n              border: '2px solid',\n              borderColor: showMCQResult ? option === patternMCQs[currentMCQIndex].answer ? '#27ae60' : option === selectedAnswer ? '#e74c3c' : '#ddd' : selectedAnswer === option ? '#3498db' : '#ddd',\n              borderRadius: '8px',\n              backgroundColor: showMCQResult ? option === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : option === selectedAnswer ? '#fadbd8' : 'white' : selectedAnswer === option ? '#ebf3fd' : 'white',\n              color: '#2c3e50',\n              cursor: showMCQResult ? 'default' : 'pointer',\n              textAlign: 'left',\n              transition: 'all 0.2s ease'\n            },\n            children: [String.fromCharCode(65 + index), \". \", option, showMCQResult && option === patternMCQs[currentMCQIndex].answer && ' ✅', showMCQResult && option === selectedAnswer && option !== patternMCQs[currentMCQIndex].answer && ' ❌']\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 13\n        }, this), showMCQResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '15px',\n            backgroundColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : '#fadbd8',\n            borderRadius: '8px',\n            border: '1px solid',\n            borderColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            style: {\n              color: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n            },\n            children: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '🎉 Correct!' : '❌ Incorrect'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '5px 0 0 0',\n              color: '#2c3e50'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Correct Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 19\n            }, this), \" \", patternMCQs[currentMCQIndex].answer]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 791,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: previousMCQ,\n          disabled: currentMCQIndex === 0,\n          style: {\n            backgroundColor: currentMCQIndex === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '6px',\n            cursor: currentMCQIndex === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous MCQ\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '5px',\n              justifyContent: 'center'\n            },\n            children: patternMCQs.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '12px',\n                height: '12px',\n                borderRadius: '50%',\n                backgroundColor: index === currentMCQIndex ? '#3498db' : completedMCQs.has(index) ? '#27ae60' : '#ddd'\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: nextMCQ,\n          disabled: currentMCQIndex === patternMCQs.length - 1,\n          style: {\n            backgroundColor: currentMCQIndex === patternMCQs.length - 1 ? '#bdc3c7' : '#3498db',\n            color: 'white',\n            border: 'none',\n            padding: '10px 20px',\n            borderRadius: '6px',\n            cursor: currentMCQIndex === patternMCQs.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next MCQ \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 769,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        border: '2px solid #f39c12'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#f39c12',\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: \"\\uD83D\\uDCAD Follow-up Learning Prompts\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          textAlign: 'center',\n          marginBottom: '25px'\n        },\n        children: \"Deepen your understanding by answering these thought-provoking questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 921,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gap: '20px'\n        },\n        children: followUpPrompts.map((prompt, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#fef9e7',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #f1c40f'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#f39c12',\n              marginBottom: '10px'\n            },\n            children: [index + 1, \". \", prompt]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: followUpAnswers[prompt] || '',\n            onChange: e => handleFollowUpAnswer(prompt, e.target.value),\n            placeholder: \"Type your answer here...\",\n            style: {\n              width: '100%',\n              minHeight: '80px',\n              padding: '12px',\n              border: '1px solid #ddd',\n              borderRadius: '6px',\n              fontSize: '14px',\n              fontFamily: 'inherit',\n              resize: 'vertical'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '25px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#e8f5e8',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #27ae60'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            style: {\n              color: '#27ae60'\n            },\n            children: [\"\\uD83D\\uDCCA Progress: \", Object.keys(followUpAnswers).length, \"/\", followUpPrompts.length, \" prompts answered\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '5px 0 0 0',\n              color: '#666',\n              fontSize: '14px'\n            },\n            children: \"Complete all prompts to master the Binary Search pattern!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 911,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 410,\n    columnNumber: 5\n  }, this);\n}\n_s(BinarySearchPattern, \"J3FDr36s6bIl5Zy4Vt+RrMfWEzI=\");\n_c = BinarySearchPattern;\nexport default BinarySearchPattern;\nvar _c;\n$RefreshReg$(_c, \"BinarySearchPattern\");", "map": {"version": 3, "names": ["React", "useState", "questionsData", "jsxDEV", "_jsxDEV", "BinarySearchPattern", "_s", "_patternMCQs$currentM", "_patternMCQs$currentM2", "currentStep", "setCurrentStep", "demoArray", "setDemoArray", "target", "<PERSON><PERSON><PERSON><PERSON>", "left", "setLeft", "right", "setRight", "mid", "setMid", "isRunning", "setIsRunning", "result", "setResult", "searchHistory", "setSearchHistory", "currentMCQIndex", "setCurrentMCQIndex", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedAnswer", "showMCQResult", "setShowMCQResult", "mcqScore", "setMCQScore", "completedMCQs", "setCompletedMCQs", "Set", "followUpAnswers", "setFollowUpAnswers", "patternMCQs", "steps", "title", "content", "code", "learningPrompt", "answer", "runDemo", "l", "r", "length", "history", "m", "Math", "floor", "step", "midValue", "comparison", "push", "Promise", "resolve", "setTimeout", "resetDemo", "handleMCQAnswer", "currentMCQ", "prev", "nextMCQ", "previousMCQ", "handleFollowUpAnswer", "prompt", "followUpPrompts", "className", "style", "fontFamily", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "borderRadius", "height", "overflow", "width", "transition", "display", "justifyContent", "gap", "flexWrap", "map", "_", "index", "onClick", "padding", "border", "cursor", "boxShadow", "lineHeight", "whiteSpace", "num", "alignItems", "fontWeight", "borderColor", "position", "top", "maxHeight", "overflowY", "disabled", "opacity", "marginTop", "includes", "fontStyle", "paddingLeft", "max", "min", "gridTemplateColumns", "name", "difficulty", "time", "problem", "size", "round", "question", "options", "option", "String", "fromCharCode", "has", "value", "onChange", "e", "placeholder", "minHeight", "resize", "Object", "keys", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/patterns/BinarySearchPattern.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\n\nfunction BinarySearchPattern() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 3, 5, 7, 9, 11, 13, 15, 17, 19]);\n  const [target, setTarget] = useState(11);\n  const [left, setLeft] = useState(0);\n  const [right, setRight] = useState(9);\n  const [mid, setMid] = useState(4);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n  const [searchHistory, setSearchHistory] = useState([]);\n  const [currentMCQIndex, setCurrentMCQIndex] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState('');\n  const [showMCQResult, setShowMCQResult] = useState(false);\n  const [mcqScore, setMCQScore] = useState(0);\n  const [completedMCQs, setCompletedMCQs] = useState(new Set());\n  const [followUpAnswers, setFollowUpAnswers] = useState({});\n\n  // Get MCQs for Binary Search pattern\n  const patternMCQs = questionsData[\"4. Binary Search\"] || [];\n\n  const steps = [\n    {\n      title: \"🔍 Understanding Binary Search Pattern\",\n      content: \"Binary Search efficiently finds elements in sorted arrays by repeatedly dividing the search space in half, achieving O(log n) time complexity.\",\n      code: `// Binary Search Template\nfunction binarySearch(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            return mid;  // Found target\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return -1;  // Target not found\n}`,\n      learningPrompt: \"Why does binary search require a sorted array?\",\n      answer: \"Binary search relies on the property that if the middle element is less than the target, all elements to the left are also less than the target, allowing us to eliminate half the search space.\"\n    },\n    {\n      title: \"📊 Step 1: Identify Binary Search Problems\",\n      content: \"Recognize when to use binary search: sorted arrays, search spaces, finding boundaries, or optimization problems.\",\n      code: `// Problem Types for Binary Search:\n\n// 1. Direct Search in Sorted Array\nfunction searchInsert(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        if (nums[mid] === target) return mid;\n        else if (nums[mid] < target) left = mid + 1;\n        else right = mid - 1;\n    }\n    \n    return left; // Insert position\n}\n\n// 2. Search in Rotated Array\nfunction searchRotated(nums, target) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] === target) return mid;\n        \n        // Check which half is sorted\n        if (nums[left] <= nums[mid]) {\n            // Left half is sorted\n            if (nums[left] <= target && target < nums[mid]) {\n                right = mid - 1;\n            } else {\n                left = mid + 1;\n            }\n        } else {\n            // Right half is sorted\n            if (nums[mid] < target && target <= nums[right]) {\n                left = mid + 1;\n            } else {\n                right = mid - 1;\n            }\n        }\n    }\n    \n    return -1;\n}\n\n// 3. Find Peak Element\nfunction findPeakElement(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[mid + 1]) {\n            right = mid; // Peak is in left half\n        } else {\n            left = mid + 1; // Peak is in right half\n        }\n    }\n    \n    return left;\n}`,\n      learningPrompt: \"What are the key indicators that a problem can use binary search?\",\n      answer: \"1) Sorted data, 2) Search space that can be divided, 3) Monotonic function (if condition is true at position x, it's true for all positions in one direction), 4) Finding boundaries or optimal values.\"\n    },\n    {\n      title: \"🎯 Step 2: Set Up Search Boundaries\",\n      content: \"Initialize left and right pointers to define the search space correctly.\",\n      code: `// Step 2: Boundary Setup Patterns\n\n// Pattern 1: Standard Array Search\nlet left = 0;\nlet right = arr.length - 1;\n\n// Pattern 2: Search Insert Position\nlet left = 0;\nlet right = arr.length; // Note: length, not length-1\n\n// Pattern 3: Search in Range [min, max]\nlet left = minValue;\nlet right = maxValue;\n\n// Pattern 4: Search for First/Last Occurrence\nfunction findFirst(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            right = mid - 1; // Continue searching left for first occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}\n\nfunction findLast(arr, target) {\n    let left = 0, right = arr.length - 1;\n    let result = -1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (arr[mid] === target) {\n            result = mid;\n            left = mid + 1; // Continue searching right for last occurrence\n        } else if (arr[mid] < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return result;\n}`,\n      learningPrompt: \"Why might we use different boundary setups?\",\n      answer: \"Different problems require different search spaces: exact search uses [0, n-1], insert position uses [0, n], and range search uses [min, max] values.\"\n    },\n    {\n      title: \"⚡ Step 3: Calculate Mid and Compare\",\n      content: \"Calculate the middle point and make comparisons to decide which half to search next.\",\n      code: `// Step 3: Mid Calculation and Comparison Logic\n\nfunction binarySearchWithSteps(arr, target) {\n    let left = 0;\n    let right = arr.length - 1;\n    let steps = [];\n    \n    while (left <= right) {\n        // Calculate mid (avoid overflow)\n        let mid = Math.floor((left + right) / 2);\n        // Alternative: let mid = left + Math.floor((right - left) / 2);\n        \n        steps.push({\n            left: left,\n            right: right,\n            mid: mid,\n            midValue: arr[mid],\n            comparison: arr[mid] === target ? 'equal' : \n                       arr[mid] < target ? 'less' : 'greater'\n        });\n        \n        if (arr[mid] === target) {\n            return { found: true, index: mid, steps: steps };\n        } else if (arr[mid] < target) {\n            left = mid + 1;  // Search right half\n        } else {\n            right = mid - 1; // Search left half\n        }\n    }\n    \n    return { found: false, index: -1, steps: steps };\n}\n\n// Avoid integer overflow (important in some languages)\nfunction safeMidCalculation(left, right) {\n    // Instead of: (left + right) / 2\n    // Use: left + (right - left) / 2\n    return left + Math.floor((right - left) / 2);\n}`,\n      learningPrompt: \"Why do we use Math.floor for mid calculation?\",\n      answer: \"Math.floor ensures we get an integer index. For arrays with even length, we consistently choose the left-middle element, preventing infinite loops.\"\n    },\n    {\n      title: \"🎮 Step 4: Interactive Demo\",\n      content: \"Watch binary search in action as it efficiently narrows down the search space.\",\n      code: `// Live Demo: Binary Search\nconst arr = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19];\nconst target = 11;\n\n// Current search space:\n// Left: ${left} (value: ${demoArray[left]})\n// Right: ${right} (value: ${demoArray[right]})\n// Mid: ${mid} (value: ${demoArray[mid]})\n// Comparison: ${demoArray[mid]} vs ${target}`,\n      learningPrompt: \"How many comparisons does binary search need in the worst case?\",\n      answer: \"At most ⌊log₂(n)⌋ + 1 comparisons, where n is the array size. For 1000 elements, that's only about 10 comparisons!\"\n    },\n    {\n      title: \"🔧 Step 5: Advanced Binary Search Patterns\",\n      content: \"Master advanced patterns like finding boundaries, searching in 2D matrices, and optimization problems.\",\n      code: `// Advanced Pattern 1: Search in 2D Matrix\nfunction searchMatrix(matrix, target) {\n    if (!matrix.length || !matrix[0].length) return false;\n    \n    let rows = matrix.length;\n    let cols = matrix[0].length;\n    let left = 0;\n    let right = rows * cols - 1;\n    \n    while (left <= right) {\n        let mid = Math.floor((left + right) / 2);\n        let midValue = matrix[Math.floor(mid / cols)][mid % cols];\n        \n        if (midValue === target) {\n            return true;\n        } else if (midValue < target) {\n            left = mid + 1;\n        } else {\n            right = mid - 1;\n        }\n    }\n    \n    return false;\n}\n\n// Advanced Pattern 2: Find Minimum in Rotated Array\nfunction findMin(nums) {\n    let left = 0, right = nums.length - 1;\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        \n        if (nums[mid] > nums[right]) {\n            // Minimum is in right half\n            left = mid + 1;\n        } else {\n            // Minimum is in left half (including mid)\n            right = mid;\n        }\n    }\n    \n    return nums[left];\n}\n\n// Advanced Pattern 3: Binary Search on Answer\nfunction minEatingSpeed(piles, h) {\n    let left = 1;\n    let right = Math.max(...piles);\n    \n    while (left < right) {\n        let mid = Math.floor((left + right) / 2);\n        let hours = 0;\n        \n        for (let pile of piles) {\n            hours += Math.ceil(pile / mid);\n        }\n        \n        if (hours <= h) {\n            right = mid; // Can eat slower\n        } else {\n            left = mid + 1; // Need to eat faster\n        }\n    }\n    \n    return left;\n}`,\n      learningPrompt: \"What's the key insight in 'binary search on answer' problems?\",\n      answer: \"Instead of searching in an array, we search in the range of possible answers. If a speed/capacity works, all higher values also work (monotonic property).\"\n    }\n  ];\n\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    setSearchHistory([]);\n    \n    let l = 0;\n    let r = demoArray.length - 1;\n    let history = [];\n    \n    while (l <= r) {\n      let m = Math.floor((l + r) / 2);\n      setLeft(l);\n      setRight(r);\n      setMid(m);\n      \n      const step = {\n        left: l,\n        right: r,\n        mid: m,\n        midValue: demoArray[m],\n        comparison: demoArray[m] === target ? 'equal' : \n                   demoArray[m] < target ? 'less' : 'greater'\n      };\n      \n      history.push(step);\n      setSearchHistory([...history]);\n      \n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      if (demoArray[m] === target) {\n        setResult(`Found ${target} at index ${m}!`);\n        setIsRunning(false);\n        return;\n      } else if (demoArray[m] < target) {\n        l = m + 1;\n      } else {\n        r = m - 1;\n      }\n    }\n    \n    setResult(`${target} not found in array`);\n    setIsRunning(false);\n  };\n\n  const resetDemo = () => {\n    setLeft(0);\n    setRight(demoArray.length - 1);\n    setMid(Math.floor((demoArray.length - 1) / 2));\n    setResult(null);\n    setSearchHistory([]);\n    setIsRunning(false);\n  };\n\n  // MCQ handling functions\n  const handleMCQAnswer = (answer) => {\n    setSelectedAnswer(answer);\n    setShowMCQResult(true);\n\n    const currentMCQ = patternMCQs[currentMCQIndex];\n    if (answer === currentMCQ.answer) {\n      setMCQScore(prev => prev + 1);\n    }\n    setCompletedMCQs(prev => new Set([...prev, currentMCQIndex]));\n  };\n\n  const nextMCQ = () => {\n    if (currentMCQIndex < patternMCQs.length - 1) {\n      setCurrentMCQIndex(prev => prev + 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n\n  const previousMCQ = () => {\n    if (currentMCQIndex > 0) {\n      setCurrentMCQIndex(prev => prev - 1);\n      setSelectedAnswer('');\n      setShowMCQResult(false);\n    }\n  };\n\n  const handleFollowUpAnswer = (prompt, answer) => {\n    setFollowUpAnswers(prev => ({\n      ...prev,\n      [prompt]: answer\n    }));\n  };\n\n  // Follow-up prompts specific to Binary Search\n  const followUpPrompts = [\n    \"Explain why binary search only works on sorted arrays and what happens if the array is not sorted.\",\n    \"How would you modify binary search to find the first or last occurrence of a duplicate element?\",\n    \"Describe the difference between iterative and recursive binary search implementations.\",\n    \"What are some real-world applications where binary search is commonly used?\",\n    \"How do you handle edge cases in binary search (empty arrays, single elements, target not found)?\"\n  ];\n\n  return (\n    <div className=\"page-content\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Header */}\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#9b59b6', fontSize: '2.5em', marginBottom: '10px' }}>\n          🔍 Binary Search Pattern\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>\n          Master binary search to achieve O(log n) solutions for search and optimization problems\n        </p>\n      </div>\n\n      {/* Progress Bar */}\n      <div style={{ marginBottom: '30px' }}>\n        <div style={{\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        }}>\n          <div style={{\n            backgroundColor: '#9b59b6',\n            height: '100%',\n            width: `${((currentStep + 1) / steps.length) * 100}%`,\n            transition: 'width 0.3s ease'\n          }}></div>\n        </div>\n        <p style={{ textAlign: 'center', margin: '10px 0', color: '#7f8c8d' }}>\n          Step {currentStep + 1} of {steps.length}\n        </p>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        gap: '10px', \n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      }}>\n        {steps.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentStep(index)}\n            style={{\n              padding: '8px 16px',\n              border: currentStep === index ? '2px solid #9b59b6' : '1px solid #ddd',\n              borderRadius: '20px',\n              backgroundColor: currentStep === index ? '#9b59b6' : 'white',\n              color: currentStep === index ? 'white' : '#333',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Current Step Content */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      }}>\n        <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>\n          {steps[currentStep].title}\n        </h2>\n        \n        <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>\n          {steps[currentStep].content}\n        </p>\n\n        {/* Code Block */}\n        <div style={{\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        }}>\n          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>\n            {steps[currentStep].code}\n          </pre>\n        </div>\n\n        {/* Interactive Demo for Step 4 */}\n        {currentStep === 4 && (\n          <div style={{\n            backgroundColor: '#f0f8ff',\n            border: '2px solid #9b59b6',\n            borderRadius: '12px',\n            padding: '20px',\n            marginBottom: '20px'\n          }}>\n            <h3 style={{ color: '#9b59b6', marginBottom: '15px' }}>🎮 Live Demo: Binary Search</h3>\n            \n            {/* Array Visualization */}\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', gap: '3px', justifyContent: 'center', marginBottom: '10px', flexWrap: 'wrap' }}>\n                {demoArray.map((num, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '35px',\n                      height: '35px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      backgroundColor: \n                        index === mid ? '#e74c3c' :\n                        index === left ? '#27ae60' :\n                        index === right ? '#3498db' :\n                        index >= left && index <= right ? '#f39c12' : '#ecf0f1',\n                      color: (index >= left && index <= right) ? 'white' : '#333',\n                      borderRadius: '6px',\n                      fontWeight: 'bold',\n                      fontSize: '12px',\n                      border: '2px solid',\n                      borderColor: \n                        index === mid ? '#c0392b' :\n                        index === left ? '#229954' :\n                        index === right ? '#2980b9' :\n                        index >= left && index <= right ? '#e67e22' : '#bdc3c7',\n                      position: 'relative'\n                    }}\n                  >\n                    {num}\n                    {index === left && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-20px',\n                        fontSize: '10px',\n                        color: '#27ae60',\n                        fontWeight: 'bold'\n                      }}>\n                        L\n                      </div>\n                    )}\n                    {index === mid && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-20px',\n                        fontSize: '10px',\n                        color: '#e74c3c',\n                        fontWeight: 'bold'\n                      }}>\n                        M\n                      </div>\n                    )}\n                    {index === right && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-20px',\n                        fontSize: '10px',\n                        color: '#3498db',\n                        fontWeight: 'bold'\n                      }}>\n                        R\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n              \n              <div style={{ textAlign: 'center', fontSize: '14px', color: '#666', marginBottom: '15px' }}>\n                <span style={{ color: '#27ae60' }}>🟢 Left: {left}</span>\n                {' | '}\n                <span style={{ color: '#e74c3c' }}>🔴 Mid: {mid} (value: {demoArray[mid]})</span>\n                {' | '}\n                <span style={{ color: '#3498db' }}>🔵 Right: {right}</span>\n                {' | '}\n                <span>Target: {target}</span>\n              </div>\n\n              {/* Search History */}\n              {searchHistory.length > 0 && (\n                <div style={{ marginBottom: '15px' }}>\n                  <h4 style={{ color: '#2c3e50', margin: '0 0 10px 0', fontSize: '14px' }}>\n                    🔍 Search History:\n                  </h4>\n                  <div style={{ maxHeight: '100px', overflowY: 'auto', fontSize: '12px' }}>\n                    {searchHistory.map((step, index) => (\n                      <div key={index} style={{ \n                        padding: '5px', \n                        backgroundColor: '#f8f9fa', \n                        margin: '2px 0',\n                        borderRadius: '4px',\n                        border: '1px solid #dee2e6'\n                      }}>\n                        Step {index + 1}: L={step.left}, M={step.mid}({step.midValue}), R={step.right} → \n                        {step.comparison === 'equal' ? ' Found!' :\n                         step.comparison === 'less' ? ' Search right' : ' Search left'}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Controls */}\n            <div style={{ textAlign: 'center', gap: '10px', display: 'flex', justifyContent: 'center' }}>\n              <button\n                onClick={runDemo}\n                disabled={isRunning}\n                style={{\n                  backgroundColor: '#9b59b6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: isRunning ? 'not-allowed' : 'pointer',\n                  opacity: isRunning ? 0.6 : 1\n                }}\n              >\n                {isRunning ? '🔄 Searching...' : '▶️ Run Demo'}\n              </button>\n              \n              <button\n                onClick={resetDemo}\n                style={{\n                  backgroundColor: '#95a5a6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: 'pointer'\n                }}\n              >\n                🔄 Reset\n              </button>\n            </div>\n\n            {result && (\n              <div style={{\n                marginTop: '15px',\n                padding: '10px',\n                backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n                border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n                borderRadius: '6px',\n                textAlign: 'center',\n                fontWeight: 'bold',\n                color: result.includes('Found') ? '#155724' : '#721c24'\n              }}>\n                {result}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Learning Prompt */}\n        <div style={{\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        }}>\n          <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>\n            🤔 Learning Prompt:\n          </h4>\n          <p style={{ color: '#856404', margin: '0 0 10px 0', fontStyle: 'italic' }}>\n            {steps[currentStep].learningPrompt}\n          </p>\n          <details>\n            <summary style={{ color: '#856404', cursor: 'pointer', fontWeight: 'bold' }}>\n              💡 Click to see answer\n            </summary>\n            <p style={{ color: '#856404', margin: '10px 0 0 0', paddingLeft: '20px' }}>\n              {steps[currentStep].answer}\n            </p>\n          </details>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '30px' }}>\n          <button\n            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}\n            disabled={currentStep === 0}\n            style={{\n              backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            ← Previous\n          </button>\n          \n          <button\n            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}\n            disabled={currentStep === steps.length - 1}\n            style={{\n              backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#9b59b6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next →\n          </button>\n        </div>\n      </div>\n\n      {/* Practice Problems */}\n      <div style={{\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #9b59b6'\n      }}>\n        <h3 style={{ color: '#9b59b6', marginBottom: '20px' }}>\n          🎯 Practice Problems\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\n          {[\n            { name: \"Binary Search\", difficulty: \"Easy\", time: \"10 min\" },\n            { name: \"Search Insert Position\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Find First and Last Position\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Search in Rotated Array\", difficulty: \"Medium\", time: \"30 min\" },\n            { name: \"Find Peak Element\", difficulty: \"Medium\", time: \"20 min\" },\n            { name: \"Median of Two Sorted Arrays\", difficulty: \"Hard\", time: \"45 min\" }\n          ].map((problem, index) => (\n            <div\n              key={index}\n              style={{\n                backgroundColor: 'white',\n                padding: '15px',\n                borderRadius: '8px',\n                border: '1px solid #dee2e6'\n              }}\n            >\n              <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0' }}>{problem.name}</h4>\n              <div style={{ fontSize: '14px', color: '#666' }}>\n                <span style={{ \n                  color: problem.difficulty === 'Easy' ? '#27ae60' : \n                        problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c' \n                }}>\n                  {problem.difficulty}\n                </span>\n                {' • '}\n                <span>{problem.time}</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* MCQ Section */}\n      {patternMCQs.length > 0 && (\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          padding: '30px',\n          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n          marginBottom: '30px',\n          border: '2px solid #3498db'\n        }}>\n          <h3 style={{ color: '#3498db', marginBottom: '20px', textAlign: 'center' }}>\n            📝 Test Your Knowledge - Binary Search MCQs\n          </h3>\n\n          {/* MCQ Progress */}\n          <div style={{ marginBottom: '20px', textAlign: 'center' }}>\n            <span style={{ color: '#666', fontSize: '14px' }}>\n              Question {currentMCQIndex + 1} of {patternMCQs.length} |\n              Score: {mcqScore}/{completedMCQs.size} |\n              Accuracy: {completedMCQs.size > 0 ? Math.round((mcqScore / completedMCQs.size) * 100) : 0}%\n            </span>\n          </div>\n\n          {/* Current MCQ */}\n          <div style={{\n            backgroundColor: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            marginBottom: '20px'\n          }}>\n            <h4 style={{ color: '#2c3e50', marginBottom: '15px' }}>\n              {patternMCQs[currentMCQIndex]?.question}\n            </h4>\n\n            <div style={{ display: 'grid', gap: '10px' }}>\n              {patternMCQs[currentMCQIndex]?.options.map((option, index) => (\n                <button\n                  key={index}\n                  onClick={() => !showMCQResult && handleMCQAnswer(option)}\n                  disabled={showMCQResult}\n                  style={{\n                    padding: '12px 16px',\n                    border: '2px solid',\n                    borderColor: showMCQResult\n                      ? (option === patternMCQs[currentMCQIndex].answer ? '#27ae60'\n                         : option === selectedAnswer ? '#e74c3c' : '#ddd')\n                      : (selectedAnswer === option ? '#3498db' : '#ddd'),\n                    borderRadius: '8px',\n                    backgroundColor: showMCQResult\n                      ? (option === patternMCQs[currentMCQIndex].answer ? '#d5f4e6'\n                         : option === selectedAnswer ? '#fadbd8' : 'white')\n                      : (selectedAnswer === option ? '#ebf3fd' : 'white'),\n                    color: '#2c3e50',\n                    cursor: showMCQResult ? 'default' : 'pointer',\n                    textAlign: 'left',\n                    transition: 'all 0.2s ease'\n                  }}\n                >\n                  {String.fromCharCode(65 + index)}. {option}\n                  {showMCQResult && option === patternMCQs[currentMCQIndex].answer && ' ✅'}\n                  {showMCQResult && option === selectedAnswer && option !== patternMCQs[currentMCQIndex].answer && ' ❌'}\n                </button>\n              ))}\n            </div>\n\n            {showMCQResult && (\n              <div style={{\n                marginTop: '15px',\n                padding: '15px',\n                backgroundColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#d5f4e6' : '#fadbd8',\n                borderRadius: '8px',\n                border: '1px solid',\n                borderColor: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n              }}>\n                <strong style={{\n                  color: selectedAnswer === patternMCQs[currentMCQIndex].answer ? '#27ae60' : '#e74c3c'\n                }}>\n                  {selectedAnswer === patternMCQs[currentMCQIndex].answer ? '🎉 Correct!' : '❌ Incorrect'}\n                </strong>\n                <p style={{ margin: '5px 0 0 0', color: '#2c3e50' }}>\n                  <strong>Correct Answer:</strong> {patternMCQs[currentMCQIndex].answer}\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* MCQ Navigation */}\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <button\n              onClick={previousMCQ}\n              disabled={currentMCQIndex === 0}\n              style={{\n                backgroundColor: currentMCQIndex === 0 ? '#bdc3c7' : '#95a5a6',\n                color: 'white',\n                border: 'none',\n                padding: '10px 20px',\n                borderRadius: '6px',\n                cursor: currentMCQIndex === 0 ? 'not-allowed' : 'pointer'\n              }}\n            >\n              ← Previous MCQ\n            </button>\n\n            <div style={{ textAlign: 'center' }}>\n              <div style={{\n                display: 'flex',\n                gap: '5px',\n                justifyContent: 'center'\n              }}>\n                {patternMCQs.map((_, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '12px',\n                      height: '12px',\n                      borderRadius: '50%',\n                      backgroundColor:\n                        index === currentMCQIndex ? '#3498db' :\n                        completedMCQs.has(index) ? '#27ae60' : '#ddd'\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n\n            <button\n              onClick={nextMCQ}\n              disabled={currentMCQIndex === patternMCQs.length - 1}\n              style={{\n                backgroundColor: currentMCQIndex === patternMCQs.length - 1 ? '#bdc3c7' : '#3498db',\n                color: 'white',\n                border: 'none',\n                padding: '10px 20px',\n                borderRadius: '6px',\n                cursor: currentMCQIndex === patternMCQs.length - 1 ? 'not-allowed' : 'pointer'\n              }}\n            >\n              Next MCQ →\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Follow-up Prompts Section */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        border: '2px solid #f39c12'\n      }}>\n        <h3 style={{ color: '#f39c12', marginBottom: '20px', textAlign: 'center' }}>\n          💭 Follow-up Learning Prompts\n        </h3>\n        <p style={{ color: '#666', textAlign: 'center', marginBottom: '25px' }}>\n          Deepen your understanding by answering these thought-provoking questions\n        </p>\n\n        <div style={{ display: 'grid', gap: '20px' }}>\n          {followUpPrompts.map((prompt, index) => (\n            <div key={index} style={{\n              backgroundColor: '#fef9e7',\n              padding: '20px',\n              borderRadius: '8px',\n              border: '1px solid #f1c40f'\n            }}>\n              <h4 style={{ color: '#f39c12', marginBottom: '10px' }}>\n                {index + 1}. {prompt}\n              </h4>\n              <textarea\n                value={followUpAnswers[prompt] || ''}\n                onChange={(e) => handleFollowUpAnswer(prompt, e.target.value)}\n                placeholder=\"Type your answer here...\"\n                style={{\n                  width: '100%',\n                  minHeight: '80px',\n                  padding: '12px',\n                  border: '1px solid #ddd',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  fontFamily: 'inherit',\n                  resize: 'vertical'\n                }}\n              />\n            </div>\n          ))}\n        </div>\n\n        <div style={{ textAlign: 'center', marginTop: '25px' }}>\n          <div style={{\n            backgroundColor: '#e8f5e8',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #27ae60'\n          }}>\n            <strong style={{ color: '#27ae60' }}>\n              📊 Progress: {Object.keys(followUpAnswers).length}/{followUpPrompts.length} prompts answered\n            </strong>\n            <p style={{ margin: '5px 0 0 0', color: '#666', fontSize: '14px' }}>\n              Complete all prompts to master the Binary Search pattern!\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default BinarySearchPattern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAC/E,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACc,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACkB,GAAG,EAAEC,MAAM,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACjC,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAIoC,GAAG,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAMuC,WAAW,GAAGtC,aAAa,CAAC,kBAAkB,CAAC,IAAI,EAAE;EAE3D,MAAMuC,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,wCAAwC;IAC/CC,OAAO,EAAE,gJAAgJ;IACzJC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,gDAAgD;IAChEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,4CAA4C;IACnDC,OAAO,EAAE,kHAAkH;IAC3HC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,mEAAmE;IACnFC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,0EAA0E;IACnFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,6CAA6C;IAC7DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,sFAAsF;IAC/FC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,gFAAgF;IACzFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA,WAAW7B,IAAI,YAAYJ,SAAS,CAACI,IAAI,CAAC;AAC1C,YAAYE,KAAK,YAAYN,SAAS,CAACM,KAAK,CAAC;AAC7C,UAAUE,GAAG,YAAYR,SAAS,CAACQ,GAAG,CAAC;AACvC,iBAAiBR,SAAS,CAACQ,GAAG,CAAC,OAAON,MAAM,EAAE;IACxCgC,cAAc,EAAE,iEAAiE;IACjFC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,4CAA4C;IACnDC,OAAO,EAAE,wGAAwG;IACjHC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+DAA+D;IAC/EC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1BzB,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,IAAI,CAAC;IACfE,gBAAgB,CAAC,EAAE,CAAC;IAEpB,IAAIsB,CAAC,GAAG,CAAC;IACT,IAAIC,CAAC,GAAGtC,SAAS,CAACuC,MAAM,GAAG,CAAC;IAC5B,IAAIC,OAAO,GAAG,EAAE;IAEhB,OAAOH,CAAC,IAAIC,CAAC,EAAE;MACb,IAAIG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACN,CAAC,GAAGC,CAAC,IAAI,CAAC,CAAC;MAC/BjC,OAAO,CAACgC,CAAC,CAAC;MACV9B,QAAQ,CAAC+B,CAAC,CAAC;MACX7B,MAAM,CAACgC,CAAC,CAAC;MAET,MAAMG,IAAI,GAAG;QACXxC,IAAI,EAAEiC,CAAC;QACP/B,KAAK,EAAEgC,CAAC;QACR9B,GAAG,EAAEiC,CAAC;QACNI,QAAQ,EAAE7C,SAAS,CAACyC,CAAC,CAAC;QACtBK,UAAU,EAAE9C,SAAS,CAACyC,CAAC,CAAC,KAAKvC,MAAM,GAAG,OAAO,GAClCF,SAAS,CAACyC,CAAC,CAAC,GAAGvC,MAAM,GAAG,MAAM,GAAG;MAC9C,CAAC;MAEDsC,OAAO,CAACO,IAAI,CAACH,IAAI,CAAC;MAClB7B,gBAAgB,CAAC,CAAC,GAAGyB,OAAO,CAAC,CAAC;MAE9B,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,IAAIjD,SAAS,CAACyC,CAAC,CAAC,KAAKvC,MAAM,EAAE;QAC3BW,SAAS,CAAC,SAASX,MAAM,aAAauC,CAAC,GAAG,CAAC;QAC3C9B,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIX,SAAS,CAACyC,CAAC,CAAC,GAAGvC,MAAM,EAAE;QAChCmC,CAAC,GAAGI,CAAC,GAAG,CAAC;MACX,CAAC,MAAM;QACLH,CAAC,GAAGG,CAAC,GAAG,CAAC;MACX;IACF;IAEA5B,SAAS,CAAC,GAAGX,MAAM,qBAAqB,CAAC;IACzCS,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMwC,SAAS,GAAGA,CAAA,KAAM;IACtB9C,OAAO,CAAC,CAAC,CAAC;IACVE,QAAQ,CAACP,SAAS,CAACuC,MAAM,GAAG,CAAC,CAAC;IAC9B9B,MAAM,CAACiC,IAAI,CAACC,KAAK,CAAC,CAAC3C,SAAS,CAACuC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9C1B,SAAS,CAAC,IAAI,CAAC;IACfE,gBAAgB,CAAC,EAAE,CAAC;IACpBJ,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMyC,eAAe,GAAIjB,MAAM,IAAK;IAClChB,iBAAiB,CAACgB,MAAM,CAAC;IACzBd,gBAAgB,CAAC,IAAI,CAAC;IAEtB,MAAMgC,UAAU,GAAGxB,WAAW,CAACb,eAAe,CAAC;IAC/C,IAAImB,MAAM,KAAKkB,UAAU,CAAClB,MAAM,EAAE;MAChCZ,WAAW,CAAC+B,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B;IACA7B,gBAAgB,CAAC6B,IAAI,IAAI,IAAI5B,GAAG,CAAC,CAAC,GAAG4B,IAAI,EAAEtC,eAAe,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMuC,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIvC,eAAe,GAAGa,WAAW,CAACU,MAAM,GAAG,CAAC,EAAE;MAC5CtB,kBAAkB,CAACqC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACpCnC,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIxC,eAAe,GAAG,CAAC,EAAE;MACvBC,kBAAkB,CAACqC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACpCnC,iBAAiB,CAAC,EAAE,CAAC;MACrBE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMoC,oBAAoB,GAAGA,CAACC,MAAM,EAAEvB,MAAM,KAAK;IAC/CP,kBAAkB,CAAC0B,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACI,MAAM,GAAGvB;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMwB,eAAe,GAAG,CACtB,oGAAoG,EACpG,iGAAiG,EACjG,wFAAwF,EACxF,6EAA6E,EAC7E,kGAAkG,CACnG;EAED,oBACElE,OAAA;IAAKmE,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAEvEtE,OAAA;MAAKoE,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxDtE,OAAA;QAAIoE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEF,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9E,OAAA;QAAGoE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEK,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAExF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN9E,OAAA;MAAKoE,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnCtE,OAAA;QAAKoE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACAtE,OAAA;UAAKoE,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,MAAM;YACdE,KAAK,EAAE,GAAI,CAAChF,WAAW,GAAG,CAAC,IAAIgC,KAAK,CAACS,MAAM,GAAI,GAAG,GAAG;YACrDwC,UAAU,EAAE;UACd;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN9E,OAAA;QAAGoE,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAES,MAAM,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAH,QAAA,GAAC,OAChE,EAACjE,WAAW,GAAG,CAAC,EAAC,MAAI,EAACgC,KAAK,CAACS,MAAM;MAAA;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN9E,OAAA;MAAKoE,KAAK,EAAE;QACVmB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXjB,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE;MACZ,CAAE;MAAApB,QAAA,EACCjC,KAAK,CAACsD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAClB7F,OAAA;QAEE8F,OAAO,EAAEA,CAAA,KAAMxF,cAAc,CAACuF,KAAK,CAAE;QACrCzB,KAAK,EAAE;UACL2B,OAAO,EAAE,UAAU;UACnBC,MAAM,EAAE3F,WAAW,KAAKwF,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;UACtEX,YAAY,EAAE,MAAM;UACpBD,eAAe,EAAE5E,WAAW,KAAKwF,KAAK,GAAG,SAAS,GAAG,OAAO;UAC5DpB,KAAK,EAAEpE,WAAW,KAAKwF,KAAK,GAAG,OAAO,GAAG,MAAM;UAC/CI,MAAM,EAAE,SAAS;UACjBvB,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EAEDuB,KAAK,GAAG;MAAC,GAZLA,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9E,OAAA;MAAKoE,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAtE,OAAA;QAAIoE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDjC,KAAK,CAAChC,WAAW,CAAC,CAACiC;MAAK;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAEL9E,OAAA;QAAGoE,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAE0B,UAAU,EAAE,KAAK;UAAE3B,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClEjC,KAAK,CAAChC,WAAW,CAAC,CAACkC;MAAO;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAGJ9E,OAAA;QAAKoE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE,MAAM;UACpBH,UAAU,EAAE,6BAA6B;UACzCK,QAAQ,EAAE,MAAM;UAChBU,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACAtE,OAAA;UAAKoE,KAAK,EAAE;YAAEY,MAAM,EAAE,CAAC;YAAEoB,UAAU,EAAE;UAAW,CAAE;UAAA9B,QAAA,EAC/CjC,KAAK,CAAChC,WAAW,CAAC,CAACmC;QAAI;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzE,WAAW,KAAK,CAAC,iBAChBL,OAAA;QAAKoE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,MAAM;UACpBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACAtE,OAAA;UAAIoE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAA2B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGvF9E,OAAA;UAAKoE,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCtE,OAAA;YAAKoE,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,KAAK;cAAED,cAAc,EAAE,QAAQ;cAAEhB,YAAY,EAAE,MAAM;cAAEkB,QAAQ,EAAE;YAAO,CAAE;YAAApB,QAAA,EAC3G/D,SAAS,CAACoF,GAAG,CAAC,CAACU,GAAG,EAAER,KAAK,kBACxB7F,OAAA;cAEEoE,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdI,OAAO,EAAE,MAAM;gBACfe,UAAU,EAAE,QAAQ;gBACpBd,cAAc,EAAE,QAAQ;gBACxBP,eAAe,EACbY,KAAK,KAAK9E,GAAG,GAAG,SAAS,GACzB8E,KAAK,KAAKlF,IAAI,GAAG,SAAS,GAC1BkF,KAAK,KAAKhF,KAAK,GAAG,SAAS,GAC3BgF,KAAK,IAAIlF,IAAI,IAAIkF,KAAK,IAAIhF,KAAK,GAAG,SAAS,GAAG,SAAS;gBACzD4D,KAAK,EAAGoB,KAAK,IAAIlF,IAAI,IAAIkF,KAAK,IAAIhF,KAAK,GAAI,OAAO,GAAG,MAAM;gBAC3DqE,YAAY,EAAE,KAAK;gBACnBqB,UAAU,EAAE,MAAM;gBAClB7B,QAAQ,EAAE,MAAM;gBAChBsB,MAAM,EAAE,WAAW;gBACnBQ,WAAW,EACTX,KAAK,KAAK9E,GAAG,GAAG,SAAS,GACzB8E,KAAK,KAAKlF,IAAI,GAAG,SAAS,GAC1BkF,KAAK,KAAKhF,KAAK,GAAG,SAAS,GAC3BgF,KAAK,IAAIlF,IAAI,IAAIkF,KAAK,IAAIhF,KAAK,GAAG,SAAS,GAAG,SAAS;gBACzD4F,QAAQ,EAAE;cACZ,CAAE;cAAAnC,QAAA,GAED+B,GAAG,EACHR,KAAK,KAAKlF,IAAI,iBACbX,OAAA;gBAAKoE,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAe,KAAK,KAAK9E,GAAG,iBACZf,OAAA;gBAAKoE,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAe,KAAK,KAAKhF,KAAK,iBACdb,OAAA;gBAAKoE,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GA1DIe,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2DP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9E,OAAA;YAAKoE,KAAK,EAAE;cAAEG,SAAS,EAAE,QAAQ;cAAEG,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE,MAAM;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,gBACzFtE,OAAA;cAAMoE,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,qBAAS,EAAC3D,IAAI;YAAA;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,KAAK,eACN9E,OAAA;cAAMoE,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,oBAAQ,EAACvD,GAAG,EAAC,WAAS,EAACR,SAAS,CAACQ,GAAG,CAAC,EAAC,GAAC;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAChF,KAAK,eACN9E,OAAA;cAAMoE,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,sBAAU,EAACzD,KAAK;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1D,KAAK,eACN9E,OAAA;cAAAsE,QAAA,GAAM,UAAQ,EAAC7D,MAAM;YAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,EAGLzD,aAAa,CAACyB,MAAM,GAAG,CAAC,iBACvB9C,OAAA;YAAKoE,KAAK,EAAE;cAAEI,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,gBACnCtE,OAAA;cAAIoE,KAAK,EAAE;gBAAEK,KAAK,EAAE,SAAS;gBAAEO,MAAM,EAAE,YAAY;gBAAEN,QAAQ,EAAE;cAAO,CAAE;cAAAJ,QAAA,EAAC;YAEzE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9E,OAAA;cAAKoE,KAAK,EAAE;gBAAEuC,SAAS,EAAE,OAAO;gBAAEC,SAAS,EAAE,MAAM;gBAAElC,QAAQ,EAAE;cAAO,CAAE;cAAAJ,QAAA,EACrEjD,aAAa,CAACsE,GAAG,CAAC,CAACxC,IAAI,EAAE0C,KAAK,kBAC7B7F,OAAA;gBAAiBoE,KAAK,EAAE;kBACtB2B,OAAO,EAAE,KAAK;kBACdd,eAAe,EAAE,SAAS;kBAC1BD,MAAM,EAAE,OAAO;kBACfE,YAAY,EAAE,KAAK;kBACnBc,MAAM,EAAE;gBACV,CAAE;gBAAA1B,QAAA,GAAC,OACI,EAACuB,KAAK,GAAG,CAAC,EAAC,MAAI,EAAC1C,IAAI,CAACxC,IAAI,EAAC,MAAI,EAACwC,IAAI,CAACpC,GAAG,EAAC,GAAC,EAACoC,IAAI,CAACC,QAAQ,EAAC,OAAK,EAACD,IAAI,CAACtC,KAAK,EAAC,SAC9E,EAACsC,IAAI,CAACE,UAAU,KAAK,OAAO,GAAG,SAAS,GACvCF,IAAI,CAACE,UAAU,KAAK,MAAM,GAAG,eAAe,GAAG,cAAc;cAAA,GATtDwC,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9E,OAAA;UAAKoE,KAAK,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEkB,GAAG,EAAE,MAAM;YAAEF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBAC1FtE,OAAA;YACE8F,OAAO,EAAEnD,OAAQ;YACjBkE,QAAQ,EAAE5F,SAAU;YACpBmD,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAEhF,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7C6F,OAAO,EAAE7F,SAAS,GAAG,GAAG,GAAG;YAC7B,CAAE;YAAAqD,QAAA,EAEDrD,SAAS,GAAG,iBAAiB,GAAG;UAAa;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eAET9E,OAAA;YACE8F,OAAO,EAAEpC,SAAU;YACnBU,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL3D,MAAM,iBACLnB,OAAA;UAAKoE,KAAK,EAAE;YACV2C,SAAS,EAAE,MAAM;YACjBhB,OAAO,EAAE,MAAM;YACfd,eAAe,EAAE9D,MAAM,CAAC6F,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS;YACjEhB,MAAM,EAAE,aAAa7E,MAAM,CAAC6F,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE;YACvE9B,YAAY,EAAE,KAAK;YACnBX,SAAS,EAAE,QAAQ;YACnBgC,UAAU,EAAE,MAAM;YAClB9B,KAAK,EAAEtD,MAAM,CAAC6F,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG;UAChD,CAAE;UAAA1C,QAAA,EACCnD;QAAM;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGD9E,OAAA;QAAKoE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACAtE,OAAA;UAAIoE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE;UAAa,CAAE;UAAAV,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9E,OAAA;UAAGoE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE,YAAY;YAAEiC,SAAS,EAAE;UAAS,CAAE;UAAA3C,QAAA,EACvEjC,KAAK,CAAChC,WAAW,CAAC,CAACoC;QAAc;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJ9E,OAAA;UAAAsE,QAAA,gBACEtE,OAAA;YAASoE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEwB,MAAM,EAAE,SAAS;cAAEM,UAAU,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAAC;UAE7E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACV9E,OAAA;YAAGoE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE,YAAY;cAAEkC,WAAW,EAAE;YAAO,CAAE;YAAA5C,QAAA,EACvEjC,KAAK,CAAChC,WAAW,CAAC,CAACqC;UAAM;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGN9E,OAAA;QAAKoE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEuB,SAAS,EAAE;QAAO,CAAE;QAAAzC,QAAA,gBAClFtE,OAAA;UACE8F,OAAO,EAAEA,CAAA,KAAMxF,cAAc,CAAC2C,IAAI,CAACkE,GAAG,CAAC,CAAC,EAAE9G,WAAW,GAAG,CAAC,CAAC,CAAE;UAC5DwG,QAAQ,EAAExG,WAAW,KAAK,CAAE;UAC5B+D,KAAK,EAAE;YACLa,eAAe,EAAE5E,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC1DoE,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE5F,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;UAC9C,CAAE;UAAAiE,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9E,OAAA;UACE8F,OAAO,EAAEA,CAAA,KAAMxF,cAAc,CAAC2C,IAAI,CAACmE,GAAG,CAAC/E,KAAK,CAACS,MAAM,GAAG,CAAC,EAAEzC,WAAW,GAAG,CAAC,CAAC,CAAE;UAC3EwG,QAAQ,EAAExG,WAAW,KAAKgC,KAAK,CAACS,MAAM,GAAG,CAAE;UAC3CsB,KAAK,EAAE;YACLa,eAAe,EAAE5E,WAAW,KAAKgC,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACzE2B,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE5F,WAAW,KAAKgC,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UAC7D,CAAE;UAAAwB,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9E,OAAA;MAAKoE,KAAK,EAAE;QACVa,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACAtE,OAAA;QAAIoE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEvD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL9E,OAAA;QAAKoE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAE8B,mBAAmB,EAAE,sCAAsC;UAAE5B,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EACvG,CACC;UAAEgD,IAAI,EAAE,eAAe;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC7D;UAAEF,IAAI,EAAE,wBAAwB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACtE;UAAEF,IAAI,EAAE,8BAA8B;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC9E;UAAEF,IAAI,EAAE,yBAAyB;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACzE;UAAEF,IAAI,EAAE,mBAAmB;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACnE;UAAEF,IAAI,EAAE,6BAA6B;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,CAC5E,CAAC7B,GAAG,CAAC,CAAC8B,OAAO,EAAE5B,KAAK,kBACnB7F,OAAA;UAEEoE,KAAK,EAAE;YACLa,eAAe,EAAE,OAAO;YACxBc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBAEFtE,OAAA;YAAIoE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE;YAAY,CAAE;YAAAV,QAAA,EAAEmD,OAAO,CAACH;UAAI;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzE9E,OAAA;YAAKoE,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBAC9CtE,OAAA;cAAMoE,KAAK,EAAE;gBACXK,KAAK,EAAEgD,OAAO,CAACF,UAAU,KAAK,MAAM,GAAG,SAAS,GAC1CE,OAAO,CAACF,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG;cACtD,CAAE;cAAAjD,QAAA,EACCmD,OAAO,CAACF;YAAU;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACN,KAAK,eACN9E,OAAA;cAAAsE,QAAA,EAAOmD,OAAO,CAACD;YAAI;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA,GAlBDe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1C,WAAW,CAACU,MAAM,GAAG,CAAC,iBACrB9C,OAAA;MAAKoE,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE,MAAM;QACpBwB,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACAtE,OAAA;QAAIoE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAGL9E,OAAA;QAAKoE,KAAK,EAAE;UAAEI,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,eACxDtE,OAAA;UAAMoE,KAAK,EAAE;YAAEK,KAAK,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAJ,QAAA,GAAC,WACvC,EAAC/C,eAAe,GAAG,CAAC,EAAC,MAAI,EAACa,WAAW,CAACU,MAAM,EAAC,YAC/C,EAACjB,QAAQ,EAAC,GAAC,EAACE,aAAa,CAAC2F,IAAI,EAAC,eAC5B,EAAC3F,aAAa,CAAC2F,IAAI,GAAG,CAAC,GAAGzE,IAAI,CAAC0E,KAAK,CAAE9F,QAAQ,GAAGE,aAAa,CAAC2F,IAAI,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GAC5F;QAAA;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN9E,OAAA;QAAKoE,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Bc,OAAO,EAAE,MAAM;UACfb,YAAY,EAAE,KAAK;UACnBV,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACAtE,OAAA;UAAIoE,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,GAAAnE,qBAAA,GACnDiC,WAAW,CAACb,eAAe,CAAC,cAAApB,qBAAA,uBAA5BA,qBAAA,CAA8ByH;QAAQ;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAEL9E,OAAA;UAAKoE,KAAK,EAAE;YAAEmB,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAO,CAAE;UAAAnB,QAAA,GAAAlE,sBAAA,GAC1CgC,WAAW,CAACb,eAAe,CAAC,cAAAnB,sBAAA,uBAA5BA,sBAAA,CAA8ByH,OAAO,CAAClC,GAAG,CAAC,CAACmC,MAAM,EAAEjC,KAAK,kBACvD7F,OAAA;YAEE8F,OAAO,EAAEA,CAAA,KAAM,CAACnE,aAAa,IAAIgC,eAAe,CAACmE,MAAM,CAAE;YACzDjB,QAAQ,EAAElF,aAAc;YACxByC,KAAK,EAAE;cACL2B,OAAO,EAAE,WAAW;cACpBC,MAAM,EAAE,WAAW;cACnBQ,WAAW,EAAE7E,aAAa,GACrBmG,MAAM,KAAK1F,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GACxDoF,MAAM,KAAKrG,cAAc,GAAG,SAAS,GAAG,MAAM,GAChDA,cAAc,KAAKqG,MAAM,GAAG,SAAS,GAAG,MAAO;cACpD5C,YAAY,EAAE,KAAK;cACnBD,eAAe,EAAEtD,aAAa,GACzBmG,MAAM,KAAK1F,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GACxDoF,MAAM,KAAKrG,cAAc,GAAG,SAAS,GAAG,OAAO,GACjDA,cAAc,KAAKqG,MAAM,GAAG,SAAS,GAAG,OAAQ;cACrDrD,KAAK,EAAE,SAAS;cAChBwB,MAAM,EAAEtE,aAAa,GAAG,SAAS,GAAG,SAAS;cAC7C4C,SAAS,EAAE,MAAM;cACjBe,UAAU,EAAE;YACd,CAAE;YAAAhB,QAAA,GAEDyD,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGnC,KAAK,CAAC,EAAC,IAAE,EAACiC,MAAM,EACzCnG,aAAa,IAAImG,MAAM,KAAK1F,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,IAAI,IAAI,EACvEf,aAAa,IAAImG,MAAM,KAAKrG,cAAc,IAAIqG,MAAM,KAAK1F,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,IAAI,IAAI;UAAA,GAvBhGmD,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELnD,aAAa,iBACZ3B,OAAA;UAAKoE,KAAK,EAAE;YACV2C,SAAS,EAAE,MAAM;YACjBhB,OAAO,EAAE,MAAM;YACfd,eAAe,EAAExD,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG,SAAS;YAC/FwC,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE,WAAW;YACnBQ,WAAW,EAAE/E,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG;UACpF,CAAE;UAAA4B,QAAA,gBACAtE,OAAA;YAAQoE,KAAK,EAAE;cACbK,KAAK,EAAEhD,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,SAAS,GAAG;YAC9E,CAAE;YAAA4B,QAAA,EACC7C,cAAc,KAAKW,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM,GAAG,aAAa,GAAG;UAAa;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACT9E,OAAA;YAAGoE,KAAK,EAAE;cAAEY,MAAM,EAAE,WAAW;cAAEP,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,gBAClDtE,OAAA;cAAAsE,QAAA,EAAQ;YAAe;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC1C,WAAW,CAACb,eAAe,CAAC,CAACmB,MAAM;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN9E,OAAA;QAAKoE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEc,UAAU,EAAE;QAAS,CAAE;QAAAhC,QAAA,gBACrFtE,OAAA;UACE8F,OAAO,EAAE/B,WAAY;UACrB8C,QAAQ,EAAEtF,eAAe,KAAK,CAAE;UAChC6C,KAAK,EAAE;YACLa,eAAe,EAAE1D,eAAe,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC9DkD,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE1E,eAAe,KAAK,CAAC,GAAG,aAAa,GAAG;UAClD,CAAE;UAAA+C,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9E,OAAA;UAAKoE,KAAK,EAAE;YAAEG,SAAS,EAAE;UAAS,CAAE;UAAAD,QAAA,eAClCtE,OAAA;YAAKoE,KAAK,EAAE;cACVmB,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,KAAK;cACVD,cAAc,EAAE;YAClB,CAAE;YAAAlB,QAAA,EACClC,WAAW,CAACuD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBACxB7F,OAAA;cAEEoE,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdD,YAAY,EAAE,KAAK;gBACnBD,eAAe,EACbY,KAAK,KAAKtE,eAAe,GAAG,SAAS,GACrCQ,aAAa,CAACkG,GAAG,CAACpC,KAAK,CAAC,GAAG,SAAS,GAAG;cAC3C;YAAE,GARGA,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UACE8F,OAAO,EAAEhC,OAAQ;UACjB+C,QAAQ,EAAEtF,eAAe,KAAKa,WAAW,CAACU,MAAM,GAAG,CAAE;UACrDsB,KAAK,EAAE;YACLa,eAAe,EAAE1D,eAAe,KAAKa,WAAW,CAACU,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACnF2B,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE1E,eAAe,KAAKa,WAAW,CAACU,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UACvE,CAAE;UAAAwB,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD9E,OAAA;MAAKoE,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzCF,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACAtE,OAAA;QAAIoE,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,EAAC;MAE5E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9E,OAAA;QAAGoE,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAEF,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAExE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJ9E,OAAA;QAAKoE,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EAC1CJ,eAAe,CAACyB,GAAG,CAAC,CAAC1B,MAAM,EAAE4B,KAAK,kBACjC7F,OAAA;UAAiBoE,KAAK,EAAE;YACtBa,eAAe,EAAE,SAAS;YAC1Bc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBACAtE,OAAA;YAAIoE,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,GACnDuB,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC5B,MAAM;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACL9E,OAAA;YACEkI,KAAK,EAAEhG,eAAe,CAAC+B,MAAM,CAAC,IAAI,EAAG;YACrCkE,QAAQ,EAAGC,CAAC,IAAKpE,oBAAoB,CAACC,MAAM,EAAEmE,CAAC,CAAC3H,MAAM,CAACyH,KAAK,CAAE;YAC9DG,WAAW,EAAC,0BAA0B;YACtCjE,KAAK,EAAE;cACLiB,KAAK,EAAE,MAAM;cACbiD,SAAS,EAAE,MAAM;cACjBvC,OAAO,EAAE,MAAM;cACfC,MAAM,EAAE,gBAAgB;cACxBd,YAAY,EAAE,KAAK;cACnBR,QAAQ,EAAE,MAAM;cAChBL,UAAU,EAAE,SAAS;cACrBkE,MAAM,EAAE;YACV;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAvBMe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN9E,OAAA;QAAKoE,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAEwC,SAAS,EAAE;QAAO,CAAE;QAAAzC,QAAA,eACrDtE,OAAA;UAAKoE,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1Bc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBACAtE,OAAA;YAAQoE,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,GAAC,yBACtB,EAACkE,MAAM,CAACC,IAAI,CAACvG,eAAe,CAAC,CAACY,MAAM,EAAC,GAAC,EAACoB,eAAe,CAACpB,MAAM,EAAC,mBAC7E;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9E,OAAA;YAAGoE,KAAK,EAAE;cAAEY,MAAM,EAAE,WAAW;cAAEP,KAAK,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEpE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5E,EAAA,CAz8BQD,mBAAmB;AAAAyI,EAAA,GAAnBzI,mBAAmB;AA28B5B,eAAeA,mBAAmB;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}