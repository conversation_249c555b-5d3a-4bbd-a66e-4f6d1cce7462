* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

body {
  font-family: sans-serif;
  background-color: #f0f0f0;
}

#root {
  min-height: 100vh;
}

/* Navigation Styles */
nav {
  background-color: #2c3e50;
  padding: 15px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  gap: 30px;
}

nav li {
  margin: 0;
}

nav a {
  color: #ecf0f1;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  padding: 10px 20px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

nav a:hover {
  background-color: #34495e;
  color: #3498db;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App > div:not(nav) {
  flex: 1;
  padding: 0;
}

/* Main content area */
.main-content {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Ensure proper spacing for all page content */
.page-content {
  padding: 20px;
  margin: 0 auto;
  max-width: 1200px;
}

.question-container {
  width: 90%;
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.options-container {
  margin-top: 20px;
}

.option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
}

.prompts-container {
  margin-top: 30px;
}

.prompt {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
}

.navigation-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.generate-report-button {
  width: 100%;
  margin-top: 20px;
  background-color: #28a745;
}
