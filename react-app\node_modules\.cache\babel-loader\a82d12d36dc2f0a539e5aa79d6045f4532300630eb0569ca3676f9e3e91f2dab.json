{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\patterns\\\\TwoPointersPattern.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TwoPointersPattern() {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 2, 3, 4, 5, 6, 7, 8, 9]);\n  const [target, setTarget] = useState(10);\n  const [leftPointer, setLeftPointer] = useState(0);\n  const [rightPointer, setRightPointer] = useState(8);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n  const steps = [{\n    title: \"🎯 Understanding Two Pointers Pattern\",\n    content: \"The Two Pointers technique uses two pointers to traverse data structures, typically arrays or strings, to solve problems efficiently.\",\n    code: `// Two Pointers Template\nfunction twoPointers(arr) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left < right) {\n        // Process current pair\n        if (condition) {\n            // Found solution\n            return [left, right];\n        } else if (needToMoveLeft) {\n            left++;\n        } else {\n            right--;\n        }\n    }\n    return null;\n}`,\n    learningPrompt: \"Why use two pointers instead of nested loops?\",\n    answer: \"Two pointers reduce time complexity from O(n²) to O(n) by eliminating the need for nested iterations.\"\n  }, {\n    title: \"📋 Step 1: Problem Analysis\",\n    content: \"Identify if the problem can use two pointers: sorted array, finding pairs, palindrome check, or removing duplicates.\",\n    code: `// Two Sum in Sorted Array\nfunction twoSum(numbers, target) {\n    let left = 0;\n    let right = numbers.length - 1;\n    \n    while (left < right) {\n        const sum = numbers[left] + numbers[right];\n        \n        if (sum === target) {\n            return [left, right];\n        } else if (sum < target) {\n            left++;  // Need larger sum\n        } else {\n            right--; // Need smaller sum\n        }\n    }\n    return [];\n}`,\n    learningPrompt: \"When should we move the left pointer vs right pointer?\",\n    answer: \"Move left when we need a larger value, move right when we need a smaller value (in sorted arrays).\"\n  }, {\n    title: \"🔧 Step 2: Initialize Pointers\",\n    content: \"Set up pointers at appropriate positions - usually start and end for opposite direction movement.\",\n    code: `// Initialization patterns\n// Pattern 1: Opposite ends\nlet left = 0, right = arr.length - 1;\n\n// Pattern 2: Same start (fast/slow)\nlet slow = 0, fast = 0;\n\n// Pattern 3: Sliding window\nlet left = 0, right = 0;`,\n    learningPrompt: \"What are the different ways to initialize two pointers?\",\n    answer: \"1) Opposite ends (left=0, right=n-1), 2) Same start (slow=0, fast=0), 3) Sliding window (both start at 0)\"\n  }, {\n    title: \"⚡ Step 3: Movement Logic\",\n    content: \"Define the conditions for moving each pointer based on the problem requirements.\",\n    code: `// Movement strategies\nwhile (left < right) {\n    if (condition_met) {\n        return result;\n    }\n    \n    // Strategy 1: Based on comparison\n    if (sum < target) left++;\n    else right--;\n    \n    // Strategy 2: Always move one\n    if (arr[left] === arr[right]) {\n        left++; right--;\n    }\n    \n    // Strategy 3: Conditional movement\n    if (isValid(left, right)) {\n        process(left, right);\n    }\n    movePointers();\n}`,\n    learningPrompt: \"How do we decide which pointer to move?\",\n    answer: \"Base the decision on problem logic: comparison results, validity checks, or alternating movement patterns.\"\n  }, {\n    title: \"🎮 Step 4: Interactive Demo\",\n    content: \"Watch the two pointers algorithm in action with a live demonstration.\",\n    code: `// Live Demo: Two Sum\nconst numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9];\nconst target = 10;\n\n// Current state:\n// Left pointer: ${leftPointer} (value: ${demoArray[leftPointer]})\n// Right pointer: ${rightPointer} (value: ${demoArray[rightPointer]})\n// Sum: ${demoArray[leftPointer] + demoArray[rightPointer]}`,\n    learningPrompt: \"What happens when sum equals target?\",\n    answer: \"We found our answer! Return the indices or values of the two pointers.\"\n  }, {\n    title: \"🏆 Step 5: Common Variations\",\n    content: \"Master different two-pointer patterns for various problem types.\",\n    code: `// Variation 1: Remove Duplicates\nfunction removeDuplicates(arr) {\n    let writeIndex = 1;\n    for (let readIndex = 1; readIndex < arr.length; readIndex++) {\n        if (arr[readIndex] !== arr[readIndex - 1]) {\n            arr[writeIndex] = arr[readIndex];\n            writeIndex++;\n        }\n    }\n    return writeIndex;\n}\n\n// Variation 2: Palindrome Check\nfunction isPalindrome(s) {\n    let left = 0, right = s.length - 1;\n    while (left < right) {\n        if (s[left] !== s[right]) return false;\n        left++; right--;\n    }\n    return true;\n}`,\n    learningPrompt: \"What are the main variations of two pointers?\",\n    answer: \"1) Opposite direction (palindrome), 2) Same direction (remove duplicates), 3) Fast/slow (cycle detection)\"\n  }];\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    let left = 0;\n    let right = demoArray.length - 1;\n    while (left < right) {\n      setLeftPointer(left);\n      setRightPointer(right);\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      const sum = demoArray[left] + demoArray[right];\n      if (sum === target) {\n        setResult(`Found! ${demoArray[left]} + ${demoArray[right]} = ${target}`);\n        setIsRunning(false);\n        return;\n      } else if (sum < target) {\n        left++;\n      } else {\n        right--;\n      }\n    }\n    setResult(\"No solution found\");\n    setIsRunning(false);\n  };\n  const resetDemo = () => {\n    setLeftPointer(0);\n    setRightPointer(demoArray.length - 1);\n    setResult(null);\n    setIsRunning(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    style: {\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#27ae60',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83C\\uDFAF Two Pointers Pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em',\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: \"Master the two pointers technique to solve array and string problems efficiently\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#27ae60',\n            height: '100%',\n            width: `${(currentStep + 1) / steps.length * 100}%`,\n            transition: 'width 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0',\n          color: '#7f8c8d'\n        },\n        children: [\"Step \", currentStep + 1, \" of \", steps.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '10px',\n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      },\n      children: steps.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentStep(index),\n        style: {\n          padding: '8px 16px',\n          border: currentStep === index ? '2px solid #27ae60' : '1px solid #ddd',\n          borderRadius: '20px',\n          backgroundColor: currentStep === index ? '#27ae60' : 'white',\n          color: currentStep === index ? 'white' : '#333',\n          cursor: 'pointer',\n          fontSize: '14px'\n        },\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#2c3e50',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          lineHeight: '1.6',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            margin: 0,\n            whiteSpace: 'pre-wrap'\n          },\n          children: steps[currentStep].code\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f0f8ff',\n          border: '2px solid #27ae60',\n          borderRadius: '12px',\n          padding: '20px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#27ae60',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83C\\uDFAE Live Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '5px',\n              justifyContent: 'center',\n              marginBottom: '10px'\n            },\n            children: demoArray.map((num, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '40px',\n                height: '40px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: index === leftPointer ? '#e74c3c' : index === rightPointer ? '#3498db' : '#ecf0f1',\n                color: index === leftPointer || index === rightPointer ? 'white' : '#333',\n                borderRadius: '8px',\n                fontWeight: 'bold',\n                border: '2px solid',\n                borderColor: index === leftPointer ? '#c0392b' : index === rightPointer ? '#2980b9' : '#bdc3c7'\n              },\n              children: num\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#e74c3c'\n              },\n              children: [\"\\uD83D\\uDD34 Left: \", leftPointer]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#3498db'\n              },\n              children: [\"\\uD83D\\uDD35 Right: \", rightPointer]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Target: \", target]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Sum: \", demoArray[leftPointer] + demoArray[rightPointer]]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            gap: '10px',\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: runDemo,\n            disabled: isRunning,\n            style: {\n              backgroundColor: '#27ae60',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: isRunning ? 'not-allowed' : 'pointer',\n              opacity: isRunning ? 0.6 : 1\n            },\n            children: isRunning ? '🔄 Running...' : '▶️ Run Demo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetDemo,\n            style: {\n              backgroundColor: '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: \"\\uD83D\\uDD04 Reset\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), result && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            padding: '10px',\n            backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n            border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n            borderRadius: '6px',\n            textAlign: 'center',\n            fontWeight: 'bold',\n            color: result.includes('Found') ? '#155724' : '#721c24'\n          },\n          children: result\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0'\n          },\n          children: \"\\uD83E\\uDD14 Learning Prompt:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0',\n            fontStyle: 'italic'\n          },\n          children: steps[currentStep].learningPrompt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            style: {\n              color: '#856404',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCA1 Click to see answer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#856404',\n              margin: '10px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: steps[currentStep].answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginTop: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.max(0, currentStep - 1)),\n          disabled: currentStep === 0,\n          style: {\n            backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.min(steps.length - 1, currentStep + 1)),\n          disabled: currentStep === steps.length - 1,\n          style: {\n            backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#27ae60',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #27ae60'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#27ae60',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83C\\uDFAF Practice Problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '15px'\n        },\n        children: [{\n          name: \"Two Sum\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Three Sum\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Remove Duplicates\",\n          difficulty: \"Easy\",\n          time: \"10 min\"\n        }, {\n          name: \"Container With Most Water\",\n          difficulty: \"Medium\",\n          time: \"20 min\"\n        }, {\n          name: \"Valid Palindrome\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Trapping Rain Water\",\n          difficulty: \"Hard\",\n          time: \"35 min\"\n        }].map((problem, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#2c3e50',\n              margin: '0 0 8px 0'\n            },\n            children: problem.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: problem.difficulty === 'Easy' ? '#27ae60' : problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c'\n              },\n              children: problem.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), ' • ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: problem.time\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n}\n_s(TwoPointersPattern, \"1UXcOoTLjmscskeynoBxEb8NBWc=\");\n_c = TwoPointersPattern;\nexport default TwoPointersPattern;\nvar _c;\n$RefreshReg$(_c, \"TwoPointersPattern\");", "map": {"version": 3, "names": ["React", "useState", "questionsData", "jsxDEV", "_jsxDEV", "TwoPointersPattern", "_s", "currentStep", "setCurrentStep", "demoArray", "setDemoArray", "target", "<PERSON><PERSON><PERSON><PERSON>", "leftPointer", "set<PERSON><PERSON>tPoint<PERSON>", "rightPointer", "setR<PERSON>Pointer", "isRunning", "setIsRunning", "result", "setResult", "steps", "title", "content", "code", "learningPrompt", "answer", "runDemo", "left", "right", "length", "Promise", "resolve", "setTimeout", "sum", "resetDemo", "className", "style", "fontFamily", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "borderRadius", "height", "overflow", "width", "transition", "display", "justifyContent", "gap", "flexWrap", "map", "_", "index", "onClick", "padding", "border", "cursor", "boxShadow", "lineHeight", "whiteSpace", "num", "alignItems", "fontWeight", "borderColor", "disabled", "opacity", "marginTop", "includes", "fontStyle", "paddingLeft", "Math", "max", "min", "gridTemplateColumns", "name", "difficulty", "time", "problem", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/patterns/TwoPointersPattern.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\n\nfunction TwoPointersPattern() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([1, 2, 3, 4, 5, 6, 7, 8, 9]);\n  const [target, setTarget] = useState(10);\n  const [leftPointer, setLeftPointer] = useState(0);\n  const [rightPointer, setRightPointer] = useState(8);\n  const [isRunning, setIsRunning] = useState(false);\n  const [result, setResult] = useState(null);\n\n  const steps = [\n    {\n      title: \"🎯 Understanding Two Pointers Pattern\",\n      content: \"The Two Pointers technique uses two pointers to traverse data structures, typically arrays or strings, to solve problems efficiently.\",\n      code: `// Two Pointers Template\nfunction twoPointers(arr) {\n    let left = 0;\n    let right = arr.length - 1;\n    \n    while (left < right) {\n        // Process current pair\n        if (condition) {\n            // Found solution\n            return [left, right];\n        } else if (needToMoveLeft) {\n            left++;\n        } else {\n            right--;\n        }\n    }\n    return null;\n}`,\n      learningPrompt: \"Why use two pointers instead of nested loops?\",\n      answer: \"Two pointers reduce time complexity from O(n²) to O(n) by eliminating the need for nested iterations.\"\n    },\n    {\n      title: \"📋 Step 1: Problem Analysis\",\n      content: \"Identify if the problem can use two pointers: sorted array, finding pairs, palindrome check, or removing duplicates.\",\n      code: `// Two Sum in Sorted Array\nfunction twoSum(numbers, target) {\n    let left = 0;\n    let right = numbers.length - 1;\n    \n    while (left < right) {\n        const sum = numbers[left] + numbers[right];\n        \n        if (sum === target) {\n            return [left, right];\n        } else if (sum < target) {\n            left++;  // Need larger sum\n        } else {\n            right--; // Need smaller sum\n        }\n    }\n    return [];\n}`,\n      learningPrompt: \"When should we move the left pointer vs right pointer?\",\n      answer: \"Move left when we need a larger value, move right when we need a smaller value (in sorted arrays).\"\n    },\n    {\n      title: \"🔧 Step 2: Initialize Pointers\",\n      content: \"Set up pointers at appropriate positions - usually start and end for opposite direction movement.\",\n      code: `// Initialization patterns\n// Pattern 1: Opposite ends\nlet left = 0, right = arr.length - 1;\n\n// Pattern 2: Same start (fast/slow)\nlet slow = 0, fast = 0;\n\n// Pattern 3: Sliding window\nlet left = 0, right = 0;`,\n      learningPrompt: \"What are the different ways to initialize two pointers?\",\n      answer: \"1) Opposite ends (left=0, right=n-1), 2) Same start (slow=0, fast=0), 3) Sliding window (both start at 0)\"\n    },\n    {\n      title: \"⚡ Step 3: Movement Logic\",\n      content: \"Define the conditions for moving each pointer based on the problem requirements.\",\n      code: `// Movement strategies\nwhile (left < right) {\n    if (condition_met) {\n        return result;\n    }\n    \n    // Strategy 1: Based on comparison\n    if (sum < target) left++;\n    else right--;\n    \n    // Strategy 2: Always move one\n    if (arr[left] === arr[right]) {\n        left++; right--;\n    }\n    \n    // Strategy 3: Conditional movement\n    if (isValid(left, right)) {\n        process(left, right);\n    }\n    movePointers();\n}`,\n      learningPrompt: \"How do we decide which pointer to move?\",\n      answer: \"Base the decision on problem logic: comparison results, validity checks, or alternating movement patterns.\"\n    },\n    {\n      title: \"🎮 Step 4: Interactive Demo\",\n      content: \"Watch the two pointers algorithm in action with a live demonstration.\",\n      code: `// Live Demo: Two Sum\nconst numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9];\nconst target = 10;\n\n// Current state:\n// Left pointer: ${leftPointer} (value: ${demoArray[leftPointer]})\n// Right pointer: ${rightPointer} (value: ${demoArray[rightPointer]})\n// Sum: ${demoArray[leftPointer] + demoArray[rightPointer]}`,\n      learningPrompt: \"What happens when sum equals target?\",\n      answer: \"We found our answer! Return the indices or values of the two pointers.\"\n    },\n    {\n      title: \"🏆 Step 5: Common Variations\",\n      content: \"Master different two-pointer patterns for various problem types.\",\n      code: `// Variation 1: Remove Duplicates\nfunction removeDuplicates(arr) {\n    let writeIndex = 1;\n    for (let readIndex = 1; readIndex < arr.length; readIndex++) {\n        if (arr[readIndex] !== arr[readIndex - 1]) {\n            arr[writeIndex] = arr[readIndex];\n            writeIndex++;\n        }\n    }\n    return writeIndex;\n}\n\n// Variation 2: Palindrome Check\nfunction isPalindrome(s) {\n    let left = 0, right = s.length - 1;\n    while (left < right) {\n        if (s[left] !== s[right]) return false;\n        left++; right--;\n    }\n    return true;\n}`,\n      learningPrompt: \"What are the main variations of two pointers?\",\n      answer: \"1) Opposite direction (palindrome), 2) Same direction (remove duplicates), 3) Fast/slow (cycle detection)\"\n    }\n  ];\n\n  const runDemo = async () => {\n    setIsRunning(true);\n    setResult(null);\n    let left = 0;\n    let right = demoArray.length - 1;\n\n    while (left < right) {\n      setLeftPointer(left);\n      setRightPointer(right);\n      \n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const sum = demoArray[left] + demoArray[right];\n      \n      if (sum === target) {\n        setResult(`Found! ${demoArray[left]} + ${demoArray[right]} = ${target}`);\n        setIsRunning(false);\n        return;\n      } else if (sum < target) {\n        left++;\n      } else {\n        right--;\n      }\n    }\n    \n    setResult(\"No solution found\");\n    setIsRunning(false);\n  };\n\n  const resetDemo = () => {\n    setLeftPointer(0);\n    setRightPointer(demoArray.length - 1);\n    setResult(null);\n    setIsRunning(false);\n  };\n\n  return (\n    <div className=\"page-content\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Header */}\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#27ae60', fontSize: '2.5em', marginBottom: '10px' }}>\n          🎯 Two Pointers Pattern\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>\n          Master the two pointers technique to solve array and string problems efficiently\n        </p>\n      </div>\n\n      {/* Progress Bar */}\n      <div style={{ marginBottom: '30px' }}>\n        <div style={{\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        }}>\n          <div style={{\n            backgroundColor: '#27ae60',\n            height: '100%',\n            width: `${((currentStep + 1) / steps.length) * 100}%`,\n            transition: 'width 0.3s ease'\n          }}></div>\n        </div>\n        <p style={{ textAlign: 'center', margin: '10px 0', color: '#7f8c8d' }}>\n          Step {currentStep + 1} of {steps.length}\n        </p>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        gap: '10px', \n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      }}>\n        {steps.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentStep(index)}\n            style={{\n              padding: '8px 16px',\n              border: currentStep === index ? '2px solid #27ae60' : '1px solid #ddd',\n              borderRadius: '20px',\n              backgroundColor: currentStep === index ? '#27ae60' : 'white',\n              color: currentStep === index ? 'white' : '#333',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Current Step Content */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      }}>\n        <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>\n          {steps[currentStep].title}\n        </h2>\n        \n        <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>\n          {steps[currentStep].content}\n        </p>\n\n        {/* Code Block */}\n        <div style={{\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        }}>\n          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>\n            {steps[currentStep].code}\n          </pre>\n        </div>\n\n        {/* Interactive Demo for Step 4 */}\n        {currentStep === 4 && (\n          <div style={{\n            backgroundColor: '#f0f8ff',\n            border: '2px solid #27ae60',\n            borderRadius: '12px',\n            padding: '20px',\n            marginBottom: '20px'\n          }}>\n            <h3 style={{ color: '#27ae60', marginBottom: '15px' }}>🎮 Live Demo</h3>\n            \n            {/* Array Visualization */}\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', marginBottom: '10px' }}>\n                {demoArray.map((num, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '40px',\n                      height: '40px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      backgroundColor: \n                        index === leftPointer ? '#e74c3c' :\n                        index === rightPointer ? '#3498db' : '#ecf0f1',\n                      color: (index === leftPointer || index === rightPointer) ? 'white' : '#333',\n                      borderRadius: '8px',\n                      fontWeight: 'bold',\n                      border: '2px solid',\n                      borderColor: \n                        index === leftPointer ? '#c0392b' :\n                        index === rightPointer ? '#2980b9' : '#bdc3c7'\n                    }}\n                  >\n                    {num}\n                  </div>\n                ))}\n              </div>\n              \n              <div style={{ textAlign: 'center', fontSize: '14px', color: '#666' }}>\n                <span style={{ color: '#e74c3c' }}>🔴 Left: {leftPointer}</span>\n                {' | '}\n                <span style={{ color: '#3498db' }}>🔵 Right: {rightPointer}</span>\n                {' | '}\n                <span>Target: {target}</span>\n                {' | '}\n                <span>Sum: {demoArray[leftPointer] + demoArray[rightPointer]}</span>\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div style={{ textAlign: 'center', gap: '10px', display: 'flex', justifyContent: 'center' }}>\n              <button\n                onClick={runDemo}\n                disabled={isRunning}\n                style={{\n                  backgroundColor: '#27ae60',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: isRunning ? 'not-allowed' : 'pointer',\n                  opacity: isRunning ? 0.6 : 1\n                }}\n              >\n                {isRunning ? '🔄 Running...' : '▶️ Run Demo'}\n              </button>\n              \n              <button\n                onClick={resetDemo}\n                style={{\n                  backgroundColor: '#95a5a6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: 'pointer'\n                }}\n              >\n                🔄 Reset\n              </button>\n            </div>\n\n            {result && (\n              <div style={{\n                marginTop: '15px',\n                padding: '10px',\n                backgroundColor: result.includes('Found') ? '#d4edda' : '#f8d7da',\n                border: `1px solid ${result.includes('Found') ? '#c3e6cb' : '#f5c6cb'}`,\n                borderRadius: '6px',\n                textAlign: 'center',\n                fontWeight: 'bold',\n                color: result.includes('Found') ? '#155724' : '#721c24'\n              }}>\n                {result}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Learning Prompt */}\n        <div style={{\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        }}>\n          <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>\n            🤔 Learning Prompt:\n          </h4>\n          <p style={{ color: '#856404', margin: '0 0 10px 0', fontStyle: 'italic' }}>\n            {steps[currentStep].learningPrompt}\n          </p>\n          <details>\n            <summary style={{ color: '#856404', cursor: 'pointer', fontWeight: 'bold' }}>\n              💡 Click to see answer\n            </summary>\n            <p style={{ color: '#856404', margin: '10px 0 0 0', paddingLeft: '20px' }}>\n              {steps[currentStep].answer}\n            </p>\n          </details>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '30px' }}>\n          <button\n            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}\n            disabled={currentStep === 0}\n            style={{\n              backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            ← Previous\n          </button>\n          \n          <button\n            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}\n            disabled={currentStep === steps.length - 1}\n            style={{\n              backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#27ae60',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next →\n          </button>\n        </div>\n      </div>\n\n      {/* Practice Problems */}\n      <div style={{\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #27ae60'\n      }}>\n        <h3 style={{ color: '#27ae60', marginBottom: '20px' }}>\n          🎯 Practice Problems\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\n          {[\n            { name: \"Two Sum\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Three Sum\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Remove Duplicates\", difficulty: \"Easy\", time: \"10 min\" },\n            { name: \"Container With Most Water\", difficulty: \"Medium\", time: \"20 min\" },\n            { name: \"Valid Palindrome\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Trapping Rain Water\", difficulty: \"Hard\", time: \"35 min\" }\n          ].map((problem, index) => (\n            <div\n              key={index}\n              style={{\n                backgroundColor: 'white',\n                padding: '15px',\n                borderRadius: '8px',\n                border: '1px solid #dee2e6'\n              }}\n            >\n              <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0' }}>{problem.name}</h4>\n              <div style={{ fontSize: '14px', color: '#666' }}>\n                <span style={{ \n                  color: problem.difficulty === 'Easy' ? '#27ae60' : \n                        problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c' \n                }}>\n                  {problem.difficulty}\n                </span>\n                {' • '}\n                <span>{problem.time}</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default TwoPointersPattern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACvE,MAAM,CAACU,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE1C,MAAMoB,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,uCAAuC;IAC9CC,OAAO,EAAE,uIAAuI;IAChJC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,sHAAsH;IAC/HC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,wDAAwD;IACxEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,gCAAgC;IACvCC,OAAO,EAAE,mGAAmG;IAC5GC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;IACnBC,cAAc,EAAE,yDAAyD;IACzEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,0BAA0B;IACjCC,OAAO,EAAE,kFAAkF;IAC3FC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,yCAAyC;IACzDC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,uEAAuE;IAChFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA,mBAAmBX,WAAW,YAAYJ,SAAS,CAACI,WAAW,CAAC;AAChE,oBAAoBE,YAAY,YAAYN,SAAS,CAACM,YAAY,CAAC;AACnE,UAAUN,SAAS,CAACI,WAAW,CAAC,GAAGJ,SAAS,CAACM,YAAY,CAAC,EAAE;IACtDU,cAAc,EAAE,sCAAsC;IACtDC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE,kEAAkE;IAC3EC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1BT,YAAY,CAAC,IAAI,CAAC;IAClBE,SAAS,CAAC,IAAI,CAAC;IACf,IAAIQ,IAAI,GAAG,CAAC;IACZ,IAAIC,KAAK,GAAGpB,SAAS,CAACqB,MAAM,GAAG,CAAC;IAEhC,OAAOF,IAAI,GAAGC,KAAK,EAAE;MACnBf,cAAc,CAACc,IAAI,CAAC;MACpBZ,eAAe,CAACa,KAAK,CAAC;MAEtB,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,GAAG,GAAGzB,SAAS,CAACmB,IAAI,CAAC,GAAGnB,SAAS,CAACoB,KAAK,CAAC;MAE9C,IAAIK,GAAG,KAAKvB,MAAM,EAAE;QAClBS,SAAS,CAAC,UAAUX,SAAS,CAACmB,IAAI,CAAC,MAAMnB,SAAS,CAACoB,KAAK,CAAC,MAAMlB,MAAM,EAAE,CAAC;QACxEO,YAAY,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIgB,GAAG,GAAGvB,MAAM,EAAE;QACvBiB,IAAI,EAAE;MACR,CAAC,MAAM;QACLC,KAAK,EAAE;MACT;IACF;IAEAT,SAAS,CAAC,mBAAmB,CAAC;IAC9BF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMiB,SAAS,GAAGA,CAAA,KAAM;IACtBrB,cAAc,CAAC,CAAC,CAAC;IACjBE,eAAe,CAACP,SAAS,CAACqB,MAAM,GAAG,CAAC,CAAC;IACrCV,SAAS,CAAC,IAAI,CAAC;IACfF,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEd,OAAA;IAAKgC,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAEvEnC,OAAA;MAAKiC,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxDnC,OAAA;QAAIiC,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEF,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3C,OAAA;QAAGiC,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEK,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAExF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN3C,OAAA;MAAKiC,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnCnC,OAAA;QAAKiC,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACAnC,OAAA;UAAKiC,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,MAAM;YACdE,KAAK,EAAE,GAAI,CAAC/C,WAAW,GAAG,CAAC,IAAIc,KAAK,CAACS,MAAM,GAAI,GAAG,GAAG;YACrDyB,UAAU,EAAE;UACd;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN3C,OAAA;QAAGiC,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAES,MAAM,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAH,QAAA,GAAC,OAChE,EAAChC,WAAW,GAAG,CAAC,EAAC,MAAI,EAACc,KAAK,CAACS,MAAM;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN3C,OAAA;MAAKiC,KAAK,EAAE;QACVmB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXjB,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE;MACZ,CAAE;MAAApB,QAAA,EACClB,KAAK,CAACuC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAClB1D,OAAA;QAEE2D,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAACsD,KAAK,CAAE;QACrCzB,KAAK,EAAE;UACL2B,OAAO,EAAE,UAAU;UACnBC,MAAM,EAAE1D,WAAW,KAAKuD,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;UACtEX,YAAY,EAAE,MAAM;UACpBD,eAAe,EAAE3C,WAAW,KAAKuD,KAAK,GAAG,SAAS,GAAG,OAAO;UAC5DpB,KAAK,EAAEnC,WAAW,KAAKuD,KAAK,GAAG,OAAO,GAAG,MAAM;UAC/CI,MAAM,EAAE,SAAS;UACjBvB,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EAEDuB,KAAK,GAAG;MAAC,GAZLA,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3C,OAAA;MAAKiC,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACAnC,OAAA;QAAIiC,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDlB,KAAK,CAACd,WAAW,CAAC,CAACe;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAEL3C,OAAA;QAAGiC,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAE0B,UAAU,EAAE,KAAK;UAAE3B,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClElB,KAAK,CAACd,WAAW,CAAC,CAACgB;MAAO;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAGJ3C,OAAA;QAAKiC,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE,MAAM;UACpBH,UAAU,EAAE,6BAA6B;UACzCK,QAAQ,EAAE,MAAM;UAChBU,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACAnC,OAAA;UAAKiC,KAAK,EAAE;YAAEY,MAAM,EAAE,CAAC;YAAEoB,UAAU,EAAE;UAAW,CAAE;UAAA9B,QAAA,EAC/ClB,KAAK,CAACd,WAAW,CAAC,CAACiB;QAAI;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxC,WAAW,KAAK,CAAC,iBAChBH,OAAA;QAAKiC,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,MAAM;UACpBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACAnC,OAAA;UAAIiC,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGxE3C,OAAA;UAAKiC,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnCnC,OAAA;YAAKiC,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,KAAK;cAAED,cAAc,EAAE,QAAQ;cAAEhB,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EACzF9B,SAAS,CAACmD,GAAG,CAAC,CAACU,GAAG,EAAER,KAAK,kBACxB1D,OAAA;cAEEiC,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdI,OAAO,EAAE,MAAM;gBACfe,UAAU,EAAE,QAAQ;gBACpBd,cAAc,EAAE,QAAQ;gBACxBP,eAAe,EACbY,KAAK,KAAKjD,WAAW,GAAG,SAAS,GACjCiD,KAAK,KAAK/C,YAAY,GAAG,SAAS,GAAG,SAAS;gBAChD2B,KAAK,EAAGoB,KAAK,KAAKjD,WAAW,IAAIiD,KAAK,KAAK/C,YAAY,GAAI,OAAO,GAAG,MAAM;gBAC3EoC,YAAY,EAAE,KAAK;gBACnBqB,UAAU,EAAE,MAAM;gBAClBP,MAAM,EAAE,WAAW;gBACnBQ,WAAW,EACTX,KAAK,KAAKjD,WAAW,GAAG,SAAS,GACjCiD,KAAK,KAAK/C,YAAY,GAAG,SAAS,GAAG;cACzC,CAAE;cAAAwB,QAAA,EAED+B;YAAG,GAnBCR,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN3C,OAAA;YAAKiC,KAAK,EAAE;cAAEG,SAAS,EAAE,QAAQ;cAAEG,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnEnC,OAAA;cAAMiC,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,qBAAS,EAAC1B,WAAW;YAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC/D,KAAK,eACN3C,OAAA;cAAMiC,KAAK,EAAE;gBAAEK,KAAK,EAAE;cAAU,CAAE;cAAAH,QAAA,GAAC,sBAAU,EAACxB,YAAY;YAAA;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACjE,KAAK,eACN3C,OAAA;cAAAmC,QAAA,GAAM,UAAQ,EAAC5B,MAAM;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC5B,KAAK,eACN3C,OAAA;cAAAmC,QAAA,GAAM,OAAK,EAAC9B,SAAS,CAACI,WAAW,CAAC,GAAGJ,SAAS,CAACM,YAAY,CAAC;YAAA;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3C,OAAA;UAAKiC,KAAK,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEkB,GAAG,EAAE,MAAM;YAAEF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBAC1FnC,OAAA;YACE2D,OAAO,EAAEpC,OAAQ;YACjB+C,QAAQ,EAAEzD,SAAU;YACpBoB,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAEjD,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7C0D,OAAO,EAAE1D,SAAS,GAAG,GAAG,GAAG;YAC7B,CAAE;YAAAsB,QAAA,EAEDtB,SAAS,GAAG,eAAe,GAAG;UAAa;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAET3C,OAAA;YACE2D,OAAO,EAAE5B,SAAU;YACnBE,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL5B,MAAM,iBACLf,OAAA;UAAKiC,KAAK,EAAE;YACVuC,SAAS,EAAE,MAAM;YACjBZ,OAAO,EAAE,MAAM;YACfd,eAAe,EAAE/B,MAAM,CAAC0D,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS;YACjEZ,MAAM,EAAE,aAAa9C,MAAM,CAAC0D,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAS,EAAE;YACvE1B,YAAY,EAAE,KAAK;YACnBX,SAAS,EAAE,QAAQ;YACnBgC,UAAU,EAAE,MAAM;YAClB9B,KAAK,EAAEvB,MAAM,CAAC0D,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,GAAG;UAChD,CAAE;UAAAtC,QAAA,EACCpB;QAAM;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGD3C,OAAA;QAAKiC,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACAnC,OAAA;UAAIiC,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE;UAAa,CAAE;UAAAV,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3C,OAAA;UAAGiC,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE,YAAY;YAAE6B,SAAS,EAAE;UAAS,CAAE;UAAAvC,QAAA,EACvElB,KAAK,CAACd,WAAW,CAAC,CAACkB;QAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJ3C,OAAA;UAAAmC,QAAA,gBACEnC,OAAA;YAASiC,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEwB,MAAM,EAAE,SAAS;cAAEM,UAAU,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAAC;UAE7E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACV3C,OAAA;YAAGiC,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE,YAAY;cAAE8B,WAAW,EAAE;YAAO,CAAE;YAAAxC,QAAA,EACvElB,KAAK,CAACd,WAAW,CAAC,CAACmB;UAAM;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGN3C,OAAA;QAAKiC,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEmB,SAAS,EAAE;QAAO,CAAE;QAAArC,QAAA,gBAClFnC,OAAA;UACE2D,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAACwE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE1E,WAAW,GAAG,CAAC,CAAC,CAAE;UAC5DmE,QAAQ,EAAEnE,WAAW,KAAK,CAAE;UAC5B8B,KAAK,EAAE;YACLa,eAAe,EAAE3C,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC1DmC,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE3D,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;UAC9C,CAAE;UAAAgC,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET3C,OAAA;UACE2D,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAACwE,IAAI,CAACE,GAAG,CAAC7D,KAAK,CAACS,MAAM,GAAG,CAAC,EAAEvB,WAAW,GAAG,CAAC,CAAC,CAAE;UAC3EmE,QAAQ,EAAEnE,WAAW,KAAKc,KAAK,CAACS,MAAM,GAAG,CAAE;UAC3CO,KAAK,EAAE;YACLa,eAAe,EAAE3C,WAAW,KAAKc,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACzEY,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAE3D,WAAW,KAAKc,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UAC7D,CAAE;UAAAS,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA;MAAKiC,KAAK,EAAE;QACVa,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACAnC,OAAA;QAAIiC,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEvD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL3C,OAAA;QAAKiC,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAE2B,mBAAmB,EAAE,sCAAsC;UAAEzB,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EACvG,CACC;UAAE6C,IAAI,EAAE,SAAS;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACvD;UAAEF,IAAI,EAAE,WAAW;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC3D;UAAEF,IAAI,EAAE,mBAAmB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACjE;UAAEF,IAAI,EAAE,2BAA2B;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC3E;UAAEF,IAAI,EAAE,kBAAkB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EAChE;UAAEF,IAAI,EAAE,qBAAqB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,CACpE,CAAC1B,GAAG,CAAC,CAAC2B,OAAO,EAAEzB,KAAK,kBACnB1D,OAAA;UAEEiC,KAAK,EAAE;YACLa,eAAe,EAAE,OAAO;YACxBc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBAEFnC,OAAA;YAAIiC,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE;YAAY,CAAE;YAAAV,QAAA,EAAEgD,OAAO,CAACH;UAAI;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzE3C,OAAA;YAAKiC,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBAC9CnC,OAAA;cAAMiC,KAAK,EAAE;gBACXK,KAAK,EAAE6C,OAAO,CAACF,UAAU,KAAK,MAAM,GAAG,SAAS,GAC1CE,OAAO,CAACF,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG;cACtD,CAAE;cAAA9C,QAAA,EACCgD,OAAO,CAACF;YAAU;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACN,KAAK,eACN3C,OAAA;cAAAmC,QAAA,EAAOgD,OAAO,CAACD;YAAI;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA,GAlBDe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzC,EAAA,CA3dQD,kBAAkB;AAAAmF,EAAA,GAAlBnF,kBAAkB;AA6d3B,eAAeA,kBAAkB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}