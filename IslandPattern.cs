using System;
using System.Collections.Generic;

public class IslandPattern
{
    // =================================================
    // DFS (Depth-First Search) Approach
    // =================================================

    /// <summary>
    /// Finds the maximum area of an island in a grid using DFS.
    /// It iterates through each cell of the grid. If a cell contains a '1',
    /// it triggers a DFS traversal to find all connected '1's, calculating the area of that island.
    /// </summary>
    /// <param name="grid">A 2D grid of 0s (water) and 1s (land).</param>
    /// <returns>The area of the largest island found.</returns>
    public int MaxAreaOfIslandDfs(int[][] grid)
    {
        if (grid == null || grid.Length == 0)
        {
            return 0;
        }

        int rows = grid.Length;
        int cols = grid[0].Length;
        int maxArea = 0;
        int islandCount = 0;

        // Create a copy of the grid to avoid modifying the original one passed to the method.
        int[][] gridCopy = new int[rows][];
        for(int i = 0; i < rows; i++)
        {
            gridCopy[i] = new int[cols];
            Array.Copy(grid[i], gridCopy[i], cols);
        }


        for (int i = 0; i < rows; i++)
        {
            for (int j = 0; j < cols; j++)
            {
                // If we find a piece of land ('1'), it's the start of a new island.
                if (gridCopy[i][j] == 1)
                {
                    islandCount++;
                    int currentArea = GetIslandAreaDfs(gridCopy, i, j);
                    maxArea = Math.Max(maxArea, currentArea);
                }
            }
        }

        Console.WriteLine($"[DFS] Total number of islands found: {islandCount}");
        return maxArea;
    }

    /// <summary>
    /// A recursive helper function that performs DFS from a given cell (r, c).
    /// It calculates the area of the island connected to this cell.
    /// </summary>
    /// <param name="grid">The grid being traversed.</param>
    /// <param name="r">The current row.</param>
    /// <param name="c">The current column.</param>
    /// <returns>The area of the island.</returns>
    private int GetIslandAreaDfs(int[][] grid, int r, int c)
    {
        int rows = grid.Length;
        int cols = grid[0].Length;

        // Boundary checks: If we go out of bounds or hit water ('0'), we stop.
        if (r < 0 || r >= rows || c < 0 || c >= cols || grid[r][c] == 0)
        {
            return 0;
        }

        // Mark the current cell as visited by "sinking" it (changing its value to 0).
        // This is crucial to prevent infinite loops and recounting the same cell.
        grid[r][c] = 0;

        // The area of the current cell is 1.
        int area = 1;

        // Explore all 4 directions (up, down, left, right) recursively and add their areas.
        area += GetIslandAreaDfs(grid, r + 1, c); // Down
        area += GetIslandAreaDfs(grid, r - 1, c); // Up
        area += GetIslandAreaDfs(grid, r, c + 1); // Right
        area += GetIslandAreaDfs(grid, r, c - 1); // Left

        return area;
    }

    // =================================================
    // BFS (Breadth-First Search) Approach
    // =================================================

    /// <summary>
    /// Finds the maximum area of an island in a grid using BFS.
    /// It uses a queue to explore the island layer by layer.
    /// </summary>
    /// <param name="grid">A 2D grid of 0s (water) and 1s (land).</param>
    /// <returns>The area of the largest island found.</returns>
    public int MaxAreaOfIslandBfs(int[][] grid)
    {
        if (grid == null || grid.Length == 0)
        {
            return 0;
        }

        int rows = grid.Length;
        int cols = grid[0].Length;
        int maxArea = 0;
        int islandCount = 0;
        
        // Create a copy of the grid to avoid modifying the original one passed to the method.
        int[][] gridCopy = new int[rows][];
        for(int i = 0; i < rows; i++)
        {
            gridCopy[i] = new int[cols];
            Array.Copy(grid[i], gridCopy[i], cols);
        }

        for (int i = 0; i < rows; i++)
        {
            for (int j = 0; j < cols; j++)
            {
                if (gridCopy[i][j] == 1)
                {
                    islandCount++;
                    int currentArea = 0;
                    var queue = new Queue<(int, int)>();
                    
                    // Start BFS from the current land cell
                    queue.Enqueue((i, j));
                    gridCopy[i][j] = 0; // Mark as visited

                    while (queue.Count > 0)
                    {
                        var (r, c) = queue.Dequeue();
                        currentArea++;

                        // Define the 4 directions
                        int[] dr = { -1, 1, 0, 0 };
                        int[] dc = { 0, 0, -1, 1 };

                        for (int k = 0; k < 4; k++)
                        {
                            int nr = r + dr[k];
                            int nc = c + dc[k];

                            if (nr >= 0 && nr < rows && nc >= 0 && nc < cols && gridCopy[nr][nc] == 1)
                            {
                                queue.Enqueue((nr, nc));
                                gridCopy[nr][nc] = 0; // Mark as visited
                            }
                        }
                    }
                    maxArea = Math.Max(maxArea, currentArea);
                }
            }
        }
        Console.WriteLine($"[BFS] Total number of islands found: {islandCount}");
        return maxArea;
    }


    public static void Main(string[] args)
    {
        IslandPattern solution = new IslandPattern();

        int[][] grid = new int[][]
        {
            new int[] {0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0},
            new int[] {0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0},
            new int[] {0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0},
            new int[] {0, 1, 0, 0, 1, 1, 0, 0, 1, 0, 1, 0, 0},
            new int[] {0, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0},
            new int[] {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0},
            new int[] {0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0},
            new int[] {0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0}
        };

        // --- DFS Example ---
        Console.WriteLine("--- Running DFS Example ---");
        int maxAreaDfs = solution.MaxAreaOfIslandDfs(grid);
        Console.WriteLine($"The maximum area of an island is: {maxAreaDfs}");
        Console.WriteLine();

        // --- BFS Example ---
        Console.WriteLine("--- Running BFS Example ---");
        int maxAreaBfs = solution.MaxAreaOfIslandBfs(grid);
        Console.WriteLine($"The maximum area of an island is: {maxAreaBfs}");
    }
}