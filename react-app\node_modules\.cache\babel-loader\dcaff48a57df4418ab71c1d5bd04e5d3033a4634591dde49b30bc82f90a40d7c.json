{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\History.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction History() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Today's Activity Summary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"summary-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"AI Chat History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        children: \"- User: What is the primary goal of the Island (Matrix Traversal) pattern? - AI: The primary goal is to identify and process contiguous groups of elements. - User: Explain the island pattern with code using dfs or bfs also to identify the area of the island. - AI: [Provided a detailed explanation with C# code for DFS and BFS solutions]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"summary-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Code Window Changes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        children: \"- **Created:** `IslandPattern.cs` (with DFS and BFS implementations). - **Modified:** `CodingPatterns.csproj` (to change the startup object to run the new code). - **Fixed:** `hashmap.cs`, `hashmap2.cs`, `hashmap3.cs` (to resolve build errors). - **Modified:** `CodingPatterns.csproj` (to revert startup object).\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"summary-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Console Output\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        children: \"$ dotnet run --- Running DFS Example --- [DFS] Total number of islands found: 6 The maximum area of an island is: 6 --- Running BFS Example --- [BFS] Total number of islands found: 6 The maximum area of an island is: 6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = History;\nexport default History;\nvar _c;\n$RefreshReg$(_c, \"History\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "History", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/History.js"], "sourcesContent": ["import React from 'react';\r\n\r\nfunction History() {\r\n  return (\r\n    <div>\r\n      <h1>Today's Activity Summary</h1>\r\n      <div className=\"summary-section\">\r\n        <h2>AI Chat History</h2>\r\n        <pre>\r\n- User: What is the primary goal of the Island (Matrix Traversal) pattern?\r\n- AI: The primary goal is to identify and process contiguous groups of elements.\r\n- User: Explain the island pattern with code using dfs or bfs also to identify the area of the island.\r\n- AI: [Provided a detailed explanation with C# code for DFS and BFS solutions]\r\n        </pre>\r\n      </div>\r\n      <div className=\"summary-section\">\r\n        <h2>Code Window Changes</h2>\r\n        <pre>\r\n- **Created:** `IslandPattern.cs` (with DFS and BFS implementations).\r\n- **Modified:** `CodingPatterns.csproj` (to change the startup object to run the new code).\r\n- **Fixed:** `hashmap.cs`, `hashmap2.cs`, `hashmap3.cs` (to resolve build errors).\r\n- **Modified:** `CodingPatterns.csproj` (to revert startup object).\r\n        </pre>\r\n      </div>\r\n      <div className=\"summary-section\">\r\n        <h2>Console Output</h2>\r\n        <pre>\r\n$ dotnet run\r\n--- Running DFS Example ---\r\n[DFS] Total number of islands found: 6\r\nThe maximum area of an island is: 6\r\n\r\n--- Running BFS Example ---\r\n[BFS] Total number of islands found: 6\r\nThe maximum area of an island is: 6\r\n        </pre>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default History;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,OAAOA,CAAA,EAAG;EACjB,oBACED,OAAA;IAAAE,QAAA,gBACEF,OAAA;MAAAE,QAAA,EAAI;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjCN,OAAA;MAAKO,SAAS,EAAC,iBAAiB;MAAAL,QAAA,gBAC9BF,OAAA;QAAAE,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBN,OAAA;QAAAE,QAAA,EAAK;MAKL;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNN,OAAA;MAAKO,SAAS,EAAC,iBAAiB;MAAAL,QAAA,gBAC9BF,OAAA;QAAAE,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BN,OAAA;QAAAE,QAAA,EAAK;MAKL;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNN,OAAA;MAAKO,SAAS,EAAC,iBAAiB;MAAAL,QAAA,gBAC9BF,OAAA;QAAAE,QAAA,EAAI;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvBN,OAAA;QAAAE,QAAA,EAAK;MASL;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACE,EAAA,GArCQP,OAAO;AAuChB,eAAeA,OAAO;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}