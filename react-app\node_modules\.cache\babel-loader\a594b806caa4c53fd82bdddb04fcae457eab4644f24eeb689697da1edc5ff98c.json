{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\patterns\\\\SlidingWindowPattern.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SlidingWindowPattern() {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([2, 1, 2, 3, 4, 1, 2, 1]);\n  const [windowSize, setWindowSize] = useState(3);\n  const [windowStart, setWindowStart] = useState(0);\n  const [windowEnd, setWindowEnd] = useState(2);\n  const [isRunning, setIsRunning] = useState(false);\n  const [currentSum, setCurrentSum] = useState(5);\n  const [maxSum, setMaxSum] = useState(5);\n  const steps = [{\n    title: \"🪟 Understanding Sliding Window Pattern\",\n    content: \"The Sliding Window technique maintains a subset of data (window) that slides through the array to solve subarray/substring problems efficiently.\",\n    code: `// Sliding Window Template\nfunction slidingWindow(arr, k) {\n    let windowSum = 0;\n    let maxSum = 0;\n    \n    // Calculate sum of first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    maxSum = windowSum;\n    \n    // Slide the window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}`,\n    learningPrompt: \"Why is sliding window more efficient than recalculating each subarray?\",\n    answer: \"Instead of recalculating the entire sum (O(k) for each position), we just remove the leftmost element and add the new rightmost element (O(1) per slide).\"\n  }, {\n    title: \"📏 Step 1: Fixed vs Variable Window\",\n    content: \"Identify whether you need a fixed-size window or a variable-size window based on the problem requirements.\",\n    code: `// Fixed Window (size k)\nfunction maxSumSubarray(arr, k) {\n    let windowSum = 0;\n    \n    // Initial window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}\n\n// Variable Window\nfunction longestSubstringKDistinct(s, k) {\n    let left = 0, maxLength = 0;\n    let charCount = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        charCount.set(s[right], (charCount.get(s[right]) || 0) + 1);\n        \n        while (charCount.size > k) {\n            charCount.set(s[left], charCount.get(s[left]) - 1);\n            if (charCount.get(s[left]) === 0) {\n                charCount.delete(s[left]);\n            }\n            left++;\n        }\n        \n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}`,\n    learningPrompt: \"When do we use fixed vs variable window size?\",\n    answer: \"Fixed window: when problem specifies exact size (e.g., 'subarray of size k'). Variable window: when we need to find optimal size based on conditions.\"\n  }, {\n    title: \"🎯 Step 2: Initialize Window\",\n    content: \"Set up the initial window and calculate the initial state (sum, count, etc.).\",\n    code: `// Step 2: Window Initialization\nfunction initializeWindow(arr, k) {\n    let windowSum = 0;\n    let windowStart = 0;\n    let windowEnd = k - 1;\n    \n    // Calculate initial window sum\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    console.log(\\`Initial window: [\\${windowStart}, \\${windowEnd}]\\`);\n    console.log(\\`Initial sum: \\${windowSum}\\`);\n    \n    return { windowSum, windowStart, windowEnd };\n}\n\n// For variable window\nfunction initializeVariableWindow() {\n    let left = 0;\n    let right = 0;\n    let windowState = new Map(); // or other data structure\n    \n    return { left, right, windowState };\n}`,\n    learningPrompt: \"What should we track in the window state?\",\n    answer: \"Depends on problem: sum (for max sum), character counts (for substring), frequency maps, or custom conditions.\"\n  }, {\n    title: \"➡️ Step 3: Slide the Window\",\n    content: \"Move the window by removing the leftmost element and adding the new rightmost element.\",\n    code: `// Step 3: Window Sliding Logic\nfunction slideWindow(arr, k) {\n    let windowSum = 0;\n    \n    // Initialize first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide the window\n    for (let windowEnd = k; windowEnd < arr.length; windowEnd++) {\n        // Add new element to window\n        windowSum += arr[windowEnd];\n        \n        // Remove element going out of window\n        windowSum -= arr[windowEnd - k];\n        \n        // Update result\n        maxSum = Math.max(maxSum, windowSum);\n        \n        console.log(\\`Window: [\\${windowEnd - k + 1}, \\${windowEnd}], Sum: \\${windowSum}\\`);\n    }\n    \n    return maxSum;\n}`,\n    learningPrompt: \"What's the key insight in the sliding operation?\",\n    answer: \"We maintain the window size by simultaneously adding one element and removing another, keeping the operation O(1) per slide.\"\n  }, {\n    title: \"🎮 Step 4: Interactive Demo\",\n    content: \"Watch the sliding window algorithm find the maximum sum subarray of size k.\",\n    code: `// Live Demo: Maximum Sum Subarray\nconst arr = [2, 1, 2, 3, 4, 1, 2, 1];\nconst k = 3;\n\n// Current window: [${windowStart}, ${windowEnd}]\n// Window elements: [${demoArray.slice(windowStart, windowEnd + 1).join(', ')}]\n// Current sum: ${currentSum}\n// Maximum sum so far: ${maxSum}`,\n    learningPrompt: \"How does the window maintain its size while sliding?\",\n    answer: \"By adding the new element at the right and removing the old element at the left simultaneously.\"\n  }, {\n    title: \"🔄 Step 5: Variable Window Patterns\",\n    content: \"Learn how to handle variable-size windows that expand and contract based on conditions.\",\n    code: `// Variable Window: Longest Substring Without Repeating Characters\nfunction lengthOfLongestSubstring(s) {\n    let left = 0;\n    let maxLength = 0;\n    let charSet = new Set();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        while (charSet.has(s[right])) {\n            charSet.delete(s[left]);\n            left++;\n        }\n        \n        charSet.add(s[right]);\n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}\n\n// Variable Window: Minimum Window Substring\nfunction minWindow(s, t) {\n    let left = 0, minLen = Infinity, minStart = 0;\n    let required = new Map();\n    let formed = 0;\n    \n    // Count characters in t\n    for (let char of t) {\n        required.set(char, (required.get(char) || 0) + 1);\n    }\n    \n    let windowCounts = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        let char = s[right];\n        windowCounts.set(char, (windowCounts.get(char) || 0) + 1);\n        \n        if (required.has(char) && windowCounts.get(char) === required.get(char)) {\n            formed++;\n        }\n        \n        // Contract window\n        while (left <= right && formed === required.size) {\n            if (right - left + 1 < minLen) {\n                minLen = right - left + 1;\n                minStart = left;\n            }\n            \n            let leftChar = s[left];\n            windowCounts.set(leftChar, windowCounts.get(leftChar) - 1);\n            if (required.has(leftChar) && windowCounts.get(leftChar) < required.get(leftChar)) {\n                formed--;\n            }\n            left++;\n        }\n    }\n    \n    return minLen === Infinity ? \"\" : s.substring(minStart, minStart + minLen);\n}`,\n    learningPrompt: \"When do we expand vs contract a variable window?\",\n    answer: \"Expand when we haven't met the condition yet. Contract when we've met the condition and want to find the minimum valid window.\"\n  }];\n  const runDemo = async () => {\n    setIsRunning(true);\n    let start = 0;\n    let sum = demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0);\n    let max = sum;\n    setWindowStart(start);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(sum);\n    setMaxSum(max);\n    await new Promise(resolve => setTimeout(resolve, 1500));\n    for (let end = windowSize; end < demoArray.length; end++) {\n      // Slide window\n      sum = sum - demoArray[start] + demoArray[end];\n      start++;\n      max = Math.max(max, sum);\n      setWindowStart(start);\n      setWindowEnd(end);\n      setCurrentSum(sum);\n      setMaxSum(max);\n      await new Promise(resolve => setTimeout(resolve, 1500));\n    }\n    setIsRunning(false);\n  };\n  const resetDemo = () => {\n    setWindowStart(0);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setMaxSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setIsRunning(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    style: {\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#3498db',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83E\\uDE9F Sliding Window Pattern\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em',\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: \"Master the sliding window technique to optimize subarray and substring problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#3498db',\n            height: '100%',\n            width: `${(currentStep + 1) / steps.length * 100}%`,\n            transition: 'width 0.3s ease'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          textAlign: 'center',\n          margin: '10px 0',\n          color: '#7f8c8d'\n        },\n        children: [\"Step \", currentStep + 1, \" of \", steps.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 284,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '10px',\n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      },\n      children: steps.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setCurrentStep(index),\n        style: {\n          padding: '8px 16px',\n          border: currentStep === index ? '2px solid #3498db' : '1px solid #ddd',\n          borderRadius: '20px',\n          backgroundColor: currentStep === index ? '#3498db' : 'white',\n          color: currentStep === index ? 'white' : '#333',\n          cursor: 'pointer',\n          fontSize: '14px'\n        },\n        children: index + 1\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#2c3e50',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#666',\n          lineHeight: '1.6',\n          marginBottom: '20px'\n        },\n        children: steps[currentStep].content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            margin: 0,\n            whiteSpace: 'pre-wrap'\n          },\n          children: steps[currentStep].code\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f0f8ff',\n          border: '2px solid #3498db',\n          borderRadius: '12px',\n          padding: '20px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#3498db',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83C\\uDFAE Live Demo: Maximum Sum Subarray\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '5px',\n              justifyContent: 'center',\n              marginBottom: '10px'\n            },\n            children: demoArray.map((num, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '40px',\n                height: '40px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: index >= windowStart && index <= windowEnd ? '#3498db' : '#ecf0f1',\n                color: index >= windowStart && index <= windowEnd ? 'white' : '#333',\n                borderRadius: '8px',\n                fontWeight: 'bold',\n                border: '2px solid',\n                borderColor: index >= windowStart && index <= windowEnd ? '#2980b9' : '#bdc3c7',\n                position: 'relative'\n              },\n              children: [num, index === windowStart && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-25px',\n                  fontSize: '12px',\n                  color: '#e74c3c',\n                  fontWeight: 'bold'\n                },\n                children: \"Start\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 23\n              }, this), index === windowEnd && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '-25px',\n                  fontSize: '12px',\n                  color: '#e74c3c',\n                  fontWeight: 'bold'\n                },\n                children: \"End\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Window Size: \", windowSize]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Current Sum: \", currentSum]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), ' | ', /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#27ae60',\n                fontWeight: 'bold'\n              },\n              children: [\"Max Sum: \", maxSum]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            gap: '10px',\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: runDemo,\n            disabled: isRunning,\n            style: {\n              backgroundColor: '#3498db',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: isRunning ? 'not-allowed' : 'pointer',\n              opacity: isRunning ? 0.6 : 1\n            },\n            children: isRunning ? '🔄 Sliding...' : '▶️ Run Demo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetDemo,\n            style: {\n              backgroundColor: '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '10px 20px',\n              borderRadius: '6px',\n              cursor: 'pointer'\n            },\n            children: \"\\uD83D\\uDD04 Reset\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0'\n          },\n          children: \"\\uD83E\\uDD14 Learning Prompt:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#856404',\n            margin: '0 0 10px 0',\n            fontStyle: 'italic'\n          },\n          children: steps[currentStep].learningPrompt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"details\", {\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            style: {\n              color: '#856404',\n              cursor: 'pointer',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCA1 Click to see answer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#856404',\n              margin: '10px 0 0 0',\n              paddingLeft: '20px'\n            },\n            children: steps[currentStep].answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginTop: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.max(0, currentStep - 1)),\n          disabled: currentStep === 0,\n          style: {\n            backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(Math.min(steps.length - 1, currentStep + 1)),\n          disabled: currentStep === steps.length - 1,\n          style: {\n            backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#3498db',\n            color: 'white',\n            border: 'none',\n            padding: '12px 24px',\n            borderRadius: '6px',\n            cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n          },\n          children: \"Next \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #3498db'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          color: '#3498db',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83C\\uDFAF Practice Problems\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '15px'\n        },\n        children: [{\n          name: \"Maximum Sum Subarray of Size K\",\n          difficulty: \"Easy\",\n          time: \"15 min\"\n        }, {\n          name: \"Longest Substring Without Repeating\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Minimum Window Substring\",\n          difficulty: \"Hard\",\n          time: \"40 min\"\n        }, {\n          name: \"Permutation in String\",\n          difficulty: \"Medium\",\n          time: \"30 min\"\n        }, {\n          name: \"Longest Substring with K Distinct\",\n          difficulty: \"Medium\",\n          time: \"25 min\"\n        }, {\n          name: \"Sliding Window Maximum\",\n          difficulty: \"Hard\",\n          time: \"35 min\"\n        }].map((problem, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            padding: '15px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#2c3e50',\n              margin: '0 0 8px 0'\n            },\n            children: problem.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: problem.difficulty === 'Easy' ? '#27ae60' : problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c'\n              },\n              children: problem.difficulty\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this), ' • ', /*#__PURE__*/_jsxDEV(\"span\", {\n              children: problem.time\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n}\n_s(SlidingWindowPattern, \"Wf1IpH6QkDsCZXubIsUIxBbXVog=\");\n_c = SlidingWindowPattern;\nexport default SlidingWindowPattern;\nvar _c;\n$RefreshReg$(_c, \"SlidingWindowPattern\");", "map": {"version": 3, "names": ["React", "useState", "questionsData", "jsxDEV", "_jsxDEV", "SlidingWindowPattern", "_s", "currentStep", "setCurrentStep", "demoArray", "setDemoArray", "windowSize", "setWindowSize", "windowStart", "setWindowStart", "windowEnd", "setWindowEnd", "isRunning", "setIsRunning", "currentSum", "setCurrentSum", "maxSum", "setMaxSum", "steps", "title", "content", "code", "learningPrompt", "answer", "slice", "join", "runDemo", "start", "sum", "reduce", "a", "b", "max", "Promise", "resolve", "setTimeout", "end", "length", "Math", "resetDemo", "className", "style", "fontFamily", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "borderRadius", "height", "overflow", "width", "transition", "display", "justifyContent", "gap", "flexWrap", "map", "_", "index", "onClick", "padding", "border", "cursor", "boxShadow", "lineHeight", "whiteSpace", "num", "alignItems", "fontWeight", "borderColor", "position", "top", "disabled", "opacity", "fontStyle", "paddingLeft", "marginTop", "min", "gridTemplateColumns", "name", "difficulty", "time", "problem", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/patterns/SlidingWindowPattern.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport questionsData from '../mcqs.json';\n\nfunction SlidingWindowPattern() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [demoArray, setDemoArray] = useState([2, 1, 2, 3, 4, 1, 2, 1]);\n  const [windowSize, setWindowSize] = useState(3);\n  const [windowStart, setWindowStart] = useState(0);\n  const [windowEnd, setWindowEnd] = useState(2);\n  const [isRunning, setIsRunning] = useState(false);\n  const [currentSum, setCurrentSum] = useState(5);\n  const [maxSum, setMaxSum] = useState(5);\n\n  const steps = [\n    {\n      title: \"🪟 Understanding Sliding Window Pattern\",\n      content: \"The Sliding Window technique maintains a subset of data (window) that slides through the array to solve subarray/substring problems efficiently.\",\n      code: `// Sliding Window Template\nfunction slidingWindow(arr, k) {\n    let windowSum = 0;\n    let maxSum = 0;\n    \n    // Calculate sum of first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    maxSum = windowSum;\n    \n    // Slide the window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}`,\n      learningPrompt: \"Why is sliding window more efficient than recalculating each subarray?\",\n      answer: \"Instead of recalculating the entire sum (O(k) for each position), we just remove the leftmost element and add the new rightmost element (O(1) per slide).\"\n    },\n    {\n      title: \"📏 Step 1: Fixed vs Variable Window\",\n      content: \"Identify whether you need a fixed-size window or a variable-size window based on the problem requirements.\",\n      code: `// Fixed Window (size k)\nfunction maxSumSubarray(arr, k) {\n    let windowSum = 0;\n    \n    // Initial window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide window\n    for (let i = k; i < arr.length; i++) {\n        windowSum = windowSum - arr[i - k] + arr[i];\n        maxSum = Math.max(maxSum, windowSum);\n    }\n    \n    return maxSum;\n}\n\n// Variable Window\nfunction longestSubstringKDistinct(s, k) {\n    let left = 0, maxLength = 0;\n    let charCount = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        charCount.set(s[right], (charCount.get(s[right]) || 0) + 1);\n        \n        while (charCount.size > k) {\n            charCount.set(s[left], charCount.get(s[left]) - 1);\n            if (charCount.get(s[left]) === 0) {\n                charCount.delete(s[left]);\n            }\n            left++;\n        }\n        \n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}`,\n      learningPrompt: \"When do we use fixed vs variable window size?\",\n      answer: \"Fixed window: when problem specifies exact size (e.g., 'subarray of size k'). Variable window: when we need to find optimal size based on conditions.\"\n    },\n    {\n      title: \"🎯 Step 2: Initialize Window\",\n      content: \"Set up the initial window and calculate the initial state (sum, count, etc.).\",\n      code: `// Step 2: Window Initialization\nfunction initializeWindow(arr, k) {\n    let windowSum = 0;\n    let windowStart = 0;\n    let windowEnd = k - 1;\n    \n    // Calculate initial window sum\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    console.log(\\`Initial window: [\\${windowStart}, \\${windowEnd}]\\`);\n    console.log(\\`Initial sum: \\${windowSum}\\`);\n    \n    return { windowSum, windowStart, windowEnd };\n}\n\n// For variable window\nfunction initializeVariableWindow() {\n    let left = 0;\n    let right = 0;\n    let windowState = new Map(); // or other data structure\n    \n    return { left, right, windowState };\n}`,\n      learningPrompt: \"What should we track in the window state?\",\n      answer: \"Depends on problem: sum (for max sum), character counts (for substring), frequency maps, or custom conditions.\"\n    },\n    {\n      title: \"➡️ Step 3: Slide the Window\",\n      content: \"Move the window by removing the leftmost element and adding the new rightmost element.\",\n      code: `// Step 3: Window Sliding Logic\nfunction slideWindow(arr, k) {\n    let windowSum = 0;\n    \n    // Initialize first window\n    for (let i = 0; i < k; i++) {\n        windowSum += arr[i];\n    }\n    \n    let maxSum = windowSum;\n    \n    // Slide the window\n    for (let windowEnd = k; windowEnd < arr.length; windowEnd++) {\n        // Add new element to window\n        windowSum += arr[windowEnd];\n        \n        // Remove element going out of window\n        windowSum -= arr[windowEnd - k];\n        \n        // Update result\n        maxSum = Math.max(maxSum, windowSum);\n        \n        console.log(\\`Window: [\\${windowEnd - k + 1}, \\${windowEnd}], Sum: \\${windowSum}\\`);\n    }\n    \n    return maxSum;\n}`,\n      learningPrompt: \"What's the key insight in the sliding operation?\",\n      answer: \"We maintain the window size by simultaneously adding one element and removing another, keeping the operation O(1) per slide.\"\n    },\n    {\n      title: \"🎮 Step 4: Interactive Demo\",\n      content: \"Watch the sliding window algorithm find the maximum sum subarray of size k.\",\n      code: `// Live Demo: Maximum Sum Subarray\nconst arr = [2, 1, 2, 3, 4, 1, 2, 1];\nconst k = 3;\n\n// Current window: [${windowStart}, ${windowEnd}]\n// Window elements: [${demoArray.slice(windowStart, windowEnd + 1).join(', ')}]\n// Current sum: ${currentSum}\n// Maximum sum so far: ${maxSum}`,\n      learningPrompt: \"How does the window maintain its size while sliding?\",\n      answer: \"By adding the new element at the right and removing the old element at the left simultaneously.\"\n    },\n    {\n      title: \"🔄 Step 5: Variable Window Patterns\",\n      content: \"Learn how to handle variable-size windows that expand and contract based on conditions.\",\n      code: `// Variable Window: Longest Substring Without Repeating Characters\nfunction lengthOfLongestSubstring(s) {\n    let left = 0;\n    let maxLength = 0;\n    let charSet = new Set();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        while (charSet.has(s[right])) {\n            charSet.delete(s[left]);\n            left++;\n        }\n        \n        charSet.add(s[right]);\n        maxLength = Math.max(maxLength, right - left + 1);\n    }\n    \n    return maxLength;\n}\n\n// Variable Window: Minimum Window Substring\nfunction minWindow(s, t) {\n    let left = 0, minLen = Infinity, minStart = 0;\n    let required = new Map();\n    let formed = 0;\n    \n    // Count characters in t\n    for (let char of t) {\n        required.set(char, (required.get(char) || 0) + 1);\n    }\n    \n    let windowCounts = new Map();\n    \n    for (let right = 0; right < s.length; right++) {\n        // Expand window\n        let char = s[right];\n        windowCounts.set(char, (windowCounts.get(char) || 0) + 1);\n        \n        if (required.has(char) && windowCounts.get(char) === required.get(char)) {\n            formed++;\n        }\n        \n        // Contract window\n        while (left <= right && formed === required.size) {\n            if (right - left + 1 < minLen) {\n                minLen = right - left + 1;\n                minStart = left;\n            }\n            \n            let leftChar = s[left];\n            windowCounts.set(leftChar, windowCounts.get(leftChar) - 1);\n            if (required.has(leftChar) && windowCounts.get(leftChar) < required.get(leftChar)) {\n                formed--;\n            }\n            left++;\n        }\n    }\n    \n    return minLen === Infinity ? \"\" : s.substring(minStart, minStart + minLen);\n}`,\n      learningPrompt: \"When do we expand vs contract a variable window?\",\n      answer: \"Expand when we haven't met the condition yet. Contract when we've met the condition and want to find the minimum valid window.\"\n    }\n  ];\n\n  const runDemo = async () => {\n    setIsRunning(true);\n    let start = 0;\n    let sum = demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0);\n    let max = sum;\n    \n    setWindowStart(start);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(sum);\n    setMaxSum(max);\n    \n    await new Promise(resolve => setTimeout(resolve, 1500));\n    \n    for (let end = windowSize; end < demoArray.length; end++) {\n      // Slide window\n      sum = sum - demoArray[start] + demoArray[end];\n      start++;\n      max = Math.max(max, sum);\n      \n      setWindowStart(start);\n      setWindowEnd(end);\n      setCurrentSum(sum);\n      setMaxSum(max);\n      \n      await new Promise(resolve => setTimeout(resolve, 1500));\n    }\n    \n    setIsRunning(false);\n  };\n\n  const resetDemo = () => {\n    setWindowStart(0);\n    setWindowEnd(windowSize - 1);\n    setCurrentSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setMaxSum(demoArray.slice(0, windowSize).reduce((a, b) => a + b, 0));\n    setIsRunning(false);\n  };\n\n  return (\n    <div className=\"page-content\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Header */}\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#3498db', fontSize: '2.5em', marginBottom: '10px' }}>\n          🪟 Sliding Window Pattern\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>\n          Master the sliding window technique to optimize subarray and substring problems\n        </p>\n      </div>\n\n      {/* Progress Bar */}\n      <div style={{ marginBottom: '30px' }}>\n        <div style={{\n          backgroundColor: '#ecf0f1',\n          borderRadius: '10px',\n          height: '8px',\n          overflow: 'hidden'\n        }}>\n          <div style={{\n            backgroundColor: '#3498db',\n            height: '100%',\n            width: `${((currentStep + 1) / steps.length) * 100}%`,\n            transition: 'width 0.3s ease'\n          }}></div>\n        </div>\n        <p style={{ textAlign: 'center', margin: '10px 0', color: '#7f8c8d' }}>\n          Step {currentStep + 1} of {steps.length}\n        </p>\n      </div>\n\n      {/* Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        gap: '10px', \n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      }}>\n        {steps.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentStep(index)}\n            style={{\n              padding: '8px 16px',\n              border: currentStep === index ? '2px solid #3498db' : '1px solid #ddd',\n              borderRadius: '20px',\n              backgroundColor: currentStep === index ? '#3498db' : 'white',\n              color: currentStep === index ? 'white' : '#333',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {index + 1}\n          </button>\n        ))}\n      </div>\n\n      {/* Current Step Content */}\n      <div style={{\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        padding: '30px',\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n        marginBottom: '30px'\n      }}>\n        <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>\n          {steps[currentStep].title}\n        </h2>\n        \n        <p style={{ color: '#666', lineHeight: '1.6', marginBottom: '20px' }}>\n          {steps[currentStep].content}\n        </p>\n\n        {/* Code Block */}\n        <div style={{\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #e9ecef',\n          borderRadius: '8px',\n          padding: '20px',\n          marginBottom: '20px',\n          fontFamily: 'Monaco, Consolas, monospace',\n          fontSize: '14px',\n          overflow: 'auto'\n        }}>\n          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>\n            {steps[currentStep].code}\n          </pre>\n        </div>\n\n        {/* Interactive Demo for Step 4 */}\n        {currentStep === 4 && (\n          <div style={{\n            backgroundColor: '#f0f8ff',\n            border: '2px solid #3498db',\n            borderRadius: '12px',\n            padding: '20px',\n            marginBottom: '20px'\n          }}>\n            <h3 style={{ color: '#3498db', marginBottom: '15px' }}>🎮 Live Demo: Maximum Sum Subarray</h3>\n            \n            {/* Array Visualization */}\n            <div style={{ marginBottom: '20px' }}>\n              <div style={{ display: 'flex', gap: '5px', justifyContent: 'center', marginBottom: '10px' }}>\n                {demoArray.map((num, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      width: '40px',\n                      height: '40px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      backgroundColor: \n                        index >= windowStart && index <= windowEnd ? '#3498db' : '#ecf0f1',\n                      color: (index >= windowStart && index <= windowEnd) ? 'white' : '#333',\n                      borderRadius: '8px',\n                      fontWeight: 'bold',\n                      border: '2px solid',\n                      borderColor: \n                        index >= windowStart && index <= windowEnd ? '#2980b9' : '#bdc3c7',\n                      position: 'relative'\n                    }}\n                  >\n                    {num}\n                    {index === windowStart && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-25px',\n                        fontSize: '12px',\n                        color: '#e74c3c',\n                        fontWeight: 'bold'\n                      }}>\n                        Start\n                      </div>\n                    )}\n                    {index === windowEnd && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '-25px',\n                        fontSize: '12px',\n                        color: '#e74c3c',\n                        fontWeight: 'bold'\n                      }}>\n                        End\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n              \n              <div style={{ textAlign: 'center', fontSize: '14px', color: '#666' }}>\n                <span>Window Size: {windowSize}</span>\n                {' | '}\n                <span>Current Sum: {currentSum}</span>\n                {' | '}\n                <span style={{ color: '#27ae60', fontWeight: 'bold' }}>Max Sum: {maxSum}</span>\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div style={{ textAlign: 'center', gap: '10px', display: 'flex', justifyContent: 'center' }}>\n              <button\n                onClick={runDemo}\n                disabled={isRunning}\n                style={{\n                  backgroundColor: '#3498db',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: isRunning ? 'not-allowed' : 'pointer',\n                  opacity: isRunning ? 0.6 : 1\n                }}\n              >\n                {isRunning ? '🔄 Sliding...' : '▶️ Run Demo'}\n              </button>\n              \n              <button\n                onClick={resetDemo}\n                style={{\n                  backgroundColor: '#95a5a6',\n                  color: 'white',\n                  border: 'none',\n                  padding: '10px 20px',\n                  borderRadius: '6px',\n                  cursor: 'pointer'\n                }}\n              >\n                🔄 Reset\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Prompt */}\n        <div style={{\n          backgroundColor: '#fff3cd',\n          border: '1px solid #ffeaa7',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        }}>\n          <h4 style={{ color: '#856404', margin: '0 0 10px 0' }}>\n            🤔 Learning Prompt:\n          </h4>\n          <p style={{ color: '#856404', margin: '0 0 10px 0', fontStyle: 'italic' }}>\n            {steps[currentStep].learningPrompt}\n          </p>\n          <details>\n            <summary style={{ color: '#856404', cursor: 'pointer', fontWeight: 'bold' }}>\n              💡 Click to see answer\n            </summary>\n            <p style={{ color: '#856404', margin: '10px 0 0 0', paddingLeft: '20px' }}>\n              {steps[currentStep].answer}\n            </p>\n          </details>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '30px' }}>\n          <button\n            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}\n            disabled={currentStep === 0}\n            style={{\n              backgroundColor: currentStep === 0 ? '#bdc3c7' : '#95a5a6',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === 0 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            ← Previous\n          </button>\n          \n          <button\n            onClick={() => setCurrentStep(Math.min(steps.length - 1, currentStep + 1))}\n            disabled={currentStep === steps.length - 1}\n            style={{\n              backgroundColor: currentStep === steps.length - 1 ? '#bdc3c7' : '#3498db',\n              color: 'white',\n              border: 'none',\n              padding: '12px 24px',\n              borderRadius: '6px',\n              cursor: currentStep === steps.length - 1 ? 'not-allowed' : 'pointer'\n            }}\n          >\n            Next →\n          </button>\n        </div>\n      </div>\n\n      {/* Practice Problems */}\n      <div style={{\n        backgroundColor: '#f8f9fa',\n        borderRadius: '12px',\n        padding: '25px',\n        border: '2px solid #3498db'\n      }}>\n        <h3 style={{ color: '#3498db', marginBottom: '20px' }}>\n          🎯 Practice Problems\n        </h3>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\n          {[\n            { name: \"Maximum Sum Subarray of Size K\", difficulty: \"Easy\", time: \"15 min\" },\n            { name: \"Longest Substring Without Repeating\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Minimum Window Substring\", difficulty: \"Hard\", time: \"40 min\" },\n            { name: \"Permutation in String\", difficulty: \"Medium\", time: \"30 min\" },\n            { name: \"Longest Substring with K Distinct\", difficulty: \"Medium\", time: \"25 min\" },\n            { name: \"Sliding Window Maximum\", difficulty: \"Hard\", time: \"35 min\" }\n          ].map((problem, index) => (\n            <div\n              key={index}\n              style={{\n                backgroundColor: 'white',\n                padding: '15px',\n                borderRadius: '8px',\n                border: '1px solid #dee2e6'\n              }}\n            >\n              <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0' }}>{problem.name}</h4>\n              <div style={{ fontSize: '14px', color: '#666' }}>\n                <span style={{ \n                  color: problem.difficulty === 'Easy' ? '#27ae60' : \n                        problem.difficulty === 'Medium' ? '#f39c12' : '#e74c3c' \n                }}>\n                  {problem.difficulty}\n                </span>\n                {' • '}\n                <span>{problem.time}</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default SlidingWindowPattern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,aAAa,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,oBAAoBA,CAAA,EAAG;EAAAC,EAAA;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EAEvC,MAAMsB,KAAK,GAAG,CACZ;IACEC,KAAK,EAAE,yCAAyC;IAChDC,OAAO,EAAE,kJAAkJ;IAC3JC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,wEAAwE;IACxFC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,4GAA4G;IACrHC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,+CAA+C;IAC/DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE,+EAA+E;IACxFC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,2CAA2C;IAC3DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,wFAAwF;IACjGC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,kDAAkD;IAClEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,6EAA6E;IACtFC,IAAI,EAAE;AACZ;AACA;AACA;AACA,sBAAsBb,WAAW,KAAKE,SAAS;AAC/C,uBAAuBN,SAAS,CAACoB,KAAK,CAAChB,WAAW,EAAEE,SAAS,GAAG,CAAC,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC;AAC7E,kBAAkBX,UAAU;AAC5B,yBAAyBE,MAAM,EAAE;IAC3BM,cAAc,EAAE,sDAAsD;IACtEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,qCAAqC;IAC5CC,OAAO,EAAE,yFAAyF;IAClGC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,cAAc,EAAE,kDAAkD;IAClEC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMG,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1Bb,YAAY,CAAC,IAAI,CAAC;IAClB,IAAIc,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAGxB,SAAS,CAACoB,KAAK,CAAC,CAAC,EAAElB,UAAU,CAAC,CAACuB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC;IACnE,IAAIC,GAAG,GAAGJ,GAAG;IAEbnB,cAAc,CAACkB,KAAK,CAAC;IACrBhB,YAAY,CAACL,UAAU,GAAG,CAAC,CAAC;IAC5BS,aAAa,CAACa,GAAG,CAAC;IAClBX,SAAS,CAACe,GAAG,CAAC;IAEd,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD,KAAK,IAAIE,GAAG,GAAG9B,UAAU,EAAE8B,GAAG,GAAGhC,SAAS,CAACiC,MAAM,EAAED,GAAG,EAAE,EAAE;MACxD;MACAR,GAAG,GAAGA,GAAG,GAAGxB,SAAS,CAACuB,KAAK,CAAC,GAAGvB,SAAS,CAACgC,GAAG,CAAC;MAC7CT,KAAK,EAAE;MACPK,GAAG,GAAGM,IAAI,CAACN,GAAG,CAACA,GAAG,EAAEJ,GAAG,CAAC;MAExBnB,cAAc,CAACkB,KAAK,CAAC;MACrBhB,YAAY,CAACyB,GAAG,CAAC;MACjBrB,aAAa,CAACa,GAAG,CAAC;MAClBX,SAAS,CAACe,GAAG,CAAC;MAEd,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IACzD;IAEArB,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAM0B,SAAS,GAAGA,CAAA,KAAM;IACtB9B,cAAc,CAAC,CAAC,CAAC;IACjBE,YAAY,CAACL,UAAU,GAAG,CAAC,CAAC;IAC5BS,aAAa,CAACX,SAAS,CAACoB,KAAK,CAAC,CAAC,EAAElB,UAAU,CAAC,CAACuB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxEd,SAAS,CAACb,SAAS,CAACoB,KAAK,CAAC,CAAC,EAAElB,UAAU,CAAC,CAACuB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpElB,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEd,OAAA;IAAKyC,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAEvE5C,OAAA;MAAK0C,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxD5C,OAAA;QAAI0C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEF,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLpD,OAAA;QAAG0C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEK,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAV,QAAA,EAAC;MAExF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNpD,OAAA;MAAK0C,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnC5C,OAAA;QAAK0C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACA5C,OAAA;UAAK0C,KAAK,EAAE;YACVa,eAAe,EAAE,SAAS;YAC1BE,MAAM,EAAE,MAAM;YACdE,KAAK,EAAE,GAAI,CAACxD,WAAW,GAAG,CAAC,IAAIgB,KAAK,CAACmB,MAAM,GAAI,GAAG,GAAG;YACrDsB,UAAU,EAAE;UACd;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNpD,OAAA;QAAG0C,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAES,MAAM,EAAE,QAAQ;UAAEP,KAAK,EAAE;QAAU,CAAE;QAAAH,QAAA,GAAC,OAChE,EAACzC,WAAW,GAAG,CAAC,EAAC,MAAI,EAACgB,KAAK,CAACmB,MAAM;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNpD,OAAA;MAAK0C,KAAK,EAAE;QACVmB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXjB,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE;MACZ,CAAE;MAAApB,QAAA,EACCzB,KAAK,CAAC8C,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAClBnE,OAAA;QAEEoE,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAAC+D,KAAK,CAAE;QACrCzB,KAAK,EAAE;UACL2B,OAAO,EAAE,UAAU;UACnBC,MAAM,EAAEnE,WAAW,KAAKgE,KAAK,GAAG,mBAAmB,GAAG,gBAAgB;UACtEX,YAAY,EAAE,MAAM;UACpBD,eAAe,EAAEpD,WAAW,KAAKgE,KAAK,GAAG,SAAS,GAAG,OAAO;UAC5DpB,KAAK,EAAE5C,WAAW,KAAKgE,KAAK,GAAG,OAAO,GAAG,MAAM;UAC/CI,MAAM,EAAE,SAAS;UACjBvB,QAAQ,EAAE;QACZ,CAAE;QAAAJ,QAAA,EAEDuB,KAAK,GAAG;MAAC,GAZLA,KAAK;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpD,OAAA;MAAK0C,KAAK,EAAE;QACVa,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfG,SAAS,EAAE,8BAA8B;QACzC1B,YAAY,EAAE;MAChB,CAAE;MAAAF,QAAA,gBACA5C,OAAA;QAAI0C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EACnDzB,KAAK,CAAChB,WAAW,CAAC,CAACiB;MAAK;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAELpD,OAAA;QAAG0C,KAAK,EAAE;UAAEK,KAAK,EAAE,MAAM;UAAE0B,UAAU,EAAE,KAAK;UAAE3B,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClEzB,KAAK,CAAChB,WAAW,CAAC,CAACkB;MAAO;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAGJpD,OAAA;QAAK0C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE,MAAM;UACpBH,UAAU,EAAE,6BAA6B;UACzCK,QAAQ,EAAE,MAAM;UAChBU,QAAQ,EAAE;QACZ,CAAE;QAAAd,QAAA,eACA5C,OAAA;UAAK0C,KAAK,EAAE;YAAEY,MAAM,EAAE,CAAC;YAAEoB,UAAU,EAAE;UAAW,CAAE;UAAA9B,QAAA,EAC/CzB,KAAK,CAAChB,WAAW,CAAC,CAACmB;QAAI;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLjD,WAAW,KAAK,CAAC,iBAChBH,OAAA;QAAK0C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,MAAM;UACpBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACA5C,OAAA;UAAI0C,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAkC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAG9FpD,OAAA;UAAK0C,KAAK,EAAE;YAAEI,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBACnC5C,OAAA;YAAK0C,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,KAAK;cAAED,cAAc,EAAE,QAAQ;cAAEhB,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EACzFvC,SAAS,CAAC4D,GAAG,CAAC,CAACU,GAAG,EAAER,KAAK,kBACxBnE,OAAA;cAEE0C,KAAK,EAAE;gBACLiB,KAAK,EAAE,MAAM;gBACbF,MAAM,EAAE,MAAM;gBACdI,OAAO,EAAE,MAAM;gBACfe,UAAU,EAAE,QAAQ;gBACpBd,cAAc,EAAE,QAAQ;gBACxBP,eAAe,EACbY,KAAK,IAAI1D,WAAW,IAAI0D,KAAK,IAAIxD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACpEoC,KAAK,EAAGoB,KAAK,IAAI1D,WAAW,IAAI0D,KAAK,IAAIxD,SAAS,GAAI,OAAO,GAAG,MAAM;gBACtE6C,YAAY,EAAE,KAAK;gBACnBqB,UAAU,EAAE,MAAM;gBAClBP,MAAM,EAAE,WAAW;gBACnBQ,WAAW,EACTX,KAAK,IAAI1D,WAAW,IAAI0D,KAAK,IAAIxD,SAAS,GAAG,SAAS,GAAG,SAAS;gBACpEoE,QAAQ,EAAE;cACZ,CAAE;cAAAnC,QAAA,GAED+B,GAAG,EACHR,KAAK,KAAK1D,WAAW,iBACpBT,OAAA;gBAAK0C,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN,EACAe,KAAK,KAAKxD,SAAS,iBAClBX,OAAA;gBAAK0C,KAAK,EAAE;kBACVqC,QAAQ,EAAE,UAAU;kBACpBC,GAAG,EAAE,OAAO;kBACZhC,QAAQ,EAAE,MAAM;kBAChBD,KAAK,EAAE,SAAS;kBAChB8B,UAAU,EAAE;gBACd,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GAxCIe,KAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyCP,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENpD,OAAA;YAAK0C,KAAK,EAAE;cAAEG,SAAS,EAAE,QAAQ;cAAEG,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBACnE5C,OAAA;cAAA4C,QAAA,GAAM,eAAa,EAACrC,UAAU;YAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrC,KAAK,eACNpD,OAAA;cAAA4C,QAAA,GAAM,eAAa,EAAC7B,UAAU;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrC,KAAK,eACNpD,OAAA;cAAM0C,KAAK,EAAE;gBAAEK,KAAK,EAAE,SAAS;gBAAE8B,UAAU,EAAE;cAAO,CAAE;cAAAjC,QAAA,GAAC,WAAS,EAAC3B,MAAM;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpD,OAAA;UAAK0C,KAAK,EAAE;YAAEG,SAAS,EAAE,QAAQ;YAAEkB,GAAG,EAAE,MAAM;YAAEF,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAS,CAAE;UAAAlB,QAAA,gBAC1F5C,OAAA;YACEoE,OAAO,EAAEzC,OAAQ;YACjBsD,QAAQ,EAAEpE,SAAU;YACpB6B,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE1D,SAAS,GAAG,aAAa,GAAG,SAAS;cAC7CqE,OAAO,EAAErE,SAAS,GAAG,GAAG,GAAG;YAC7B,CAAE;YAAA+B,QAAA,EAED/B,SAAS,GAAG,eAAe,GAAG;UAAa;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAETpD,OAAA;YACEoE,OAAO,EAAE5B,SAAU;YACnBE,KAAK,EAAE;cACLa,eAAe,EAAE,SAAS;cAC1BR,KAAK,EAAE,OAAO;cACduB,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBb,YAAY,EAAE,KAAK;cACnBe,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDpD,OAAA;QAAK0C,KAAK,EAAE;UACVa,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3Bd,YAAY,EAAE,KAAK;UACnBa,OAAO,EAAE,MAAM;UACfvB,YAAY,EAAE;QAChB,CAAE;QAAAF,QAAA,gBACA5C,OAAA;UAAI0C,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE;UAAa,CAAE;UAAAV,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpD,OAAA;UAAG0C,KAAK,EAAE;YAAEK,KAAK,EAAE,SAAS;YAAEO,MAAM,EAAE,YAAY;YAAE6B,SAAS,EAAE;UAAS,CAAE;UAAAvC,QAAA,EACvEzB,KAAK,CAAChB,WAAW,CAAC,CAACoB;QAAc;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJpD,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YAAS0C,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEwB,MAAM,EAAE,SAAS;cAAEM,UAAU,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAAC;UAE7E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACVpD,OAAA;YAAG0C,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE,YAAY;cAAE8B,WAAW,EAAE;YAAO,CAAE;YAAAxC,QAAA,EACvEzB,KAAK,CAAChB,WAAW,CAAC,CAACqB;UAAM;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAGNpD,OAAA;QAAK0C,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEuB,SAAS,EAAE;QAAO,CAAE;QAAAzC,QAAA,gBAClF5C,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAACmC,IAAI,CAACN,GAAG,CAAC,CAAC,EAAE9B,WAAW,GAAG,CAAC,CAAC,CAAE;UAC5D8E,QAAQ,EAAE9E,WAAW,KAAK,CAAE;UAC5BuC,KAAK,EAAE;YACLa,eAAe,EAAEpD,WAAW,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAC1D4C,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEpE,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;UAC9C,CAAE;UAAAyC,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETpD,OAAA;UACEoE,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAACmC,IAAI,CAAC+C,GAAG,CAACnE,KAAK,CAACmB,MAAM,GAAG,CAAC,EAAEnC,WAAW,GAAG,CAAC,CAAC,CAAE;UAC3E8E,QAAQ,EAAE9E,WAAW,KAAKgB,KAAK,CAACmB,MAAM,GAAG,CAAE;UAC3CI,KAAK,EAAE;YACLa,eAAe,EAAEpD,WAAW,KAAKgB,KAAK,CAACmB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YACzES,KAAK,EAAE,OAAO;YACduB,MAAM,EAAE,MAAM;YACdD,OAAO,EAAE,WAAW;YACpBb,YAAY,EAAE,KAAK;YACnBe,MAAM,EAAEpE,WAAW,KAAKgB,KAAK,CAACmB,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG;UAC7D,CAAE;UAAAM,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MAAK0C,KAAK,EAAE;QACVa,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,MAAM;QACpBa,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE;MACV,CAAE;MAAA1B,QAAA,gBACA5C,OAAA;QAAI0C,KAAK,EAAE;UAAEK,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAEvD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELpD,OAAA;QAAK0C,KAAK,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAE0B,mBAAmB,EAAE,sCAAsC;UAAExB,GAAG,EAAE;QAAO,CAAE;QAAAnB,QAAA,EACvG,CACC;UAAE4C,IAAI,EAAE,gCAAgC;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EAC9E;UAAEF,IAAI,EAAE,qCAAqC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACrF;UAAEF,IAAI,EAAE,0BAA0B;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,EACxE;UAAEF,IAAI,EAAE,uBAAuB;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACvE;UAAEF,IAAI,EAAE,mCAAmC;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,EACnF;UAAEF,IAAI,EAAE,wBAAwB;UAAEC,UAAU,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC,CACvE,CAACzB,GAAG,CAAC,CAAC0B,OAAO,EAAExB,KAAK,kBACnBnE,OAAA;UAEE0C,KAAK,EAAE;YACLa,eAAe,EAAE,OAAO;YACxBc,OAAO,EAAE,MAAM;YACfb,YAAY,EAAE,KAAK;YACnBc,MAAM,EAAE;UACV,CAAE;UAAA1B,QAAA,gBAEF5C,OAAA;YAAI0C,KAAK,EAAE;cAAEK,KAAK,EAAE,SAAS;cAAEO,MAAM,EAAE;YAAY,CAAE;YAAAV,QAAA,EAAE+C,OAAO,CAACH;UAAI;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzEpD,OAAA;YAAK0C,KAAK,EAAE;cAAEM,QAAQ,EAAE,MAAM;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAH,QAAA,gBAC9C5C,OAAA;cAAM0C,KAAK,EAAE;gBACXK,KAAK,EAAE4C,OAAO,CAACF,UAAU,KAAK,MAAM,GAAG,SAAS,GAC1CE,OAAO,CAACF,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG;cACtD,CAAE;cAAA7C,QAAA,EACC+C,OAAO,CAACF;YAAU;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACN,KAAK,eACNpD,OAAA;cAAA4C,QAAA,EAAO+C,OAAO,CAACD;YAAI;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC;QAAA,GAlBDe,KAAK;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClD,EAAA,CAvjBQD,oBAAoB;AAAA2F,EAAA,GAApB3F,oBAAoB;AAyjB7B,eAAeA,oBAAoB;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}