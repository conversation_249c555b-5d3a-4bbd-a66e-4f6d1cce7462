{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\CQRSPattern.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CQRSPattern() {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [showDemo, setShowDemo] = useState(false);\n  const [demoOutput, setDemoOutput] = useState([]);\n  const implementationSteps = [{\n    title: \"Phase 1: Foundation Setup (15 min)\",\n    description: \"Define Core Interfaces\",\n    code: `// Command Interface - Represents intent to change state\npublic interface ICommand\n{\n    Guid Id { get; set; }\n}\n\n// Query Interface - Represents intent to read data  \npublic interface IQuery<TResult>\n{\n}\n\n// Event Interface - Represents something that happened\npublic interface IEvent\n{\n    Guid Id { get; set; }\n    DateTime Timestamp { get; set; }\n    string EventType { get; }\n    string Data { get; }\n}`,\n    prompt: \"Why do we separate Commands and Queries into different interfaces?\",\n    answer: \"Different responsibilities and optimization needs - Commands change state and focus on consistency, Queries read state and focus on performance.\"\n  }, {\n    title: \"Phase 2: Command Side (20 min)\",\n    description: \"Implement Command Handlers with Business Logic\",\n    code: `public class CreateProductCommand : ICommand\n{\n    public Guid Id { get; set; }\n    public string Name { get; set; }\n    public decimal Price { get; set; }\n    public string Category { get; set; }\n}\n\npublic class ProductCommandHandler\n{\n    private readonly EventStore _eventStore;\n\n    public void Handle(CreateProductCommand command)\n    {\n        // 1. Validate business rules\n        if (string.IsNullOrEmpty(command.Name))\n            throw new ArgumentException(\"Product name is required\");\n        \n        // 2. Generate event\n        var productCreatedEvent = new ProductCreatedEvent\n        {\n            Id = command.Id,\n            Name = command.Name,\n            Price = command.Price,\n            Category = command.Category\n        };\n\n        // 3. Store event\n        _eventStore.SaveEvent(productCreatedEvent);\n    }\n}`,\n    prompt: \"Where should business logic and validation live in CQRS?\",\n    answer: \"In Command Handlers - they are the 'brain' of the write side, validate commands, enforce business rules, and generate events.\"\n  }, {\n    title: \"Phase 3: Event Sourcing (15 min)\",\n    description: \"Create Event Store for Audit Trail\",\n    code: `public class ProductCreatedEvent : IEvent\n{\n    public Guid Id { get; set; }\n    public string Name { get; set; }\n    public decimal Price { get; set; }\n    public string Category { get; set; }\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\n    public string EventType => \"ProductCreated\";\n    public string Data => $\"Name: {Name}, Price: {Price}\";\n}\n\npublic class EventStore\n{\n    private readonly List<IEvent> _events = new List<IEvent>();\n    public event Action<IEvent> EventPublished;\n\n    public void SaveEvent(IEvent eventToSave)\n    {\n        _events.Add(eventToSave);\n        EventPublished?.Invoke(eventToSave);\n    }\n}`,\n    prompt: \"What role does Event Sourcing play in CQRS?\",\n    answer: \"Provides complete audit trail, ability to replay events, temporal queries, and decouples command and query sides.\"\n  }, {\n    title: \"Phase 4: Query Side (20 min)\",\n    description: \"Build Read Models Optimized for Queries\",\n    code: `public class ProductReadModel\n{\n    public Guid Id { get; set; }\n    public string Name { get; set; }\n    public decimal Price { get; set; }\n    public string Category { get; set; }\n    public DateTime CreatedAt { get; set; }\n    public int ViewCount { get; set; } // Denormalized data\n}\n\npublic class ProductQueryHandler\n{\n    private readonly ReadDatabase _readDatabase;\n\n    public IEnumerable<ProductReadModel> Handle(GetAllProductsQuery query)\n    {\n        return _readDatabase.GetAllProducts();\n    }\n}`,\n    prompt: \"How are Read Models different from Write Models?\",\n    answer: \"Read Models are optimized for specific queries, can be denormalized for performance, support multiple views, and contain no business logic.\"\n  }, {\n    title: \"Phase 5: Event Handling (15 min)\",\n    description: \"Update Read Models via Event Handlers\",\n    code: `public class ProductEventHandler\n{\n    private readonly ReadDatabase _readDatabase;\n\n    public void Handle(IEvent eventToHandle)\n    {\n        switch (eventToHandle)\n        {\n            case ProductCreatedEvent productCreated:\n                HandleProductCreated(productCreated);\n                break;\n        }\n    }\n\n    private void HandleProductCreated(ProductCreatedEvent eventData)\n    {\n        var readModel = new ProductReadModel\n        {\n            Id = eventData.Id,\n            Name = eventData.Name,\n            Price = eventData.Price,\n            Category = eventData.Category,\n            CreatedAt = eventData.Timestamp,\n            ViewCount = 0\n        };\n\n        _readDatabase.AddProduct(readModel);\n    }\n}`,\n    prompt: \"How do Event Handlers maintain consistency?\",\n    answer: \"They listen to events from command side, update read models accordingly, handle eventual consistency, and can implement retry mechanisms.\"\n  }, {\n    title: \"Phase 6: Integration (10 min)\",\n    description: \"Wire Everything Together\",\n    code: `public void SetupCQRS()\n{\n    // Initialize components\n    var eventStore = new EventStore();\n    var readDatabase = new ReadDatabase();\n    var commandHandler = new ProductCommandHandler(eventStore);\n    var queryHandler = new ProductQueryHandler(readDatabase);\n    var eventHandler = new ProductEventHandler(readDatabase);\n\n    // Wire event handling\n    eventStore.EventPublished += eventHandler.Handle;\n}`,\n    prompt: \"What's the typical flow in a CQRS system?\",\n    answer: \"Command Handler processes command → generates event → Event Handler updates read model → Query Handler reads optimized data.\"\n  }];\n  const runDemo = () => {\n    setShowDemo(true);\n    const output = [\"🏗️ CQRS Architecture Components:\", \"   📝 Command Handler: Processes business operations\", \"   📖 Query Handler: Retrieves optimized data views\", \"   📚 Event Store: Maintains complete audit trail\", \"   🔄 Event Handler: Updates read models\", \"\", \"🚀 Executing CQRS Operations:\", \"\", \"📝 Creating: Gaming Laptop ($1299.99)\", \"   📝 Event saved: ProductCreated\", \"   📖 Read model updated: Product Gaming Laptop added\", \"   ✅ Product created successfully\", \"\", \"📝 Creating: Wireless Mouse ($49.99)\", \"   📝 Event saved: ProductCreated\", \"   📖 Read model updated: Product Wireless Mouse added\", \"   ✅ Product created successfully\", \"\", \"📝 Updating price: $1199.99 - Black Friday Sale\", \"   📝 Event saved: ProductPriceUpdated\", \"   📖 Read model updated: Product Gaming Laptop modified\", \"   ✅ Product price updated successfully\", \"\", \"📖 Querying all products:\", \"   • Gaming Laptop: $1199.99 (Electronics)\", \"   • Wireless Mouse: $49.99 (Electronics)\", \"\", \"📚 Event History:\", \"   🔸 ProductCreated at 17:24:11\", \"   🔸 ProductCreated at 17:24:11\", \"   🔸 ProductPriceUpdated at 17:24:11\"];\n    setDemoOutput(output);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      maxWidth: '1200px',\n      margin: '0 auto',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#2c3e50',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83C\\uDFAF CQRS Pattern Implementation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em'\n        },\n        children: \"Command Query Responsibility Segregation - Step-by-Step Learning Guide\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#e74c3c',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83D\\uDCCB Implementation Sequence\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '10px',\n          marginBottom: '20px'\n        },\n        children: implementationSteps.map((step, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setCurrentStep(index),\n          style: {\n            padding: '10px 15px',\n            border: 'none',\n            borderRadius: '5px',\n            backgroundColor: currentStep === index ? '#3498db' : '#ecf0f1',\n            color: currentStep === index ? 'white' : '#2c3e50',\n            cursor: 'pointer',\n            fontSize: '14px',\n            fontWeight: 'bold'\n          },\n          children: [\"Phase \", index + 1]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fff',\n          padding: '25px',\n          borderRadius: '8px',\n          border: '1px solid #ddd'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#8e44ad',\n            marginBottom: '15px'\n          },\n          children: implementationSteps[currentStep].title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#7f8c8d',\n            marginBottom: '20px',\n            fontSize: '16px'\n          },\n          children: implementationSteps[currentStep].description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#f8f9fa',\n            padding: '15px',\n            borderRadius: '5px',\n            marginBottom: '20px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            style: {\n              margin: 0,\n              fontSize: '14px',\n              lineHeight: '1.4',\n              overflow: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"code\", {\n              children: implementationSteps[currentStep].code\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#e8f5e8',\n            padding: '15px',\n            borderRadius: '5px',\n            border: '1px solid #27ae60'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#27ae60',\n              margin: '0 0 10px 0'\n            },\n            children: \"\\uD83C\\uDF93 Learning Prompt:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0 0 10px 0',\n              fontWeight: 'bold',\n              color: '#2c3e50'\n            },\n            children: implementationSteps[currentStep].prompt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: '#27ae60',\n              fontSize: '14px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), \" \", implementationSteps[currentStep].answer]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#e74c3c',\n          marginBottom: '20px'\n        },\n        children: \"\\uD83D\\uDE80 Live Demo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: runDemo,\n          style: {\n            padding: '15px 30px',\n            fontSize: '16px',\n            fontWeight: 'bold',\n            backgroundColor: '#27ae60',\n            color: 'white',\n            border: 'none',\n            borderRadius: '5px',\n            cursor: 'pointer'\n          },\n          children: \"Run CQRS Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), showDemo && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#2c3e50',\n          color: '#ecf0f1',\n          padding: '20px',\n          borderRadius: '8px',\n          fontFamily: 'monospace'\n        },\n        children: demoOutput.map((line, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '5px'\n          },\n          children: line\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#e74c3c',\n          marginBottom: '20px'\n        },\n        children: \"\\u2705 CQRS Benefits\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#fff',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #ddd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#3498db',\n              marginBottom: '15px'\n            },\n            children: \"\\uD83C\\uDFAF Independent Scaling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Read side scales independently of write side\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Use different databases for reads vs writes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Optimize each side for specific needs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#fff',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #ddd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#3498db',\n              marginBottom: '15px'\n            },\n            children: \"\\u26A1 Performance Optimization\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Read models optimized for specific queries\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Denormalized data for faster reads\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"No complex joins needed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#fff',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #ddd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#3498db',\n              marginBottom: '15px'\n            },\n            children: \"\\uD83D\\uDCDA Complete Audit Trail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Complete history of all changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Ability to replay events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Temporal queries support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f8f9fa',\n        padding: '20px',\n        borderRadius: '8px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#e74c3c',\n          marginBottom: '15px'\n        },\n        children: \"\\uD83C\\uDFAF When to Use CQRS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: '1fr 1fr',\n          gap: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#27ae60',\n              marginBottom: '10px'\n            },\n            children: \"\\u2705 Good Fit:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Complex business domains\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"High read-to-write ratios\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Need for multiple specialized views\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Audit trail requirements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Different performance needs for reads vs writes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: '#e74c3c',\n              marginBottom: '10px'\n            },\n            children: \"\\u274C Avoid When:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              lineHeight: '1.6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Simple CRUD applications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Small team/project size\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Low complexity domains\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Immediate consistency required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Limited infrastructure resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#ecf0f1',\n        padding: '20px',\n        borderRadius: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: '#2c3e50',\n          marginBottom: '15px'\n        },\n        children: \"\\uD83D\\uDE80 Next Steps for Production\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '15px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#8e44ad',\n              margin: '0 0 10px 0'\n            },\n            children: \"Infrastructure:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              margin: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Add Dependency Injection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Implement Message Bus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Add Persistence Layers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#8e44ad',\n              margin: '0 0 10px 0'\n            },\n            children: \"Quality:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              margin: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Error Handling & Retry Logic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Monitoring & Logging\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Comprehensive Unit Tests\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              color: '#8e44ad',\n              margin: '0 0 10px 0'\n            },\n            children: \"Frameworks:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              fontSize: '14px',\n              margin: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Consider MediatR for .NET\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Event Store databases\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Message queues (RabbitMQ, etc.)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n}\n_s(CQRSPattern, \"1UZqX0s5dVy4e16IBCVACtXuTa0=\");\n_c = CQRSPattern;\nexport default CQRSPattern;\nvar _c;\n$RefreshReg$(_c, \"CQRSPattern\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "CQRSPattern", "_s", "currentStep", "setCurrentStep", "showDemo", "setShowDemo", "demoOutput", "setDemoOutput", "implementationSteps", "title", "description", "code", "prompt", "answer", "runDemo", "output", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "fontFamily", "children", "textAlign", "marginBottom", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexWrap", "gap", "map", "step", "index", "onClick", "border", "borderRadius", "backgroundColor", "cursor", "fontWeight", "lineHeight", "overflow", "line", "gridTemplateColumns", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/CQRSPattern.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nfunction CQRSPattern() {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [showDemo, setShowDemo] = useState(false);\n  const [demoOutput, setDemoOutput] = useState([]);\n\n  const implementationSteps = [\n    {\n      title: \"Phase 1: Foundation Setup (15 min)\",\n      description: \"Define Core Interfaces\",\n      code: `// Command Interface - Represents intent to change state\npublic interface ICommand\n{\n    Guid Id { get; set; }\n}\n\n// Query Interface - Represents intent to read data  \npublic interface IQuery<TResult>\n{\n}\n\n// Event Interface - Represents something that happened\npublic interface IEvent\n{\n    Guid Id { get; set; }\n    DateTime Timestamp { get; set; }\n    string EventType { get; }\n    string Data { get; }\n}`,\n      prompt: \"Why do we separate Commands and Queries into different interfaces?\",\n      answer: \"Different responsibilities and optimization needs - Commands change state and focus on consistency, Queries read state and focus on performance.\"\n    },\n    {\n      title: \"Phase 2: Command Side (20 min)\",\n      description: \"Implement Command Handlers with Business Logic\",\n      code: `public class CreateProductCommand : ICommand\n{\n    public Guid Id { get; set; }\n    public string Name { get; set; }\n    public decimal Price { get; set; }\n    public string Category { get; set; }\n}\n\npublic class ProductCommandHandler\n{\n    private readonly EventStore _eventStore;\n\n    public void Handle(CreateProductCommand command)\n    {\n        // 1. Validate business rules\n        if (string.IsNullOrEmpty(command.Name))\n            throw new ArgumentException(\"Product name is required\");\n        \n        // 2. Generate event\n        var productCreatedEvent = new ProductCreatedEvent\n        {\n            Id = command.Id,\n            Name = command.Name,\n            Price = command.Price,\n            Category = command.Category\n        };\n\n        // 3. Store event\n        _eventStore.SaveEvent(productCreatedEvent);\n    }\n}`,\n      prompt: \"Where should business logic and validation live in CQRS?\",\n      answer: \"In Command Handlers - they are the 'brain' of the write side, validate commands, enforce business rules, and generate events.\"\n    },\n    {\n      title: \"Phase 3: Event Sourcing (15 min)\",\n      description: \"Create Event Store for Audit Trail\",\n      code: `public class ProductCreatedEvent : IEvent\n{\n    public Guid Id { get; set; }\n    public string Name { get; set; }\n    public decimal Price { get; set; }\n    public string Category { get; set; }\n    public DateTime Timestamp { get; set; } = DateTime.UtcNow;\n    public string EventType => \"ProductCreated\";\n    public string Data => $\"Name: {Name}, Price: {Price}\";\n}\n\npublic class EventStore\n{\n    private readonly List<IEvent> _events = new List<IEvent>();\n    public event Action<IEvent> EventPublished;\n\n    public void SaveEvent(IEvent eventToSave)\n    {\n        _events.Add(eventToSave);\n        EventPublished?.Invoke(eventToSave);\n    }\n}`,\n      prompt: \"What role does Event Sourcing play in CQRS?\",\n      answer: \"Provides complete audit trail, ability to replay events, temporal queries, and decouples command and query sides.\"\n    },\n    {\n      title: \"Phase 4: Query Side (20 min)\",\n      description: \"Build Read Models Optimized for Queries\",\n      code: `public class ProductReadModel\n{\n    public Guid Id { get; set; }\n    public string Name { get; set; }\n    public decimal Price { get; set; }\n    public string Category { get; set; }\n    public DateTime CreatedAt { get; set; }\n    public int ViewCount { get; set; } // Denormalized data\n}\n\npublic class ProductQueryHandler\n{\n    private readonly ReadDatabase _readDatabase;\n\n    public IEnumerable<ProductReadModel> Handle(GetAllProductsQuery query)\n    {\n        return _readDatabase.GetAllProducts();\n    }\n}`,\n      prompt: \"How are Read Models different from Write Models?\",\n      answer: \"Read Models are optimized for specific queries, can be denormalized for performance, support multiple views, and contain no business logic.\"\n    },\n    {\n      title: \"Phase 5: Event Handling (15 min)\",\n      description: \"Update Read Models via Event Handlers\",\n      code: `public class ProductEventHandler\n{\n    private readonly ReadDatabase _readDatabase;\n\n    public void Handle(IEvent eventToHandle)\n    {\n        switch (eventToHandle)\n        {\n            case ProductCreatedEvent productCreated:\n                HandleProductCreated(productCreated);\n                break;\n        }\n    }\n\n    private void HandleProductCreated(ProductCreatedEvent eventData)\n    {\n        var readModel = new ProductReadModel\n        {\n            Id = eventData.Id,\n            Name = eventData.Name,\n            Price = eventData.Price,\n            Category = eventData.Category,\n            CreatedAt = eventData.Timestamp,\n            ViewCount = 0\n        };\n\n        _readDatabase.AddProduct(readModel);\n    }\n}`,\n      prompt: \"How do Event Handlers maintain consistency?\",\n      answer: \"They listen to events from command side, update read models accordingly, handle eventual consistency, and can implement retry mechanisms.\"\n    },\n    {\n      title: \"Phase 6: Integration (10 min)\",\n      description: \"Wire Everything Together\",\n      code: `public void SetupCQRS()\n{\n    // Initialize components\n    var eventStore = new EventStore();\n    var readDatabase = new ReadDatabase();\n    var commandHandler = new ProductCommandHandler(eventStore);\n    var queryHandler = new ProductQueryHandler(readDatabase);\n    var eventHandler = new ProductEventHandler(readDatabase);\n\n    // Wire event handling\n    eventStore.EventPublished += eventHandler.Handle;\n}`,\n      prompt: \"What's the typical flow in a CQRS system?\",\n      answer: \"Command Handler processes command → generates event → Event Handler updates read model → Query Handler reads optimized data.\"\n    }\n  ];\n\n  const runDemo = () => {\n    setShowDemo(true);\n    const output = [\n      \"🏗️ CQRS Architecture Components:\",\n      \"   📝 Command Handler: Processes business operations\",\n      \"   📖 Query Handler: Retrieves optimized data views\",\n      \"   📚 Event Store: Maintains complete audit trail\",\n      \"   🔄 Event Handler: Updates read models\",\n      \"\",\n      \"🚀 Executing CQRS Operations:\",\n      \"\",\n      \"📝 Creating: Gaming Laptop ($1299.99)\",\n      \"   📝 Event saved: ProductCreated\",\n      \"   📖 Read model updated: Product Gaming Laptop added\",\n      \"   ✅ Product created successfully\",\n      \"\",\n      \"📝 Creating: Wireless Mouse ($49.99)\",\n      \"   📝 Event saved: ProductCreated\", \n      \"   📖 Read model updated: Product Wireless Mouse added\",\n      \"   ✅ Product created successfully\",\n      \"\",\n      \"📝 Updating price: $1199.99 - Black Friday Sale\",\n      \"   📝 Event saved: ProductPriceUpdated\",\n      \"   📖 Read model updated: Product Gaming Laptop modified\",\n      \"   ✅ Product price updated successfully\",\n      \"\",\n      \"📖 Querying all products:\",\n      \"   • Gaming Laptop: $1199.99 (Electronics)\",\n      \"   • Wireless Mouse: $49.99 (Electronics)\",\n      \"\",\n      \"📚 Event History:\",\n      \"   🔸 ProductCreated at 17:24:11\",\n      \"   🔸 ProductCreated at 17:24:11\", \n      \"   🔸 ProductPriceUpdated at 17:24:11\"\n    ];\n    setDemoOutput(output);\n  };\n\n  return (\n    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto', fontFamily: 'Arial, sans-serif' }}>\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#2c3e50', fontSize: '2.5em', marginBottom: '10px' }}>\n          🎯 CQRS Pattern Implementation\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em' }}>\n          Command Query Responsibility Segregation - Step-by-Step Learning Guide\n        </p>\n      </div>\n\n      {/* Implementation Steps */}\n      <div style={{ marginBottom: '30px' }}>\n        <h2 style={{ color: '#e74c3c', marginBottom: '20px' }}>📋 Implementation Sequence</h2>\n        \n        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginBottom: '20px' }}>\n          {implementationSteps.map((step, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentStep(index)}\n              style={{\n                padding: '10px 15px',\n                border: 'none',\n                borderRadius: '5px',\n                backgroundColor: currentStep === index ? '#3498db' : '#ecf0f1',\n                color: currentStep === index ? 'white' : '#2c3e50',\n                cursor: 'pointer',\n                fontSize: '14px',\n                fontWeight: 'bold'\n              }}\n            >\n              Phase {index + 1}\n            </button>\n          ))}\n        </div>\n\n        <div style={{ backgroundColor: '#fff', padding: '25px', borderRadius: '8px', border: '1px solid #ddd' }}>\n          <h3 style={{ color: '#8e44ad', marginBottom: '15px' }}>\n            {implementationSteps[currentStep].title}\n          </h3>\n          <p style={{ color: '#7f8c8d', marginBottom: '20px', fontSize: '16px' }}>\n            {implementationSteps[currentStep].description}\n          </p>\n          \n          <div style={{ backgroundColor: '#f8f9fa', padding: '15px', borderRadius: '5px', marginBottom: '20px' }}>\n            <pre style={{ margin: 0, fontSize: '14px', lineHeight: '1.4', overflow: 'auto' }}>\n              <code>{implementationSteps[currentStep].code}</code>\n            </pre>\n          </div>\n\n          <div style={{ backgroundColor: '#e8f5e8', padding: '15px', borderRadius: '5px', border: '1px solid #27ae60' }}>\n            <h4 style={{ color: '#27ae60', margin: '0 0 10px 0' }}>\n              🎓 Learning Prompt:\n            </h4>\n            <p style={{ margin: '0 0 10px 0', fontWeight: 'bold', color: '#2c3e50' }}>\n              {implementationSteps[currentStep].prompt}\n            </p>\n            <p style={{ margin: 0, color: '#27ae60', fontSize: '14px' }}>\n              <strong>Answer:</strong> {implementationSteps[currentStep].answer}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Demo Section */}\n      <div style={{ marginBottom: '30px' }}>\n        <h2 style={{ color: '#e74c3c', marginBottom: '20px' }}>🚀 Live Demo</h2>\n        \n        <div style={{ textAlign: 'center', marginBottom: '20px' }}>\n          <button\n            onClick={runDemo}\n            style={{\n              padding: '15px 30px',\n              fontSize: '16px',\n              fontWeight: 'bold',\n              backgroundColor: '#27ae60',\n              color: 'white',\n              border: 'none',\n              borderRadius: '5px',\n              cursor: 'pointer'\n            }}\n          >\n            Run CQRS Demo\n          </button>\n        </div>\n\n        {showDemo && (\n          <div style={{ backgroundColor: '#2c3e50', color: '#ecf0f1', padding: '20px', borderRadius: '8px', fontFamily: 'monospace' }}>\n            {demoOutput.map((line, index) => (\n              <div key={index} style={{ marginBottom: '5px' }}>\n                {line}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Benefits Section */}\n      <div style={{ marginBottom: '30px' }}>\n        <h2 style={{ color: '#e74c3c', marginBottom: '20px' }}>✅ CQRS Benefits</h2>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>\n          <div style={{ backgroundColor: '#fff', padding: '20px', borderRadius: '8px', border: '1px solid #ddd' }}>\n            <h3 style={{ color: '#3498db', marginBottom: '15px' }}>🎯 Independent Scaling</h3>\n            <ul style={{ fontSize: '14px', lineHeight: '1.6' }}>\n              <li>Read side scales independently of write side</li>\n              <li>Use different databases for reads vs writes</li>\n              <li>Optimize each side for specific needs</li>\n            </ul>\n          </div>\n          \n          <div style={{ backgroundColor: '#fff', padding: '20px', borderRadius: '8px', border: '1px solid #ddd' }}>\n            <h3 style={{ color: '#3498db', marginBottom: '15px' }}>⚡ Performance Optimization</h3>\n            <ul style={{ fontSize: '14px', lineHeight: '1.6' }}>\n              <li>Read models optimized for specific queries</li>\n              <li>Denormalized data for faster reads</li>\n              <li>No complex joins needed</li>\n            </ul>\n          </div>\n          \n          <div style={{ backgroundColor: '#fff', padding: '20px', borderRadius: '8px', border: '1px solid #ddd' }}>\n            <h3 style={{ color: '#3498db', marginBottom: '15px' }}>📚 Complete Audit Trail</h3>\n            <ul style={{ fontSize: '14px', lineHeight: '1.6' }}>\n              <li>Complete history of all changes</li>\n              <li>Ability to replay events</li>\n              <li>Temporal queries support</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      {/* When to Use */}\n      <div style={{ backgroundColor: '#f8f9fa', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>\n        <h2 style={{ color: '#e74c3c', marginBottom: '15px' }}>🎯 When to Use CQRS</h2>\n        \n        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>\n          <div>\n            <h3 style={{ color: '#27ae60', marginBottom: '10px' }}>✅ Good Fit:</h3>\n            <ul style={{ fontSize: '14px', lineHeight: '1.6' }}>\n              <li>Complex business domains</li>\n              <li>High read-to-write ratios</li>\n              <li>Need for multiple specialized views</li>\n              <li>Audit trail requirements</li>\n              <li>Different performance needs for reads vs writes</li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 style={{ color: '#e74c3c', marginBottom: '10px' }}>❌ Avoid When:</h3>\n            <ul style={{ fontSize: '14px', lineHeight: '1.6' }}>\n              <li>Simple CRUD applications</li>\n              <li>Small team/project size</li>\n              <li>Low complexity domains</li>\n              <li>Immediate consistency required</li>\n              <li>Limited infrastructure resources</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      {/* Next Steps */}\n      <div style={{ backgroundColor: '#ecf0f1', padding: '20px', borderRadius: '8px' }}>\n        <h2 style={{ color: '#2c3e50', marginBottom: '15px' }}>🚀 Next Steps for Production</h2>\n        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>\n          <div>\n            <h4 style={{ color: '#8e44ad', margin: '0 0 10px 0' }}>Infrastructure:</h4>\n            <ul style={{ fontSize: '14px', margin: 0 }}>\n              <li>Add Dependency Injection</li>\n              <li>Implement Message Bus</li>\n              <li>Add Persistence Layers</li>\n            </ul>\n          </div>\n          <div>\n            <h4 style={{ color: '#8e44ad', margin: '0 0 10px 0' }}>Quality:</h4>\n            <ul style={{ fontSize: '14px', margin: 0 }}>\n              <li>Error Handling & Retry Logic</li>\n              <li>Monitoring & Logging</li>\n              <li>Comprehensive Unit Tests</li>\n            </ul>\n          </div>\n          <div>\n            <h4 style={{ color: '#8e44ad', margin: '0 0 10px 0' }}>Frameworks:</h4>\n            <ul style={{ fontSize: '14px', margin: 0 }}>\n              <li>Consider MediatR for .NET</li>\n              <li>Event Store databases</li>\n              <li>Message queues (RabbitMQ, etc.)</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default CQRSPattern;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMW,mBAAmB,GAAG,CAC1B;IACEC,KAAK,EAAE,oCAAoC;IAC3CC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,MAAM,EAAE,oEAAoE;IAC5EC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,gDAAgD;IAC7DC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,MAAM,EAAE,0DAA0D;IAClEC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,kCAAkC;IACzCC,WAAW,EAAE,oCAAoC;IACjDC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,MAAM,EAAE,6CAA6C;IACrDC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,MAAM,EAAE,kDAAkD;IAC1DC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,kCAAkC;IACzCC,WAAW,EAAE,uCAAuC;IACpDC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,MAAM,EAAE,6CAA6C;IACrDC,MAAM,EAAE;EACV,CAAC,EACD;IACEJ,KAAK,EAAE,+BAA+B;IACtCC,WAAW,EAAE,0BAA0B;IACvCC,IAAI,EAAE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;IACIC,MAAM,EAAE,2CAA2C;IACnDC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpBT,WAAW,CAAC,IAAI,CAAC;IACjB,MAAMU,MAAM,GAAG,CACb,mCAAmC,EACnC,sDAAsD,EACtD,qDAAqD,EACrD,mDAAmD,EACnD,0CAA0C,EAC1C,EAAE,EACF,+BAA+B,EAC/B,EAAE,EACF,uCAAuC,EACvC,mCAAmC,EACnC,uDAAuD,EACvD,mCAAmC,EACnC,EAAE,EACF,sCAAsC,EACtC,mCAAmC,EACnC,wDAAwD,EACxD,mCAAmC,EACnC,EAAE,EACF,iDAAiD,EACjD,wCAAwC,EACxC,0DAA0D,EAC1D,yCAAyC,EACzC,EAAE,EACF,2BAA2B,EAC3B,4CAA4C,EAC5C,2CAA2C,EAC3C,EAAE,EACF,mBAAmB,EACnB,kCAAkC,EAClC,kCAAkC,EAClC,uCAAuC,CACxC;IACDR,aAAa,CAACQ,MAAM,CAAC;EACvB,CAAC;EAED,oBACEhB,OAAA;IAAKiB,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE,QAAQ;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBACrGtB,OAAA;MAAKiB,KAAK,EAAE;QAAEM,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxDtB,OAAA;QAAIiB,KAAK,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE,OAAO;UAAEF,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9B,OAAA;QAAGiB,KAAK,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QAAAJ,QAAA,EAAC;MAEnD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN9B,OAAA;MAAKiB,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnCtB,OAAA;QAAIiB,KAAK,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAA0B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEtF9B,OAAA;QAAKiB,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAET,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClFb,mBAAmB,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnCpC,OAAA;UAEEqC,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAACgC,KAAK,CAAE;UACrCnB,KAAK,EAAE;YACLC,OAAO,EAAE,WAAW;YACpBoB,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAErC,WAAW,KAAKiC,KAAK,GAAG,SAAS,GAAG,SAAS;YAC9DX,KAAK,EAAEtB,WAAW,KAAKiC,KAAK,GAAG,OAAO,GAAG,SAAS;YAClDK,MAAM,EAAE,SAAS;YACjBf,QAAQ,EAAE,MAAM;YAChBgB,UAAU,EAAE;UACd,CAAE;UAAApB,QAAA,GACH,QACO,EAACc,KAAK,GAAG,CAAC;QAAA,GAbXA,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN9B,OAAA;QAAKiB,KAAK,EAAE;UAAEuB,eAAe,EAAE,MAAM;UAAEtB,OAAO,EAAE,MAAM;UAAEqB,YAAY,EAAE,KAAK;UAAED,MAAM,EAAE;QAAiB,CAAE;QAAAhB,QAAA,gBACtGtB,OAAA;UAAIiB,KAAK,EAAE;YAAEQ,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EACnDb,mBAAmB,CAACN,WAAW,CAAC,CAACO;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACL9B,OAAA;UAAGiB,KAAK,EAAE;YAAEQ,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE,MAAM;YAAEE,QAAQ,EAAE;UAAO,CAAE;UAAAJ,QAAA,EACpEb,mBAAmB,CAACN,WAAW,CAAC,CAACQ;QAAW;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAEJ9B,OAAA;UAAKiB,KAAK,EAAE;YAAEuB,eAAe,EAAE,SAAS;YAAEtB,OAAO,EAAE,MAAM;YAAEqB,YAAY,EAAE,KAAK;YAAEf,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,eACrGtB,OAAA;YAAKiB,KAAK,EAAE;cAAEG,MAAM,EAAE,CAAC;cAAEM,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAtB,QAAA,eAC/EtB,OAAA;cAAAsB,QAAA,EAAOb,mBAAmB,CAACN,WAAW,CAAC,CAACS;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9B,OAAA;UAAKiB,KAAK,EAAE;YAAEuB,eAAe,EAAE,SAAS;YAAEtB,OAAO,EAAE,MAAM;YAAEqB,YAAY,EAAE,KAAK;YAAED,MAAM,EAAE;UAAoB,CAAE;UAAAhB,QAAA,gBAC5GtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAEL,MAAM,EAAE;YAAa,CAAE;YAAAE,QAAA,EAAC;UAEvD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9B,OAAA;YAAGiB,KAAK,EAAE;cAAEG,MAAM,EAAE,YAAY;cAAEsB,UAAU,EAAE,MAAM;cAAEjB,KAAK,EAAE;YAAU,CAAE;YAAAH,QAAA,EACtEb,mBAAmB,CAACN,WAAW,CAAC,CAACU;UAAM;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACJ9B,OAAA;YAAGiB,KAAK,EAAE;cAAEG,MAAM,EAAE,CAAC;cAAEK,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,gBAC1DtB,OAAA;cAAAsB,QAAA,EAAQ;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrB,mBAAmB,CAACN,WAAW,CAAC,CAACW,MAAM;UAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKiB,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnCtB,OAAA;QAAIiB,KAAK,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAY;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAExE9B,OAAA;QAAKiB,KAAK,EAAE;UAAEM,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,eACxDtB,OAAA;UACEqC,OAAO,EAAEtB,OAAQ;UACjBE,KAAK,EAAE;YACLC,OAAO,EAAE,WAAW;YACpBQ,QAAQ,EAAE,MAAM;YAChBgB,UAAU,EAAE,MAAM;YAClBF,eAAe,EAAE,SAAS;YAC1Bf,KAAK,EAAE,OAAO;YACda,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBE,MAAM,EAAE;UACV,CAAE;UAAAnB,QAAA,EACH;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELzB,QAAQ,iBACPL,OAAA;QAAKiB,KAAK,EAAE;UAAEuB,eAAe,EAAE,SAAS;UAAEf,KAAK,EAAE,SAAS;UAAEP,OAAO,EAAE,MAAM;UAAEqB,YAAY,EAAE,KAAK;UAAElB,UAAU,EAAE;QAAY,CAAE;QAAAC,QAAA,EACzHf,UAAU,CAAC2B,GAAG,CAAC,CAACW,IAAI,EAAET,KAAK,kBAC1BpC,OAAA;UAAiBiB,KAAK,EAAE;YAAEO,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,EAC7CuB;QAAI,GADGT,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN9B,OAAA;MAAKiB,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnCtB,OAAA;QAAIiB,KAAK,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE3E9B,OAAA;QAAKiB,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEe,mBAAmB,EAAE,sCAAsC;UAAEb,GAAG,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACxGtB,OAAA;UAAKiB,KAAK,EAAE;YAAEuB,eAAe,EAAE,MAAM;YAAEtB,OAAO,EAAE,MAAM;YAAEqB,YAAY,EAAE,KAAK;YAAED,MAAM,EAAE;UAAiB,CAAE;UAAAhB,QAAA,gBACtGtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAAsB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClF9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAM,CAAE;YAAArB,QAAA,gBACjDtB,OAAA;cAAAsB,QAAA,EAAI;YAA4C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD9B,OAAA;cAAAsB,QAAA,EAAI;YAA2C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpD9B,OAAA;cAAAsB,QAAA,EAAI;YAAqC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN9B,OAAA;UAAKiB,KAAK,EAAE;YAAEuB,eAAe,EAAE,MAAM;YAAEtB,OAAO,EAAE,MAAM;YAAEqB,YAAY,EAAE,KAAK;YAAED,MAAM,EAAE;UAAiB,CAAE;UAAAhB,QAAA,gBACtGtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtF9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAM,CAAE;YAAArB,QAAA,gBACjDtB,OAAA;cAAAsB,QAAA,EAAI;YAA0C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD9B,OAAA;cAAAsB,QAAA,EAAI;YAAkC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3C9B,OAAA;cAAAsB,QAAA,EAAI;YAAuB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN9B,OAAA;UAAKiB,KAAK,EAAE;YAAEuB,eAAe,EAAE,MAAM;YAAEtB,OAAO,EAAE,MAAM;YAAEqB,YAAY,EAAE,KAAK;YAAED,MAAM,EAAE;UAAiB,CAAE;UAAAhB,QAAA,gBACtGtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAAuB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAM,CAAE;YAAArB,QAAA,gBACjDtB,OAAA;cAAAsB,QAAA,EAAI;YAA+B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxC9B,OAAA;cAAAsB,QAAA,EAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC9B,OAAA;cAAAsB,QAAA,EAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKiB,KAAK,EAAE;QAAEuB,eAAe,EAAE,SAAS;QAAEtB,OAAO,EAAE,MAAM;QAAEqB,YAAY,EAAE,KAAK;QAAEf,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACrGtB,OAAA;QAAIiB,KAAK,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAAmB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE/E9B,OAAA;QAAKiB,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEe,mBAAmB,EAAE,SAAS;UAAEb,GAAG,EAAE;QAAO,CAAE;QAAAX,QAAA,gBAC3EtB,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvE9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAM,CAAE;YAAArB,QAAA,gBACjDtB,OAAA;cAAAsB,QAAA,EAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC9B,OAAA;cAAAsB,QAAA,EAAI;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9B,OAAA;cAAAsB,QAAA,EAAI;YAAmC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C9B,OAAA;cAAAsB,QAAA,EAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC9B,OAAA;cAAAsB,QAAA,EAAI;YAA+C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN9B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEiB,UAAU,EAAE;YAAM,CAAE;YAAArB,QAAA,gBACjDtB,OAAA;cAAAsB,QAAA,EAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC9B,OAAA;cAAAsB,QAAA,EAAI;YAAuB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChC9B,OAAA;cAAAsB,QAAA,EAAI;YAAsB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/B9B,OAAA;cAAAsB,QAAA,EAAI;YAA8B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvC9B,OAAA;cAAAsB,QAAA,EAAI;YAAgC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9B,OAAA;MAAKiB,KAAK,EAAE;QAAEuB,eAAe,EAAE,SAAS;QAAEtB,OAAO,EAAE,MAAM;QAAEqB,YAAY,EAAE;MAAM,CAAE;MAAAjB,QAAA,gBAC/EtB,OAAA;QAAIiB,KAAK,EAAE;UAAEQ,KAAK,EAAE,SAAS;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAA4B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxF9B,OAAA;QAAKiB,KAAK,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEe,mBAAmB,EAAE,sCAAsC;UAAEb,GAAG,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACxGtB,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAEL,MAAM,EAAE;YAAa,CAAE;YAAAE,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3E9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACzCtB,OAAA;cAAAsB,QAAA,EAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC9B,OAAA;cAAAsB,QAAA,EAAI;YAAqB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B9B,OAAA;cAAAsB,QAAA,EAAI;YAAsB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN9B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAEL,MAAM,EAAE;YAAa,CAAE;YAAAE,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACzCtB,OAAA;cAAAsB,QAAA,EAAI;YAA4B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrC9B,OAAA;cAAAsB,QAAA,EAAI;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7B9B,OAAA;cAAAsB,QAAA,EAAI;YAAwB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN9B,OAAA;UAAAsB,QAAA,gBACEtB,OAAA;YAAIiB,KAAK,EAAE;cAAEQ,KAAK,EAAE,SAAS;cAAEL,MAAM,EAAE;YAAa,CAAE;YAAAE,QAAA,EAAC;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvE9B,OAAA;YAAIiB,KAAK,EAAE;cAAES,QAAQ,EAAE,MAAM;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACzCtB,OAAA;cAAAsB,QAAA,EAAI;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9B,OAAA;cAAAsB,QAAA,EAAI;YAAqB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B9B,OAAA;cAAAsB,QAAA,EAAI;YAA+B;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5B,EAAA,CAtZQD,WAAW;AAAA8C,EAAA,GAAX9C,WAAW;AAwZpB,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}