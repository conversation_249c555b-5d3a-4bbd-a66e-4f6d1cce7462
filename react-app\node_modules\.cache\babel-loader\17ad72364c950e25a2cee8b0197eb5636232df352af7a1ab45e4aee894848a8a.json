{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\coding\\\\react-app\\\\src\\\\CodingPatternsGuide.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CodingPatternsGuide() {\n  _s();\n  const [selectedTier, setSelectedTier] = useState('tier1');\n  const [completedPatterns, setCompletedPatterns] = useState(new Set());\n  const patterns = {\n    tier1: {\n      title: \"🥇 Tier 1: Foundation Patterns (Must Master)\",\n      subtitle: \"80% of interviews - Master these first\",\n      color: \"#27ae60\",\n      patterns: [{\n        id: \"two-pointers\",\n        name: \"Two Pointers Pattern\",\n        description: \"Efficient technique for array and string problems using two pointers\",\n        difficulty: \"Easy-Medium\",\n        timeToMaster: \"3-5 days\",\n        problems: 25,\n        keyProblems: [\"Two Sum\", \"Three Sum\", \"Remove Duplicates\", \"Palindrome Check\"],\n        timeComplexity: \"O(n)\",\n        spaceComplexity: \"O(1)\"\n      }, {\n        id: \"sliding-window\",\n        name: \"Sliding Window Pattern\",\n        description: \"Optimize subarray/substring problems by maintaining a window\",\n        difficulty: \"Easy-Medium\",\n        timeToMaster: \"4-6 days\",\n        problems: 30,\n        keyProblems: [\"Max Sum Subarray\", \"Longest Substring\", \"Min Window Substring\"],\n        timeComplexity: \"O(n)\",\n        spaceComplexity: \"O(k)\"\n      }, {\n        id: \"binary-search\",\n        name: \"Binary Search Pattern\",\n        description: \"Logarithmic search in sorted arrays and search spaces\",\n        difficulty: \"Medium\",\n        timeToMaster: \"5-7 days\",\n        problems: 35,\n        keyProblems: [\"Search Insert Position\", \"Find Peak Element\", \"Search in Rotated Array\"],\n        timeComplexity: \"O(log n)\",\n        spaceComplexity: \"O(1)\"\n      }, {\n        id: \"tree-traversal\",\n        name: \"Tree Traversal (DFS/BFS)\",\n        description: \"Navigate tree structures using depth-first and breadth-first approaches\",\n        difficulty: \"Medium\",\n        timeToMaster: \"6-8 days\",\n        problems: 40,\n        keyProblems: [\"Binary Tree Paths\", \"Level Order\", \"Validate BST\", \"Max Depth\"],\n        timeComplexity: \"O(n)\",\n        spaceComplexity: \"O(h)\"\n      }, {\n        id: \"dp-1d\",\n        name: \"1D Dynamic Programming\",\n        description: \"Solve optimization problems using memoization and tabulation\",\n        difficulty: \"Medium-Hard\",\n        timeToMaster: \"7-10 days\",\n        problems: 45,\n        keyProblems: [\"Fibonacci\", \"Climbing Stairs\", \"House Robber\", \"Coin Change\"],\n        timeComplexity: \"O(n)\",\n        spaceComplexity: \"O(n)\"\n      }]\n    },\n    tier2: {\n      title: \"🥈 Tier 2: Important Patterns\",\n      subtitle: \"60% of interviews - Build on foundation\",\n      color: \"#3498db\",\n      patterns: [{\n        id: \"fast-slow-pointers\",\n        name: \"Fast & Slow Pointers\",\n        description: \"Detect cycles and find middle elements using Floyd's algorithm\",\n        difficulty: \"Medium\",\n        timeToMaster: \"4-5 days\",\n        problems: 20,\n        keyProblems: [\"Detect Cycle\", \"Find Middle\", \"Happy Number\", \"Palindrome Linked List\"],\n        timeComplexity: \"O(n)\",\n        spaceComplexity: \"O(1)\"\n      }, {\n        id: \"graph-algorithms\",\n        name: \"Graph Algorithms\",\n        description: \"Traverse and analyze graph structures using DFS, BFS, and Union-Find\",\n        difficulty: \"Medium-Hard\",\n        timeToMaster: \"8-10 days\",\n        problems: 50,\n        keyProblems: [\"Number of Islands\", \"Course Schedule\", \"Clone Graph\", \"Word Ladder\"],\n        timeComplexity: \"O(V + E)\",\n        spaceComplexity: \"O(V)\"\n      }, {\n        id: \"heap-priority-queue\",\n        name: \"Heap/Priority Queue\",\n        description: \"Efficiently find top K elements and merge operations\",\n        difficulty: \"Medium\",\n        timeToMaster: \"5-7 days\",\n        problems: 25,\n        keyProblems: [\"Kth Largest\", \"Merge K Lists\", \"Top K Frequent\", \"Find Median\"],\n        timeComplexity: \"O(n log k)\",\n        spaceComplexity: \"O(k)\"\n      }, {\n        id: \"backtracking\",\n        name: \"Backtracking Pattern\",\n        description: \"Generate combinations and solve constraint satisfaction problems\",\n        difficulty: \"Medium-Hard\",\n        timeToMaster: \"6-8 days\",\n        problems: 35,\n        keyProblems: [\"N-Queens\", \"Generate Parentheses\", \"Permutations\", \"Sudoku Solver\"],\n        timeComplexity: \"O(2^n)\",\n        spaceComplexity: \"O(n)\"\n      }, {\n        id: \"merge-intervals\",\n        name: \"Merge Intervals\",\n        description: \"Handle overlapping intervals and scheduling problems\",\n        difficulty: \"Medium\",\n        timeToMaster: \"3-4 days\",\n        problems: 15,\n        keyProblems: [\"Merge Intervals\", \"Insert Interval\", \"Meeting Rooms\", \"Non-overlapping\"],\n        timeComplexity: \"O(n log n)\",\n        spaceComplexity: \"O(n)\"\n      }]\n    },\n    tier3: {\n      title: \"🥉 Tier 3: Advanced Patterns\",\n      subtitle: \"30% of interviews - For senior roles\",\n      color: \"#e74c3c\",\n      patterns: [{\n        id: \"dp-2d\",\n        name: \"2D Dynamic Programming\",\n        description: \"Complex optimization problems with 2D state spaces\",\n        difficulty: \"Hard\",\n        timeToMaster: \"10-14 days\",\n        problems: 40,\n        keyProblems: [\"Unique Paths\", \"Edit Distance\", \"LCS\", \"Palindrome Partitioning\"],\n        timeComplexity: \"O(m*n)\",\n        spaceComplexity: \"O(m*n)\"\n      }, {\n        id: \"greedy\",\n        name: \"Greedy Algorithms\",\n        description: \"Make locally optimal choices for global optimization\",\n        difficulty: \"Medium-Hard\",\n        timeToMaster: \"5-7 days\",\n        problems: 30,\n        keyProblems: [\"Activity Selection\", \"Gas Station\", \"Jump Game\", \"Candy\"],\n        timeComplexity: \"O(n log n)\",\n        spaceComplexity: \"O(1)\"\n      }, {\n        id: \"trie\",\n        name: \"Trie (Prefix Tree)\",\n        description: \"Efficient string search and prefix operations\",\n        difficulty: \"Medium\",\n        timeToMaster: \"4-6 days\",\n        problems: 20,\n        keyProblems: [\"Implement Trie\", \"Word Search II\", \"Auto-complete\", \"Longest Word\"],\n        timeComplexity: \"O(m)\",\n        spaceComplexity: \"O(ALPHABET_SIZE * N)\"\n      }, {\n        id: \"union-find\",\n        name: \"Union-Find (Disjoint Set)\",\n        description: \"Efficiently handle connected components and cycle detection\",\n        difficulty: \"Medium-Hard\",\n        timeToMaster: \"5-7 days\",\n        problems: 25,\n        keyProblems: [\"Connected Components\", \"Redundant Connection\", \"Accounts Merge\"],\n        timeComplexity: \"O(α(n))\",\n        spaceComplexity: \"O(n)\"\n      }, {\n        id: \"modified-binary-search\",\n        name: \"Modified Binary Search\",\n        description: \"Binary search in rotated, mountain, and infinite arrays\",\n        difficulty: \"Hard\",\n        timeToMaster: \"6-8 days\",\n        problems: 20,\n        keyProblems: [\"Search Rotated Array\", \"Find in Mountain\", \"Search 2D Matrix\"],\n        timeComplexity: \"O(log n)\",\n        spaceComplexity: \"O(1)\"\n      }, {\n        id: \"cyclic-sort\",\n        name: \"Cyclic Sort Pattern\",\n        description: \"Sort arrays with numbers in range [1, n] in-place\",\n        difficulty: \"Medium\",\n        timeToMaster: \"3-4 days\",\n        problems: 15,\n        keyProblems: [\"Missing Number\", \"Find Duplicate\", \"First Missing Positive\"],\n        timeComplexity: \"O(n)\",\n        spaceComplexity: \"O(1)\"\n      }]\n    }\n  };\n  const togglePatternCompletion = patternId => {\n    const newCompleted = new Set(completedPatterns);\n    if (newCompleted.has(patternId)) {\n      newCompleted.delete(patternId);\n    } else {\n      newCompleted.add(patternId);\n    }\n    setCompletedPatterns(newCompleted);\n  };\n  const getTierProgress = tierKey => {\n    const tierPatterns = patterns[tierKey].patterns;\n    const completed = tierPatterns.filter(p => completedPatterns.has(p.id)).length;\n    return Math.round(completed / tierPatterns.length * 100);\n  };\n  const getTotalProgress = () => {\n    const allPatterns = Object.values(patterns).flatMap(tier => tier.patterns);\n    const completed = allPatterns.filter(p => completedPatterns.has(p.id)).length;\n    return Math.round(completed / allPatterns.length * 100);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"page-content\",\n    style: {\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          color: '#2c3e50',\n          fontSize: '2.5em',\n          marginBottom: '10px'\n        },\n        children: \"\\uD83C\\uDFAF Complete Coding Patterns Guide\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#7f8c8d',\n          fontSize: '1.2em',\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: \"Master all 16 essential coding patterns for cracking technical interviews with step-by-step implementations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#f8f9fa',\n          borderRadius: '12px',\n          padding: '20px',\n          margin: '20px auto',\n          maxWidth: '600px',\n          border: '2px solid #ecf0f1'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#2c3e50',\n            marginBottom: '15px'\n          },\n          children: \"\\uD83D\\uDCCA Overall Progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#ecf0f1',\n            borderRadius: '10px',\n            height: '20px',\n            overflow: 'hidden',\n            marginBottom: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              backgroundColor: '#27ae60',\n              height: '100%',\n              width: `${getTotalProgress()}%`,\n              transition: 'width 0.3s ease',\n              borderRadius: '10px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: 0,\n            color: '#7f8c8d'\n          },\n          children: [completedPatterns.size, \" / 16 patterns completed (\", getTotalProgress(), \"%)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        gap: '15px',\n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      },\n      children: Object.entries(patterns).map(([tierKey, tier]) => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setSelectedTier(tierKey),\n        style: {\n          padding: '12px 24px',\n          border: selectedTier === tierKey ? `3px solid ${tier.color}` : '2px solid #ddd',\n          borderRadius: '8px',\n          backgroundColor: selectedTier === tierKey ? tier.color : 'white',\n          color: selectedTier === tierKey ? 'white' : '#333',\n          cursor: 'pointer',\n          fontSize: '16px',\n          fontWeight: 'bold',\n          transition: 'all 0.3s ease',\n          position: 'relative'\n        },\n        children: [tier.title.split(':')[0], /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '-8px',\n            right: '-8px',\n            backgroundColor: '#27ae60',\n            color: 'white',\n            borderRadius: '50%',\n            width: '24px',\n            height: '24px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontSize: '12px',\n            fontWeight: 'bold'\n          },\n          children: [getTierProgress(tierKey), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)]\n      }, tierKey, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '30px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '25px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: patterns[selectedTier].color,\n            fontSize: '2em',\n            marginBottom: '5px'\n          },\n          children: patterns[selectedTier].title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#7f8c8d',\n            fontSize: '1.1em'\n          },\n          children: patterns[selectedTier].subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',\n          gap: '25px'\n        },\n        children: patterns[selectedTier].patterns.map(pattern => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: '12px',\n            padding: '25px',\n            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n            border: `3px solid ${patterns[selectedTier].color}`,\n            position: 'relative',\n            transition: 'transform 0.2s ease'\n          },\n          onMouseEnter: e => e.currentTarget.style.transform = 'translateY(-5px)',\n          onMouseLeave: e => e.currentTarget.style.transform = 'translateY(0)',\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '15px',\n              right: '15px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: completedPatterns.has(pattern.id),\n              onChange: () => togglePatternCompletion(pattern.id),\n              style: {\n                transform: 'scale(1.5)',\n                cursor: 'pointer'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              color: patterns[selectedTier].color,\n              marginBottom: '10px',\n              fontSize: '1.3em',\n              paddingRight: '40px'\n            },\n            children: pattern.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#666',\n              marginBottom: '15px',\n              lineHeight: '1.5'\n            },\n            children: pattern.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '10px',\n              marginBottom: '15px',\n              fontSize: '13px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Difficulty:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), \" \", pattern.difficulty]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Time to Master:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 19\n              }, this), \" \", pattern.timeToMaster]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Practice Problems:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), \" \", pattern.problems]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Time Complexity:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), \" \", pattern.timeComplexity]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '15px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                color: '#2c3e50',\n                margin: '0 0 8px 0',\n                fontSize: '14px'\n              },\n              children: \"\\uD83C\\uDFAF Key Problems:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: '5px'\n              },\n              children: pattern.keyProblems.map((problem, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  backgroundColor: '#f8f9fa',\n                  color: '#495057',\n                  padding: '4px 8px',\n                  borderRadius: '12px',\n                  fontSize: '12px',\n                  border: '1px solid #dee2e6'\n                },\n                children: problem\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              backgroundColor: patterns[selectedTier].color,\n              color: 'white',\n              border: 'none',\n              padding: '12px 20px',\n              borderRadius: '6px',\n              fontSize: '14px',\n              fontWeight: 'bold',\n              cursor: 'pointer',\n              width: '100%',\n              transition: 'opacity 0.2s ease'\n            },\n            onMouseEnter: e => e.target.style.opacity = '0.9',\n            onMouseLeave: e => e.target.style.opacity = '1',\n            onClick: () => {\n              // Navigate to specific pattern implementation\n              const routeMap = {\n                'two-pointers': '/patterns/two-pointers',\n                'sliding-window': '/patterns/sliding-window',\n                'binary-search': '/patterns/binary-search'\n              };\n              if (routeMap[pattern.id]) {\n                window.location.href = routeMap[pattern.id];\n              } else {\n                alert(`${pattern.name} implementation coming soon!`);\n              }\n            },\n            children: [\"\\uD83D\\uDCDA Learn \", pattern.name, \" \\u2192\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this)]\n        }, pattern.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 5\n  }, this);\n}\n_s(CodingPatternsGuide, \"JzqQ+qeEtoyr/1pJUgvyDDf9eAg=\");\n_c = CodingPatternsGuide;\nexport default CodingPatternsGuide;\nvar _c;\n$RefreshReg$(_c, \"CodingPatternsGuide\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "CodingPatternsGuide", "_s", "selected<PERSON><PERSON>", "setSelectedTier", "completedPatterns", "setCompletedPatterns", "Set", "patterns", "tier1", "title", "subtitle", "color", "id", "name", "description", "difficulty", "timeToMaster", "problems", "keyProblems", "timeComplexity", "spaceComplexity", "tier2", "tier3", "togglePatternCompletion", "patternId", "newCompleted", "has", "delete", "add", "getTierProgress", "tierKey", "tierPatterns", "completed", "filter", "p", "length", "Math", "round", "getTotalProgress", "allPatterns", "Object", "values", "flatMap", "tier", "className", "style", "fontFamily", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "borderRadius", "padding", "border", "height", "overflow", "width", "transition", "size", "display", "justifyContent", "gap", "flexWrap", "entries", "map", "onClick", "cursor", "fontWeight", "position", "split", "top", "right", "alignItems", "gridTemplateColumns", "pattern", "boxShadow", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "type", "checked", "onChange", "paddingRight", "lineHeight", "problem", "idx", "target", "opacity", "routeMap", "window", "location", "href", "alert", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/coding/react-app/src/CodingPatternsGuide.js"], "sourcesContent": ["import React, { useState } from 'react';\n\nfunction CodingPatternsGuide() {\n  const [selectedTier, setSelectedTier] = useState('tier1');\n  const [completedPatterns, setCompletedPatterns] = useState(new Set());\n\n  const patterns = {\n    tier1: {\n      title: \"🥇 Tier 1: Foundation Patterns (Must Master)\",\n      subtitle: \"80% of interviews - Master these first\",\n      color: \"#27ae60\",\n      patterns: [\n        {\n          id: \"two-pointers\",\n          name: \"Two Pointers Pattern\",\n          description: \"Efficient technique for array and string problems using two pointers\",\n          difficulty: \"Easy-Medium\",\n          timeToMaster: \"3-5 days\",\n          problems: 25,\n          keyProblems: [\"Two Sum\", \"Three Sum\", \"Remove Duplicates\", \"Palindrome Check\"],\n          timeComplexity: \"O(n)\",\n          spaceComplexity: \"O(1)\"\n        },\n        {\n          id: \"sliding-window\",\n          name: \"Sliding Window Pattern\",\n          description: \"Optimize subarray/substring problems by maintaining a window\",\n          difficulty: \"Easy-Medium\",\n          timeToMaster: \"4-6 days\",\n          problems: 30,\n          keyProblems: [\"Max Sum Subarray\", \"Longest Substring\", \"Min Window Substring\"],\n          timeComplexity: \"O(n)\",\n          spaceComplexity: \"O(k)\"\n        },\n        {\n          id: \"binary-search\",\n          name: \"Binary Search Pattern\",\n          description: \"Logarithmic search in sorted arrays and search spaces\",\n          difficulty: \"Medium\",\n          timeToMaster: \"5-7 days\",\n          problems: 35,\n          keyProblems: [\"Search Insert Position\", \"Find Peak Element\", \"Search in Rotated Array\"],\n          timeComplexity: \"O(log n)\",\n          spaceComplexity: \"O(1)\"\n        },\n        {\n          id: \"tree-traversal\",\n          name: \"Tree Traversal (DFS/BFS)\",\n          description: \"Navigate tree structures using depth-first and breadth-first approaches\",\n          difficulty: \"Medium\",\n          timeToMaster: \"6-8 days\",\n          problems: 40,\n          keyProblems: [\"Binary Tree Paths\", \"Level Order\", \"Validate BST\", \"Max Depth\"],\n          timeComplexity: \"O(n)\",\n          spaceComplexity: \"O(h)\"\n        },\n        {\n          id: \"dp-1d\",\n          name: \"1D Dynamic Programming\",\n          description: \"Solve optimization problems using memoization and tabulation\",\n          difficulty: \"Medium-Hard\",\n          timeToMaster: \"7-10 days\",\n          problems: 45,\n          keyProblems: [\"Fibonacci\", \"Climbing Stairs\", \"House Robber\", \"Coin Change\"],\n          timeComplexity: \"O(n)\",\n          spaceComplexity: \"O(n)\"\n        }\n      ]\n    },\n    tier2: {\n      title: \"🥈 Tier 2: Important Patterns\",\n      subtitle: \"60% of interviews - Build on foundation\",\n      color: \"#3498db\",\n      patterns: [\n        {\n          id: \"fast-slow-pointers\",\n          name: \"Fast & Slow Pointers\",\n          description: \"Detect cycles and find middle elements using Floyd's algorithm\",\n          difficulty: \"Medium\",\n          timeToMaster: \"4-5 days\",\n          problems: 20,\n          keyProblems: [\"Detect Cycle\", \"Find Middle\", \"Happy Number\", \"Palindrome Linked List\"],\n          timeComplexity: \"O(n)\",\n          spaceComplexity: \"O(1)\"\n        },\n        {\n          id: \"graph-algorithms\",\n          name: \"Graph Algorithms\",\n          description: \"Traverse and analyze graph structures using DFS, BFS, and Union-Find\",\n          difficulty: \"Medium-Hard\",\n          timeToMaster: \"8-10 days\",\n          problems: 50,\n          keyProblems: [\"Number of Islands\", \"Course Schedule\", \"Clone Graph\", \"Word Ladder\"],\n          timeComplexity: \"O(V + E)\",\n          spaceComplexity: \"O(V)\"\n        },\n        {\n          id: \"heap-priority-queue\",\n          name: \"Heap/Priority Queue\",\n          description: \"Efficiently find top K elements and merge operations\",\n          difficulty: \"Medium\",\n          timeToMaster: \"5-7 days\",\n          problems: 25,\n          keyProblems: [\"Kth Largest\", \"Merge K Lists\", \"Top K Frequent\", \"Find Median\"],\n          timeComplexity: \"O(n log k)\",\n          spaceComplexity: \"O(k)\"\n        },\n        {\n          id: \"backtracking\",\n          name: \"Backtracking Pattern\",\n          description: \"Generate combinations and solve constraint satisfaction problems\",\n          difficulty: \"Medium-Hard\",\n          timeToMaster: \"6-8 days\",\n          problems: 35,\n          keyProblems: [\"N-Queens\", \"Generate Parentheses\", \"Permutations\", \"Sudoku Solver\"],\n          timeComplexity: \"O(2^n)\",\n          spaceComplexity: \"O(n)\"\n        },\n        {\n          id: \"merge-intervals\",\n          name: \"Merge Intervals\",\n          description: \"Handle overlapping intervals and scheduling problems\",\n          difficulty: \"Medium\",\n          timeToMaster: \"3-4 days\",\n          problems: 15,\n          keyProblems: [\"Merge Intervals\", \"Insert Interval\", \"Meeting Rooms\", \"Non-overlapping\"],\n          timeComplexity: \"O(n log n)\",\n          spaceComplexity: \"O(n)\"\n        }\n      ]\n    },\n    tier3: {\n      title: \"🥉 Tier 3: Advanced Patterns\",\n      subtitle: \"30% of interviews - For senior roles\",\n      color: \"#e74c3c\",\n      patterns: [\n        {\n          id: \"dp-2d\",\n          name: \"2D Dynamic Programming\",\n          description: \"Complex optimization problems with 2D state spaces\",\n          difficulty: \"Hard\",\n          timeToMaster: \"10-14 days\",\n          problems: 40,\n          keyProblems: [\"Unique Paths\", \"Edit Distance\", \"LCS\", \"Palindrome Partitioning\"],\n          timeComplexity: \"O(m*n)\",\n          spaceComplexity: \"O(m*n)\"\n        },\n        {\n          id: \"greedy\",\n          name: \"Greedy Algorithms\",\n          description: \"Make locally optimal choices for global optimization\",\n          difficulty: \"Medium-Hard\",\n          timeToMaster: \"5-7 days\",\n          problems: 30,\n          keyProblems: [\"Activity Selection\", \"Gas Station\", \"Jump Game\", \"Candy\"],\n          timeComplexity: \"O(n log n)\",\n          spaceComplexity: \"O(1)\"\n        },\n        {\n          id: \"trie\",\n          name: \"Trie (Prefix Tree)\",\n          description: \"Efficient string search and prefix operations\",\n          difficulty: \"Medium\",\n          timeToMaster: \"4-6 days\",\n          problems: 20,\n          keyProblems: [\"Implement Trie\", \"Word Search II\", \"Auto-complete\", \"Longest Word\"],\n          timeComplexity: \"O(m)\",\n          spaceComplexity: \"O(ALPHABET_SIZE * N)\"\n        },\n        {\n          id: \"union-find\",\n          name: \"Union-Find (Disjoint Set)\",\n          description: \"Efficiently handle connected components and cycle detection\",\n          difficulty: \"Medium-Hard\",\n          timeToMaster: \"5-7 days\",\n          problems: 25,\n          keyProblems: [\"Connected Components\", \"Redundant Connection\", \"Accounts Merge\"],\n          timeComplexity: \"O(α(n))\",\n          spaceComplexity: \"O(n)\"\n        },\n        {\n          id: \"modified-binary-search\",\n          name: \"Modified Binary Search\",\n          description: \"Binary search in rotated, mountain, and infinite arrays\",\n          difficulty: \"Hard\",\n          timeToMaster: \"6-8 days\",\n          problems: 20,\n          keyProblems: [\"Search Rotated Array\", \"Find in Mountain\", \"Search 2D Matrix\"],\n          timeComplexity: \"O(log n)\",\n          spaceComplexity: \"O(1)\"\n        },\n        {\n          id: \"cyclic-sort\",\n          name: \"Cyclic Sort Pattern\",\n          description: \"Sort arrays with numbers in range [1, n] in-place\",\n          difficulty: \"Medium\",\n          timeToMaster: \"3-4 days\",\n          problems: 15,\n          keyProblems: [\"Missing Number\", \"Find Duplicate\", \"First Missing Positive\"],\n          timeComplexity: \"O(n)\",\n          spaceComplexity: \"O(1)\"\n        }\n      ]\n    }\n  };\n\n  const togglePatternCompletion = (patternId) => {\n    const newCompleted = new Set(completedPatterns);\n    if (newCompleted.has(patternId)) {\n      newCompleted.delete(patternId);\n    } else {\n      newCompleted.add(patternId);\n    }\n    setCompletedPatterns(newCompleted);\n  };\n\n  const getTierProgress = (tierKey) => {\n    const tierPatterns = patterns[tierKey].patterns;\n    const completed = tierPatterns.filter(p => completedPatterns.has(p.id)).length;\n    return Math.round((completed / tierPatterns.length) * 100);\n  };\n\n  const getTotalProgress = () => {\n    const allPatterns = Object.values(patterns).flatMap(tier => tier.patterns);\n    const completed = allPatterns.filter(p => completedPatterns.has(p.id)).length;\n    return Math.round((completed / allPatterns.length) * 100);\n  };\n\n  return (\n    <div className=\"page-content\" style={{ fontFamily: 'Arial, sans-serif' }}>\n      {/* Header */}\n      <div style={{ textAlign: 'center', marginBottom: '30px' }}>\n        <h1 style={{ color: '#2c3e50', fontSize: '2.5em', marginBottom: '10px' }}>\n          🎯 Complete Coding Patterns Guide\n        </h1>\n        <p style={{ color: '#7f8c8d', fontSize: '1.2em', maxWidth: '800px', margin: '0 auto' }}>\n          Master all 16 essential coding patterns for cracking technical interviews with step-by-step implementations\n        </p>\n        \n        {/* Overall Progress */}\n        <div style={{\n          backgroundColor: '#f8f9fa',\n          borderRadius: '12px',\n          padding: '20px',\n          margin: '20px auto',\n          maxWidth: '600px',\n          border: '2px solid #ecf0f1'\n        }}>\n          <h3 style={{ color: '#2c3e50', marginBottom: '15px' }}>📊 Overall Progress</h3>\n          <div style={{\n            backgroundColor: '#ecf0f1',\n            borderRadius: '10px',\n            height: '20px',\n            overflow: 'hidden',\n            marginBottom: '10px'\n          }}>\n            <div style={{\n              backgroundColor: '#27ae60',\n              height: '100%',\n              width: `${getTotalProgress()}%`,\n              transition: 'width 0.3s ease',\n              borderRadius: '10px'\n            }}></div>\n          </div>\n          <p style={{ margin: 0, color: '#7f8c8d' }}>\n            {completedPatterns.size} / 16 patterns completed ({getTotalProgress()}%)\n          </p>\n        </div>\n      </div>\n\n      {/* Tier Navigation */}\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        gap: '15px', \n        marginBottom: '30px',\n        flexWrap: 'wrap'\n      }}>\n        {Object.entries(patterns).map(([tierKey, tier]) => (\n          <button\n            key={tierKey}\n            onClick={() => setSelectedTier(tierKey)}\n            style={{\n              padding: '12px 24px',\n              border: selectedTier === tierKey ? `3px solid ${tier.color}` : '2px solid #ddd',\n              borderRadius: '8px',\n              backgroundColor: selectedTier === tierKey ? tier.color : 'white',\n              color: selectedTier === tierKey ? 'white' : '#333',\n              cursor: 'pointer',\n              fontSize: '16px',\n              fontWeight: 'bold',\n              transition: 'all 0.3s ease',\n              position: 'relative'\n            }}\n          >\n            {tier.title.split(':')[0]}\n            <div style={{\n              position: 'absolute',\n              top: '-8px',\n              right: '-8px',\n              backgroundColor: '#27ae60',\n              color: 'white',\n              borderRadius: '50%',\n              width: '24px',\n              height: '24px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: '12px',\n              fontWeight: 'bold'\n            }}>\n              {getTierProgress(tierKey)}%\n            </div>\n          </button>\n        ))}\n      </div>\n\n      {/* Selected Tier Content */}\n      <div style={{ marginBottom: '30px' }}>\n        <div style={{ textAlign: 'center', marginBottom: '25px' }}>\n          <h2 style={{ color: patterns[selectedTier].color, fontSize: '2em', marginBottom: '5px' }}>\n            {patterns[selectedTier].title}\n          </h2>\n          <p style={{ color: '#7f8c8d', fontSize: '1.1em' }}>\n            {patterns[selectedTier].subtitle}\n          </p>\n        </div>\n\n        {/* Patterns Grid */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',\n          gap: '25px'\n        }}>\n          {patterns[selectedTier].patterns.map((pattern) => (\n            <div\n              key={pattern.id}\n              style={{\n                backgroundColor: 'white',\n                borderRadius: '12px',\n                padding: '25px',\n                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                border: `3px solid ${patterns[selectedTier].color}`,\n                position: 'relative',\n                transition: 'transform 0.2s ease'\n              }}\n              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}\n              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}\n            >\n              {/* Completion Checkbox */}\n              <div style={{ position: 'absolute', top: '15px', right: '15px' }}>\n                <input\n                  type=\"checkbox\"\n                  checked={completedPatterns.has(pattern.id)}\n                  onChange={() => togglePatternCompletion(pattern.id)}\n                  style={{ transform: 'scale(1.5)', cursor: 'pointer' }}\n                />\n              </div>\n\n              <h3 style={{ \n                color: patterns[selectedTier].color, \n                marginBottom: '10px',\n                fontSize: '1.3em',\n                paddingRight: '40px'\n              }}>\n                {pattern.name}\n              </h3>\n              \n              <p style={{ color: '#666', marginBottom: '15px', lineHeight: '1.5' }}>\n                {pattern.description}\n              </p>\n\n              {/* Pattern Stats */}\n              <div style={{ \n                display: 'grid', \n                gridTemplateColumns: '1fr 1fr', \n                gap: '10px', \n                marginBottom: '15px',\n                fontSize: '13px'\n              }}>\n                <div>\n                  <strong>Difficulty:</strong> {pattern.difficulty}\n                </div>\n                <div>\n                  <strong>Time to Master:</strong> {pattern.timeToMaster}\n                </div>\n                <div>\n                  <strong>Practice Problems:</strong> {pattern.problems}\n                </div>\n                <div>\n                  <strong>Time Complexity:</strong> {pattern.timeComplexity}\n                </div>\n              </div>\n\n              {/* Key Problems */}\n              <div style={{ marginBottom: '15px' }}>\n                <h4 style={{ color: '#2c3e50', margin: '0 0 8px 0', fontSize: '14px' }}>\n                  🎯 Key Problems:\n                </h4>\n                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>\n                  {pattern.keyProblems.map((problem, idx) => (\n                    <span\n                      key={idx}\n                      style={{\n                        backgroundColor: '#f8f9fa',\n                        color: '#495057',\n                        padding: '4px 8px',\n                        borderRadius: '12px',\n                        fontSize: '12px',\n                        border: '1px solid #dee2e6'\n                      }}\n                    >\n                      {problem}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Action Button */}\n              <button\n                style={{\n                  backgroundColor: patterns[selectedTier].color,\n                  color: 'white',\n                  border: 'none',\n                  padding: '12px 20px',\n                  borderRadius: '6px',\n                  fontSize: '14px',\n                  fontWeight: 'bold',\n                  cursor: 'pointer',\n                  width: '100%',\n                  transition: 'opacity 0.2s ease'\n                }}\n                onMouseEnter={(e) => e.target.style.opacity = '0.9'}\n                onMouseLeave={(e) => e.target.style.opacity = '1'}\n                onClick={() => {\n                  // Navigate to specific pattern implementation\n                  const routeMap = {\n                    'two-pointers': '/patterns/two-pointers',\n                    'sliding-window': '/patterns/sliding-window',\n                    'binary-search': '/patterns/binary-search'\n                  };\n\n                  if (routeMap[pattern.id]) {\n                    window.location.href = routeMap[pattern.id];\n                  } else {\n                    alert(`${pattern.name} implementation coming soon!`);\n                  }\n                }}\n              >\n                📚 Learn {pattern.name} →\n              </button>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default CodingPatternsGuide;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGN,QAAQ,CAAC,OAAO,CAAC;EACzD,MAAM,CAACO,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGR,QAAQ,CAAC,IAAIS,GAAG,CAAC,CAAC,CAAC;EAErE,MAAMC,QAAQ,GAAG;IACfC,KAAK,EAAE;MACLC,KAAK,EAAE,8CAA8C;MACrDC,QAAQ,EAAE,wCAAwC;MAClDC,KAAK,EAAE,SAAS;MAChBJ,QAAQ,EAAE,CACR;QACEK,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,sEAAsE;QACnFC,UAAU,EAAE,aAAa;QACzBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,mBAAmB,EAAE,kBAAkB,CAAC;QAC9EC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,gBAAgB;QACpBC,IAAI,EAAE,wBAAwB;QAC9BC,WAAW,EAAE,8DAA8D;QAC3EC,UAAU,EAAE,aAAa;QACzBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,sBAAsB,CAAC;QAC9EC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,eAAe;QACnBC,IAAI,EAAE,uBAAuB;QAC7BC,WAAW,EAAE,uDAAuD;QACpEC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,wBAAwB,EAAE,mBAAmB,EAAE,yBAAyB,CAAC;QACvFC,cAAc,EAAE,UAAU;QAC1BC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,gBAAgB;QACpBC,IAAI,EAAE,0BAA0B;QAChCC,WAAW,EAAE,yEAAyE;QACtFC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,mBAAmB,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,CAAC;QAC9EC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,wBAAwB;QAC9BC,WAAW,EAAE,8DAA8D;QAC3EC,UAAU,EAAE,aAAa;QACzBC,YAAY,EAAE,WAAW;QACzBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,cAAc,EAAE,aAAa,CAAC;QAC5EC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE;MACnB,CAAC;IAEL,CAAC;IACDC,KAAK,EAAE;MACLZ,KAAK,EAAE,+BAA+B;MACtCC,QAAQ,EAAE,yCAAyC;MACnDC,KAAK,EAAE,SAAS;MAChBJ,QAAQ,EAAE,CACR;QACEK,EAAE,EAAE,oBAAoB;QACxBC,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,gEAAgE;QAC7EC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,wBAAwB,CAAC;QACtFC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,kBAAkB;QACtBC,IAAI,EAAE,kBAAkB;QACxBC,WAAW,EAAE,sEAAsE;QACnFC,UAAU,EAAE,aAAa;QACzBC,YAAY,EAAE,WAAW;QACzBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,aAAa,EAAE,aAAa,CAAC;QACnFC,cAAc,EAAE,UAAU;QAC1BC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,qBAAqB;QACzBC,IAAI,EAAE,qBAAqB;QAC3BC,WAAW,EAAE,sDAAsD;QACnEC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC;QAC9EC,cAAc,EAAE,YAAY;QAC5BC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,sBAAsB;QAC5BC,WAAW,EAAE,kEAAkE;QAC/EC,UAAU,EAAE,aAAa;QACzBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,UAAU,EAAE,sBAAsB,EAAE,cAAc,EAAE,eAAe,CAAC;QAClFC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,iBAAiB;QACrBC,IAAI,EAAE,iBAAiB;QACvBC,WAAW,EAAE,sDAAsD;QACnEC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,iBAAiB,CAAC;QACvFC,cAAc,EAAE,YAAY;QAC5BC,eAAe,EAAE;MACnB,CAAC;IAEL,CAAC;IACDE,KAAK,EAAE;MACLb,KAAK,EAAE,8BAA8B;MACrCC,QAAQ,EAAE,sCAAsC;MAChDC,KAAK,EAAE,SAAS;MAChBJ,QAAQ,EAAE,CACR;QACEK,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,wBAAwB;QAC9BC,WAAW,EAAE,oDAAoD;QACjEC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,YAAY;QAC1BC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,KAAK,EAAE,yBAAyB,CAAC;QAChFC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,sDAAsD;QACnEC,UAAU,EAAE,aAAa;QACzBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,oBAAoB,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC;QACxEC,cAAc,EAAE,YAAY;QAC5BC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,MAAM;QACVC,IAAI,EAAE,oBAAoB;QAC1BC,WAAW,EAAE,+CAA+C;QAC5DC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,eAAe,EAAE,cAAc,CAAC;QAClFC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,2BAA2B;QACjCC,WAAW,EAAE,6DAA6D;QAC1EC,UAAU,EAAE,aAAa;QACzBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;QAC/EC,cAAc,EAAE,SAAS;QACzBC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,wBAAwB;QAC5BC,IAAI,EAAE,wBAAwB;QAC9BC,WAAW,EAAE,yDAAyD;QACtEC,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,sBAAsB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;QAC7EC,cAAc,EAAE,UAAU;QAC1BC,eAAe,EAAE;MACnB,CAAC,EACD;QACER,EAAE,EAAE,aAAa;QACjBC,IAAI,EAAE,qBAAqB;QAC3BC,WAAW,EAAE,mDAAmD;QAChEC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,UAAU;QACxBC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,wBAAwB,CAAC;QAC3EC,cAAc,EAAE,MAAM;QACtBC,eAAe,EAAE;MACnB,CAAC;IAEL;EACF,CAAC;EAED,MAAMG,uBAAuB,GAAIC,SAAS,IAAK;IAC7C,MAAMC,YAAY,GAAG,IAAInB,GAAG,CAACF,iBAAiB,CAAC;IAC/C,IAAIqB,YAAY,CAACC,GAAG,CAACF,SAAS,CAAC,EAAE;MAC/BC,YAAY,CAACE,MAAM,CAACH,SAAS,CAAC;IAChC,CAAC,MAAM;MACLC,YAAY,CAACG,GAAG,CAACJ,SAAS,CAAC;IAC7B;IACAnB,oBAAoB,CAACoB,YAAY,CAAC;EACpC,CAAC;EAED,MAAMI,eAAe,GAAIC,OAAO,IAAK;IACnC,MAAMC,YAAY,GAAGxB,QAAQ,CAACuB,OAAO,CAAC,CAACvB,QAAQ;IAC/C,MAAMyB,SAAS,GAAGD,YAAY,CAACE,MAAM,CAACC,CAAC,IAAI9B,iBAAiB,CAACsB,GAAG,CAACQ,CAAC,CAACtB,EAAE,CAAC,CAAC,CAACuB,MAAM;IAC9E,OAAOC,IAAI,CAACC,KAAK,CAAEL,SAAS,GAAGD,YAAY,CAACI,MAAM,GAAI,GAAG,CAAC;EAC5D,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAClC,QAAQ,CAAC,CAACmC,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACpC,QAAQ,CAAC;IAC1E,MAAMyB,SAAS,GAAGO,WAAW,CAACN,MAAM,CAACC,CAAC,IAAI9B,iBAAiB,CAACsB,GAAG,CAACQ,CAAC,CAACtB,EAAE,CAAC,CAAC,CAACuB,MAAM;IAC7E,OAAOC,IAAI,CAACC,KAAK,CAAEL,SAAS,GAAGO,WAAW,CAACJ,MAAM,GAAI,GAAG,CAAC;EAC3D,CAAC;EAED,oBACEpC,OAAA;IAAK6C,SAAS,EAAC,cAAc;IAACC,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAoB,CAAE;IAAAC,QAAA,gBAEvEhD,OAAA;MAAK8C,KAAK,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACxDhD,OAAA;QAAI8C,KAAK,EAAE;UAAElC,KAAK,EAAE,SAAS;UAAEuC,QAAQ,EAAE,OAAO;UAAED,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAC;MAE1E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLvD,OAAA;QAAG8C,KAAK,EAAE;UAAElC,KAAK,EAAE,SAAS;UAAEuC,QAAQ,EAAE,OAAO;UAAEK,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAT,QAAA,EAAC;MAExF;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAGJvD,OAAA;QAAK8C,KAAK,EAAE;UACVY,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfH,MAAM,EAAE,WAAW;UACnBD,QAAQ,EAAE,OAAO;UACjBK,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,gBACAhD,OAAA;UAAI8C,KAAK,EAAE;YAAElC,KAAK,EAAE,SAAS;YAAEsC,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EvD,OAAA;UAAK8C,KAAK,EAAE;YACVY,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,MAAM;YACpBG,MAAM,EAAE,MAAM;YACdC,QAAQ,EAAE,QAAQ;YAClBb,YAAY,EAAE;UAChB,CAAE;UAAAF,QAAA,eACAhD,OAAA;YAAK8C,KAAK,EAAE;cACVY,eAAe,EAAE,SAAS;cAC1BI,MAAM,EAAE,MAAM;cACdE,KAAK,EAAE,GAAGzB,gBAAgB,CAAC,CAAC,GAAG;cAC/B0B,UAAU,EAAE,iBAAiB;cAC7BN,YAAY,EAAE;YAChB;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNvD,OAAA;UAAG8C,KAAK,EAAE;YAAEW,MAAM,EAAE,CAAC;YAAE7C,KAAK,EAAE;UAAU,CAAE;UAAAoC,QAAA,GACvC3C,iBAAiB,CAAC6D,IAAI,EAAC,4BAA0B,EAAC3B,gBAAgB,CAAC,CAAC,EAAC,IACxE;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvD,OAAA;MAAK8C,KAAK,EAAE;QACVqB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,GAAG,EAAE,MAAM;QACXnB,YAAY,EAAE,MAAM;QACpBoB,QAAQ,EAAE;MACZ,CAAE;MAAAtB,QAAA,EACCP,MAAM,CAAC8B,OAAO,CAAC/D,QAAQ,CAAC,CAACgE,GAAG,CAAC,CAAC,CAACzC,OAAO,EAAEa,IAAI,CAAC,kBAC5C5C,OAAA;QAEEyE,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC2B,OAAO,CAAE;QACxCe,KAAK,EAAE;UACLc,OAAO,EAAE,WAAW;UACpBC,MAAM,EAAE1D,YAAY,KAAK4B,OAAO,GAAG,aAAaa,IAAI,CAAChC,KAAK,EAAE,GAAG,gBAAgB;UAC/E+C,YAAY,EAAE,KAAK;UACnBD,eAAe,EAAEvD,YAAY,KAAK4B,OAAO,GAAGa,IAAI,CAAChC,KAAK,GAAG,OAAO;UAChEA,KAAK,EAAET,YAAY,KAAK4B,OAAO,GAAG,OAAO,GAAG,MAAM;UAClD2C,MAAM,EAAE,SAAS;UACjBvB,QAAQ,EAAE,MAAM;UAChBwB,UAAU,EAAE,MAAM;UAClBV,UAAU,EAAE,eAAe;UAC3BW,QAAQ,EAAE;QACZ,CAAE;QAAA5B,QAAA,GAEDJ,IAAI,CAAClC,KAAK,CAACmE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,eACzB7E,OAAA;UAAK8C,KAAK,EAAE;YACV8B,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,MAAM;YACXC,KAAK,EAAE,MAAM;YACbrB,eAAe,EAAE,SAAS;YAC1B9C,KAAK,EAAE,OAAO;YACd+C,YAAY,EAAE,KAAK;YACnBK,KAAK,EAAE,MAAM;YACbF,MAAM,EAAE,MAAM;YACdK,OAAO,EAAE,MAAM;YACfa,UAAU,EAAE,QAAQ;YACpBZ,cAAc,EAAE,QAAQ;YACxBjB,QAAQ,EAAE,MAAM;YAChBwB,UAAU,EAAE;UACd,CAAE;UAAA3B,QAAA,GACClB,eAAe,CAACC,OAAO,CAAC,EAAC,GAC5B;QAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA,GAhCDxB,OAAO;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiCN,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNvD,OAAA;MAAK8C,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAF,QAAA,gBACnChD,OAAA;QAAK8C,KAAK,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBACxDhD,OAAA;UAAI8C,KAAK,EAAE;YAAElC,KAAK,EAAEJ,QAAQ,CAACL,YAAY,CAAC,CAACS,KAAK;YAAEuC,QAAQ,EAAE,KAAK;YAAED,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,EACtFxC,QAAQ,CAACL,YAAY,CAAC,CAACO;QAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLvD,OAAA;UAAG8C,KAAK,EAAE;YAAElC,KAAK,EAAE,SAAS;YAAEuC,QAAQ,EAAE;UAAQ,CAAE;UAAAH,QAAA,EAC/CxC,QAAQ,CAACL,YAAY,CAAC,CAACQ;QAAQ;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvD,OAAA;QAAK8C,KAAK,EAAE;UACVqB,OAAO,EAAE,MAAM;UACfc,mBAAmB,EAAE,sCAAsC;UAC3DZ,GAAG,EAAE;QACP,CAAE;QAAArB,QAAA,EACCxC,QAAQ,CAACL,YAAY,CAAC,CAACK,QAAQ,CAACgE,GAAG,CAAEU,OAAO,iBAC3ClF,OAAA;UAEE8C,KAAK,EAAE;YACLY,eAAe,EAAE,OAAO;YACxBC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,MAAM;YACfuB,SAAS,EAAE,8BAA8B;YACzCtB,MAAM,EAAE,aAAarD,QAAQ,CAACL,YAAY,CAAC,CAACS,KAAK,EAAE;YACnDgE,QAAQ,EAAE,UAAU;YACpBX,UAAU,EAAE;UACd,CAAE;UACFmB,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACxC,KAAK,CAACyC,SAAS,GAAG,kBAAmB;UAC1EC,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACC,aAAa,CAACxC,KAAK,CAACyC,SAAS,GAAG,eAAgB;UAAAvC,QAAA,gBAGvEhD,OAAA;YAAK8C,KAAK,EAAE;cAAE8B,QAAQ,EAAE,UAAU;cAAEE,GAAG,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAA/B,QAAA,eAC/DhD,OAAA;cACEyF,IAAI,EAAC,UAAU;cACfC,OAAO,EAAErF,iBAAiB,CAACsB,GAAG,CAACuD,OAAO,CAACrE,EAAE,CAAE;cAC3C8E,QAAQ,EAAEA,CAAA,KAAMnE,uBAAuB,CAAC0D,OAAO,CAACrE,EAAE,CAAE;cACpDiC,KAAK,EAAE;gBAAEyC,SAAS,EAAE,YAAY;gBAAEb,MAAM,EAAE;cAAU;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvD,OAAA;YAAI8C,KAAK,EAAE;cACTlC,KAAK,EAAEJ,QAAQ,CAACL,YAAY,CAAC,CAACS,KAAK;cACnCsC,YAAY,EAAE,MAAM;cACpBC,QAAQ,EAAE,OAAO;cACjByC,YAAY,EAAE;YAChB,CAAE;YAAA5C,QAAA,EACCkC,OAAO,CAACpE;UAAI;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAELvD,OAAA;YAAG8C,KAAK,EAAE;cAAElC,KAAK,EAAE,MAAM;cAAEsC,YAAY,EAAE,MAAM;cAAE2C,UAAU,EAAE;YAAM,CAAE;YAAA7C,QAAA,EAClEkC,OAAO,CAACnE;UAAW;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGJvD,OAAA;YAAK8C,KAAK,EAAE;cACVqB,OAAO,EAAE,MAAM;cACfc,mBAAmB,EAAE,SAAS;cAC9BZ,GAAG,EAAE,MAAM;cACXnB,YAAY,EAAE,MAAM;cACpBC,QAAQ,EAAE;YACZ,CAAE;YAAAH,QAAA,gBACAhD,OAAA;cAAAgD,QAAA,gBACEhD,OAAA;gBAAAgD,QAAA,EAAQ;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAAClE,UAAU;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNvD,OAAA;cAAAgD,QAAA,gBACEhD,OAAA;gBAAAgD,QAAA,EAAQ;cAAe;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAACjE,YAAY;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNvD,OAAA;cAAAgD,QAAA,gBACEhD,OAAA;gBAAAgD,QAAA,EAAQ;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAAChE,QAAQ;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNvD,OAAA;cAAAgD,QAAA,gBACEhD,OAAA;gBAAAgD,QAAA,EAAQ;cAAgB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC2B,OAAO,CAAC9D,cAAc;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA;YAAK8C,KAAK,EAAE;cAAEI,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,gBACnChD,OAAA;cAAI8C,KAAK,EAAE;gBAAElC,KAAK,EAAE,SAAS;gBAAE6C,MAAM,EAAE,WAAW;gBAAEN,QAAQ,EAAE;cAAO,CAAE;cAAAH,QAAA,EAAC;YAExE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvD,OAAA;cAAK8C,KAAK,EAAE;gBAAEqB,OAAO,EAAE,MAAM;gBAAEG,QAAQ,EAAE,MAAM;gBAAED,GAAG,EAAE;cAAM,CAAE;cAAArB,QAAA,EAC3DkC,OAAO,CAAC/D,WAAW,CAACqD,GAAG,CAAC,CAACsB,OAAO,EAAEC,GAAG,kBACpC/F,OAAA;gBAEE8C,KAAK,EAAE;kBACLY,eAAe,EAAE,SAAS;kBAC1B9C,KAAK,EAAE,SAAS;kBAChBgD,OAAO,EAAE,SAAS;kBAClBD,YAAY,EAAE,MAAM;kBACpBR,QAAQ,EAAE,MAAM;kBAChBU,MAAM,EAAE;gBACV,CAAE;gBAAAb,QAAA,EAED8C;cAAO,GAVHC,GAAG;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWJ,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvD,OAAA;YACE8C,KAAK,EAAE;cACLY,eAAe,EAAElD,QAAQ,CAACL,YAAY,CAAC,CAACS,KAAK;cAC7CA,KAAK,EAAE,OAAO;cACdiD,MAAM,EAAE,MAAM;cACdD,OAAO,EAAE,WAAW;cACpBD,YAAY,EAAE,KAAK;cACnBR,QAAQ,EAAE,MAAM;cAChBwB,UAAU,EAAE,MAAM;cAClBD,MAAM,EAAE,SAAS;cACjBV,KAAK,EAAE,MAAM;cACbC,UAAU,EAAE;YACd,CAAE;YACFmB,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACW,MAAM,CAAClD,KAAK,CAACmD,OAAO,GAAG,KAAM;YACpDT,YAAY,EAAGH,CAAC,IAAKA,CAAC,CAACW,MAAM,CAAClD,KAAK,CAACmD,OAAO,GAAG,GAAI;YAClDxB,OAAO,EAAEA,CAAA,KAAM;cACb;cACA,MAAMyB,QAAQ,GAAG;gBACf,cAAc,EAAE,wBAAwB;gBACxC,gBAAgB,EAAE,0BAA0B;gBAC5C,eAAe,EAAE;cACnB,CAAC;cAED,IAAIA,QAAQ,CAAChB,OAAO,CAACrE,EAAE,CAAC,EAAE;gBACxBsF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGH,QAAQ,CAAChB,OAAO,CAACrE,EAAE,CAAC;cAC7C,CAAC,MAAM;gBACLyF,KAAK,CAAC,GAAGpB,OAAO,CAACpE,IAAI,8BAA8B,CAAC;cACtD;YACF,CAAE;YAAAkC,QAAA,GACH,qBACU,EAACkC,OAAO,CAACpE,IAAI,EAAC,SACzB;UAAA;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAlHJ2B,OAAO,CAACrE,EAAE;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmHZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACrD,EAAA,CAvcQD,mBAAmB;AAAAsG,EAAA,GAAnBtG,mBAAmB;AAyc5B,eAAeA,mBAAmB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}