[{"C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Patterns.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Chat.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\MainPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\History.js": "7"}, {"size": 535, "mtime": 1750832291616, "results": "8", "hashOfConfig": "9"}, {"size": 692, "mtime": 1751212686564, "results": "10", "hashOfConfig": "9"}, {"size": 362, "mtime": 1750832291751, "results": "11", "hashOfConfig": "9"}, {"size": 3161, "mtime": 1750836198440, "results": "12", "hashOfConfig": "9"}, {"size": 1636, "mtime": 1750838189679, "results": "13", "hashOfConfig": "9"}, {"size": 5383, "mtime": 1751212679142, "results": "14", "hashOfConfig": "9"}, {"size": 1412, "mtime": 1751212661329, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "222fj5", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Patterns.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Chat.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\MainPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\History.js", [], []]