[{"C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Patterns.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Chat.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\MainPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\History.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\CQRSPattern.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\PatternsSummary.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\CodingPatternsGuide.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\patterns\\TwoPointersPattern.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\patterns\\BinarySearchPattern.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\patterns\\SlidingWindowPattern.js": "13"}, {"size": 535, "mtime": 1750832291616, "results": "14", "hashOfConfig": "15"}, {"size": 1552, "mtime": 1751808903345, "results": "16", "hashOfConfig": "15"}, {"size": 362, "mtime": 1750832291751, "results": "17", "hashOfConfig": "15"}, {"size": 3161, "mtime": 1750836198440, "results": "18", "hashOfConfig": "15"}, {"size": 1636, "mtime": 1750838189679, "results": "19", "hashOfConfig": "15"}, {"size": 7073, "mtime": 1751808958040, "results": "20", "hashOfConfig": "15"}, {"size": 23077, "mtime": 1751213284498, "results": "21", "hashOfConfig": "15"}, {"size": 15631, "mtime": 1751737496966, "results": "22", "hashOfConfig": "15"}, {"size": 8993, "mtime": 1751737507447, "results": "23", "hashOfConfig": "15"}, {"size": 16852, "mtime": 1751808915622, "results": "24", "hashOfConfig": "15"}, {"size": 16514, "mtime": 1751808648268, "results": "25", "hashOfConfig": "15"}, {"size": 24396, "mtime": 1751808819437, "results": "26", "hashOfConfig": "15"}, {"size": 19697, "mtime": 1751808724832, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "222fj5", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Patterns.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Chat.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\MainPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\History.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\CQRSPattern.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\PatternsSummary.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\CodingPatternsGuide.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\patterns\\TwoPointersPattern.js", ["67", "68"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\patterns\\BinarySearchPattern.js", ["69", "70"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\patterns\\SlidingWindowPattern.js", ["71", "72"], [], {"ruleId": "73", "severity": 1, "message": "74", "line": 5, "column": 21, "nodeType": "75", "messageId": "76", "endLine": 5, "endColumn": 33}, {"ruleId": "73", "severity": 1, "message": "77", "line": 6, "column": 18, "nodeType": "75", "messageId": "76", "endLine": 6, "endColumn": 27}, {"ruleId": "73", "severity": 1, "message": "74", "line": 5, "column": 21, "nodeType": "75", "messageId": "76", "endLine": 5, "endColumn": 33}, {"ruleId": "73", "severity": 1, "message": "77", "line": 6, "column": 18, "nodeType": "75", "messageId": "76", "endLine": 6, "endColumn": 27}, {"ruleId": "73", "severity": 1, "message": "74", "line": 5, "column": 21, "nodeType": "75", "messageId": "76", "endLine": 5, "endColumn": 33}, {"ruleId": "73", "severity": 1, "message": "78", "line": 6, "column": 22, "nodeType": "75", "messageId": "76", "endLine": 6, "endColumn": 35}, "no-unused-vars", "'setDemoArray' is assigned a value but never used.", "Identifier", "unusedVar", "'setTarget' is assigned a value but never used.", "'setWindowSize' is assigned a value but never used."]