[{"C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Patterns.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Chat.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\MainPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\History.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\CQRSPattern.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\PatternsSummary.js": "9"}, {"size": 535, "mtime": 1750832291616, "results": "10", "hashOfConfig": "11"}, {"size": 878, "mtime": 1751736419641, "results": "12", "hashOfConfig": "11"}, {"size": 362, "mtime": 1750832291751, "results": "13", "hashOfConfig": "11"}, {"size": 3161, "mtime": 1750836198440, "results": "14", "hashOfConfig": "11"}, {"size": 1636, "mtime": 1750838189679, "results": "15", "hashOfConfig": "11"}, {"size": 6498, "mtime": 1751737488976, "results": "16", "hashOfConfig": "11"}, {"size": 23077, "mtime": 1751213284498, "results": "17", "hashOfConfig": "11"}, {"size": 15631, "mtime": 1751737496966, "results": "18", "hashOfConfig": "11"}, {"size": 8993, "mtime": 1751737507447, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "222fj5", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Patterns.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\Chat.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\MainPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\History.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\CQRSPattern.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\coding\\react-app\\src\\PatternsSummary.js", [], []]